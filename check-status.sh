#!/bin/bash

# TradeAI Bot 状态检查脚本

echo "🔍 TradeAI Bot 状态检查"
echo "======================"

# 检查端口占用
echo "📡 端口状态:"
echo "  3001 (后端): $(lsof -i :3001 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
echo "  5173 (前端): $(lsof -i :5173 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"

# 检查服务状态
echo ""
echo "🔗 服务状态:"

# 检查后端
if curl -s http://localhost:3001/health >/dev/null 2>&1; then
    echo "  后端: ✅ 运行中"
    echo "    健康检查: $(curl -s http://localhost:3001/health | jq -r '.status' 2>/dev/null || echo '响应异常')"
else
    echo "  后端: ❌ 未运行"
fi

# 检查前端
if curl -s http://localhost:5173 >/dev/null 2>&1; then
    echo "  前端: ✅ 运行中"
else
    echo "  前端: ❌ 未运行"
fi

# 检查依赖
echo ""
echo "📦 依赖状态:"
echo "  后端依赖: $([ -d "node_modules" ] && echo '✅ 已安装' || echo '❌ 未安装')"
echo "  前端依赖: $([ -d "client/node_modules" ] && echo '✅ 已安装' || echo '❌ 未安装')"

# 检查关键文件
echo ""
echo "📁 文件状态:"
echo "  启动脚本: $([ -f "start.sh" ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "  后端服务器: $([ -f "src/server/index-minimal.ts" ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "  前端应用: $([ -f "client/src/App-simple.tsx" ] && echo '✅ 存在' || echo '❌ 缺失')"

# 检查进程
echo ""
echo "🔄 进程状态:"
NODE_PROCESSES=$(ps aux | grep -E "(node|ts-node)" | grep -v grep | wc -l)
echo "  Node.js 进程: $NODE_PROCESSES 个"

if [ $NODE_PROCESSES -gt 0 ]; then
    echo "  进程详情:"
    ps aux | grep -E "(node|ts-node)" | grep -v grep | while read line; do
        echo "    $line"
    done
fi

echo ""
echo "🎯 快速操作:"
echo "  启动服务: ./start.sh"
echo "  检查日志: tail -f logs/*.log"
echo "  停止服务: pkill -f 'node\|ts-node'"

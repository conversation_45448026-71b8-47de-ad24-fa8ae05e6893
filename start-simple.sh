#!/bin/bash

# TradeAI Bot 超简单启动脚本
# 解决所有依赖问题

set -e

echo "🚀 TradeAI Bot 超简单启动"
echo "=========================="

# 清理端口
echo "🧹 清理端口占用..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 启动后端
echo "🔧 启动后端服务器..."
node test-server.js &
BACKEND_PID=$!
echo "✅ 后端已启动 (PID: $BACKEND_PID)"

# 等待后端启动
sleep 3

# 验证后端
if curl -s http://localhost:3001/health > /dev/null; then
    echo "✅ 后端健康检查通过"
else
    echo "❌ 后端启动失败"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# 启动前端
echo "🎨 启动前端服务器..."
cd client

# 使用简化的App组件
if [ -f "src/App-simple.tsx" ]; then
    echo "📝 使用简化版前端组件..."
    cp src/App.tsx src/App-backup.tsx 2>/dev/null || true
    cp src/App-simple.tsx src/App.tsx
    echo "✅ 已切换到简化版组件"
else
    echo "⚠️  简化版组件不存在，使用原版"
fi

# 启动前端开发服务器
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 前端已启动 (PID: $FRONTEND_PID)"

# 显示访问信息
echo ""
echo "🎉 启动完成！"
echo "==============="
echo "📱 前端: http://localhost:5173"
echo "🔗 后端: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo ""
echo "⏳ 前端可能需要几秒钟加载..."
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待进程
wait $BACKEND_PID $FRONTEND_PID

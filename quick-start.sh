#!/bin/bash

# TradeAI Bot 快速启动脚本 (macOS)
# 解决 nodemon 崩溃和前端访问问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口并清理
cleanup_ports() {
    log_info "清理端口占用..."
    
    # 清理 3001 端口
    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 3001 被占用，正在清理..."
        lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    fi
    
    # 清理 5173 端口 (Vite 默认端口)
    if lsof -Pi :5173 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 5173 被占用，正在清理..."
        lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    fi
    
    # 清理 3000 端口
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 3000 被占用，正在清理..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    fi
    
    sleep 2
}

# 启动后端服务器
start_backend() {
    log_info "启动后端服务器..."
    
    # 使用简化的测试服务器
    if [ -f "test-server.js" ]; then
        node test-server.js &
        BACKEND_PID=$!
        log_success "后端服务器已启动 (PID: $BACKEND_PID)"
    else
        log_error "test-server.js 文件不存在"
        return 1
    fi
    
    # 等待后端启动
    sleep 3
    
    # 验证后端是否启动成功
    if curl -s http://localhost:3001/health > /dev/null; then
        log_success "后端健康检查通过"
    else
        log_error "后端启动失败"
        return 1
    fi
}

# 启动前端服务器
start_frontend() {
    log_info "启动前端服务器..."
    
    if [ ! -d "client" ]; then
        log_error "client 目录不存在"
        return 1
    fi
    
    cd client
    
    # 检查前端依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 启动前端开发服务器
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    log_success "前端服务器已启动 (PID: $FRONTEND_PID)"
    
    # 等待前端启动
    sleep 5
    
    # 检查前端是否启动成功
    if curl -s http://localhost:5173 > /dev/null; then
        log_success "前端启动成功"
    else
        log_warning "前端可能还在启动中..."
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "=========================================="
    echo "🎉 TradeAI Bot 启动成功！"
    echo "=========================================="
    echo ""
    echo "📱 前端地址: http://localhost:5173"
    echo "🔗 后端地址: http://localhost:3001"
    echo "🔍 健康检查: http://localhost:3001/health"
    echo "🧪 API测试: http://localhost:3001/api/test"
    echo ""
    echo "📋 快速测试:"
    echo "  curl http://localhost:3001/health"
    echo "  curl http://localhost:3001/api/test"
    echo ""
    echo "⚠️  注意: 前端可能需要几秒钟才能完全加载"
    echo "🛑 按 Ctrl+C 停止所有服务"
    echo ""
}

# 清理函数
cleanup() {
    echo ""
    log_info "正在停止所有服务..."
    
    # 停止后端
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_info "后端服务器已停止"
    fi
    
    # 停止前端
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_info "前端服务器已停止"
    fi
    
    # 强制清理端口
    cleanup_ports
    
    log_success "所有服务已停止"
    exit 0
}

# 主函数
main() {
    echo ""
    echo "=========================================="
    echo "   TradeAI Bot 快速启动脚本 (macOS)"
    echo "=========================================="
    echo ""
    
    # 检查基础环境
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
    
    # 清理端口
    cleanup_ports
    
    # 启动后端
    if ! start_backend; then
        log_error "后端启动失败"
        exit 1
    fi
    
    # 启动前端
    if ! start_frontend; then
        log_error "前端启动失败"
        cleanup
        exit 1
    fi
    
    # 显示访问信息
    show_access_info
    
    # 等待用户中断
    wait $BACKEND_PID $FRONTEND_PID
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 运行主函数
main "$@"

# TradeAI Bot 项目结构

## 📁 核心目录结构

```
TradeAI Bot/
├── 📁 src/                    # 后端源码
│   ├── 📁 server/            # 服务器代码
│   │   ├── index.ts          # 主服务器文件
│   │   ├── index-minimal.ts  # 简化服务器（优化版）
│   │   └── utils/            # 服务器工具
│   ├── 📁 shared/            # 共享代码
│   └── 📁 tests/             # 测试文件
│
├── 📁 client/                # 前端源码
│   ├── 📁 src/               # React 源码
│   │   ├── App.tsx           # 主应用组件
│   │   ├── App-simple.tsx    # 简化应用组件
│   │   ├── 📁 components/    # React 组件
│   │   ├── 📁 pages/         # 页面组件
│   │   └── 📁 services/      # API 服务
│   ├── package.json          # 前端依赖
│   └── vite.config.ts        # Vite 配置
│
├── 📁 docs/                  # 项目文档
├── 📁 scripts/               # 部署脚本
├── 📁 k8s/                   # Kubernetes 配置
├── 📁 nginx/                 # Nginx 配置
│
├── start.sh                  # 🚀 统一启动脚本
├── fix-main-app.sh          # 前端组件修复脚本
├── test-server.js           # 简单测试服务器
├── package.json             # 后端依赖
└── README.md                # 项目说明
```

## 🔧 优化内容

### 已清理的冗余文件
- ❌ `fix-and-start.sh`
- ❌ `start-final.sh`
- ❌ `start-main.sh`
- ❌ `start-simple.sh`
- ❌ `start-stable.sh`
- ❌ `quick-start.sh`

### 保留的核心文件
- ✅ `start.sh` - 统一启动脚本
- ✅ `fix-main-app.sh` - 前端组件修复
- ✅ `test-server.js` - 简单测试服务器

## 🚀 启动方式

### 开发环境
```bash
# 统一启动（推荐）
chmod +x start.sh
./start.sh
```

### 生产环境
```bash
# 构建并启动
npm run build
npm start
```

## 📋 解决的问题

1. ✅ **CSP 问题** - 完全禁用开发环境的内容安全策略
2. ✅ **依赖冗余** - 移除不必要的依赖和中间件
3. ✅ **文件冗余** - 清理重复的启动脚本
4. ✅ **结构混乱** - 统一项目结构和启动流程
5. ✅ **前端空白** - 使用简化组件避免依赖问题

## 🎯 核心特性

- **最小依赖**: 只使用必要的包
- **统一启动**: 一个脚本解决所有问题
- **自动恢复**: 停止时自动恢复原始文件
- **错误处理**: 完善的错误诊断和处理
- **开发友好**: 解决了所有开发环境问题

#!/bin/bash

# TradeAI Bot 主程序启动脚本
# 修复所有依赖问题并启动完整版本

set -e

echo "🚀 TradeAI Bot 主程序启动"
echo "========================="

# 清理端口
echo "🧹 清理端口占用..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 修复前端依赖
echo "🔧 修复前端依赖..."
chmod +x fix-main-app.sh
./fix-main-app.sh

# 检查是否需要安装依赖
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi

if [ ! -d "client/node_modules" ]; then
    echo "安装前端依赖..."
    cd client
    npm install
    cd ..
fi

# 启动后端 (使用 TypeScript)
echo "🔧 启动后端服务器..."
npm run dev:server &
BACKEND_PID=$!
echo "✅ 后端已启动 (PID: $BACKEND_PID)"

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 10

# 验证后端
echo "🔍 验证后端状态..."
for i in {1..10}; do
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ 后端健康检查通过"
        break
    else
        echo "⏳ 等待后端启动... ($i/10)"
        sleep 3
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 后端启动失败，查看日志:"
        echo "请检查后端日志以获取详细错误信息"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
done

# 启动前端
echo "🎨 启动前端服务器..."
cd client
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 前端已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 8

# 检查前端状态
echo "🔍 检查前端状态..."
for i in {1..5}; do
    if curl -s http://localhost:5173 > /dev/null; then
        echo "✅ 前端启动成功"
        break
    else
        echo "⏳ 等待前端启动... ($i/5)"
        sleep 3
    fi
done

# 显示访问信息
echo ""
echo "🎉 TradeAI Bot 主程序启动完成！"
echo "================================"
echo ""
echo "📱 前端地址: http://localhost:5173"
echo "🔗 后端地址: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo "📚 API文档: http://localhost:3001/api"
echo ""
echo "🌟 功能页面:"
echo "  📊 仪表板: http://localhost:5173/"
echo "  🔐 登录: http://localhost:5173/login"
echo "  📝 注册: http://localhost:5173/register"
echo "  🤖 策略: http://localhost:5173/strategies"
echo "  🏢 交易所: http://localhost:5173/exchanges"
echo "  💰 钱包: http://localhost:5173/wallets"
echo "  ⚙️  设置: http://localhost:5173/settings"
echo ""
echo "💡 提示:"
echo "  - 前端使用了完整的路由系统"
echo "  - 所有页面都有基础框架"
echo "  - 可以在各页面间导航"
echo "  - 后端API已准备就绪"
echo ""
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    # 停止后端
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo "🔧 后端服务器已停止"
    fi
    
    # 停止前端
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo "🎨 前端服务器已停止"
    fi
    
    # 强制清理端口
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待进程
wait $BACKEND_PID $FRONTEND_PID

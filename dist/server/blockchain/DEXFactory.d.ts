import { BlockchainNetwork, DEXProtocol } from '../../shared/types';
import { IDEX, DEXConfig } from './base/DEXInterface';
export declare class DEXFactory {
    private static instances;
    /**
     * 创建DEX实例
     * @param protocol DEX协议
     * @param network 区块链网络
     * @param config 配置信息
     * @returns DEX实例
     */
    static createDEX(protocol: DEXProtocol, network: BlockchainNetwork, config: DEXConfig): Promise<IDEX>;
    /**
     * 获取已存在的DEX实例
     * @param protocol DEX协议
     * @param network 区块链网络
     * @returns DEX实例或null
     */
    static getDEX(protocol: DEXProtocol, network: BlockchainNetwork): IDEX | null;
    /**
     * 移除DEX实例
     * @param protocol DEX协议
     * @param network 区块链网络
     */
    static removeDEX(protocol: DEXProtocol, network: BlockchainNetwork): Promise<void>;
    /**
     * 获取所有活跃的DEX实例
     * @returns 活跃的DEX实例数组
     */
    static getActiveDEXes(): IDEX[];
    /**
     * 断开所有DEX连接
     */
    static disconnectAll(): Promise<void>;
    /**
     * 获取支持的DEX协议列表
     * @param network 区块链网络
     * @returns 支持的DEX协议数组
     */
    static getSupportedProtocols(network: BlockchainNetwork): DEXProtocol[];
    /**
     * 检查协议是否在指定网络上受支持
     * @param protocol DEX协议
     * @param network 区块链网络
     * @returns 是否支持
     */
    static isSupported(protocol: DEXProtocol, network: BlockchainNetwork): boolean;
    /**
     * 获取DEX的默认配置
     * @param protocol DEX协议
     * @param network 区块链网络
     * @returns 默认配置
     */
    static getDefaultConfig(protocol: DEXProtocol, network: BlockchainNetwork): Partial<DEXConfig>;
    /**
     * 获取网络的默认RPC URL
     * @param network 区块链网络
     * @returns RPC URL
     */
    static getDefaultRPCUrl(network: BlockchainNetwork): string;
    /**
     * 验证DEX配置
     * @param protocol DEX协议
     * @param network 区块链网络
     * @param config 配置信息
     * @returns 验证结果
     */
    static validateConfig(protocol: DEXProtocol, network: BlockchainNetwork, config: DEXConfig): {
        valid: boolean;
        errors: string[];
    };
    /**
     * 测试DEX连接
     * @param protocol DEX协议
     * @param network 区块链网络
     * @param config 配置信息
     * @returns 测试结果
     */
    static testConnection(protocol: DEXProtocol, network: BlockchainNetwork, config: DEXConfig): Promise<{
        success: boolean;
        message: string;
        blockNumber?: number;
        networkInfo?: any;
    }>;
    /**
     * 获取系统统计信息
     * @returns 统计信息
     */
    static getSystemStats(): {
        totalConnections: number;
        protocolStats: Record<DEXProtocol, number>;
        networkStats: Record<BlockchainNetwork, number>;
    };
}
//# sourceMappingURL=DEXFactory.d.ts.map
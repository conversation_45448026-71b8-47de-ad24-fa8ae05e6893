"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniswapV2 = void 0;
const BaseDEX_1 = require("../base/BaseDEX");
const types_1 = require("../../../shared/types");
const DEXInterface_1 = require("../base/DEXInterface");
// Uniswap V2 Router ABI (简化版)
const UNISWAP_V2_ROUTER_ABI = [
    'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
    'function swapTokensForExactTokens(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
    'function addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB, uint liquidity)',
    'function removeLiquidity(address tokenA, address tokenB, uint liquidity, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB)',
    'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)',
    'function getAmountsIn(uint amountOut, address[] calldata path) external view returns (uint[] memory amounts)',
    'function quote(uint amountA, uint reserveA, uint reserveB) external pure returns (uint amountB)',
    'function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut) external pure returns (uint amountOut)',
    'function getAmountIn(uint amountOut, uint reserveIn, uint reserveOut) external pure returns (uint amountIn)'
];
// Uniswap V2 Factory ABI (简化版)
const UNISWAP_V2_FACTORY_ABI = [
    'function getPair(address tokenA, address tokenB) external view returns (address pair)',
    'function allPairs(uint) external view returns (address pair)',
    'function allPairsLength() external view returns (uint)',
    'function createPair(address tokenA, address tokenB) external returns (address pair)'
];
// ERC20 ABI (简化版)
const ERC20_ABI = [
    'function name() external view returns (string)',
    'function symbol() external view returns (string)',
    'function decimals() external view returns (uint8)',
    'function totalSupply() external view returns (uint256)',
    'function balanceOf(address owner) external view returns (uint256)',
    'function transfer(address to, uint256 value) external returns (bool)',
    'function approve(address spender, uint256 value) external returns (bool)',
    'function allowance(address owner, address spender) external view returns (uint256)'
];
// Uniswap V2 Pair ABI (简化版)
const UNISWAP_V2_PAIR_ABI = [
    'function token0() external view returns (address)',
    'function token1() external view returns (address)',
    'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
    'function totalSupply() external view returns (uint)',
    'function balanceOf(address owner) external view returns (uint)',
    'event Swap(address indexed sender, uint amount0In, uint amount1In, uint amount0Out, uint amount1Out, address indexed to)',
    'event Mint(address indexed sender, uint amount0, uint amount1)',
    'event Burn(address indexed sender, uint amount0, uint amount1, address indexed to)'
];
class UniswapV2 extends BaseDEX_1.BaseDEX {
    constructor(network) {
        super(types_1.DEXProtocol.UNISWAP_V2, network);
    }
    getRouterABI() {
        return UNISWAP_V2_ROUTER_ABI;
    }
    getFactoryABI() {
        return UNISWAP_V2_FACTORY_ABI;
    }
    getERC20ABI() {
        return ERC20_ABI;
    }
    getPairABI() {
        return UNISWAP_V2_PAIR_ABI;
    }
    // 获取所有代币（这里返回常用代币列表）
    async getTokens() {
        const commonTokens = {
            [types_1.BlockchainNetwork.ETH]: [
                {
                    address: '******************************************',
                    symbol: 'WETH',
                    name: 'Wrapped Ether',
                    decimals: 18
                },
                {
                    address: '******************************************',
                    symbol: 'USDC',
                    name: 'USD Coin',
                    decimals: 6
                },
                {
                    address: '******************************************',
                    symbol: 'USDT',
                    name: 'Tether USD',
                    decimals: 6
                }
            ],
            [types_1.BlockchainNetwork.BSC]: [
                {
                    address: '******************************************',
                    symbol: 'WBNB',
                    name: 'Wrapped BNB',
                    decimals: 18
                },
                {
                    address: '******************************************',
                    symbol: 'USDC',
                    name: 'USD Coin',
                    decimals: 18
                },
                {
                    address: '******************************************',
                    symbol: 'USDT',
                    name: 'Tether USD',
                    decimals: 18
                }
            ],
            [types_1.BlockchainNetwork.BASE]: [
                {
                    address: '******************************************',
                    symbol: 'WETH',
                    name: 'Wrapped Ether',
                    decimals: 18
                },
                {
                    address: '******************************************',
                    symbol: 'USDC',
                    name: 'USD Coin',
                    decimals: 6
                }
            ],
            [types_1.BlockchainNetwork.SOLANA]: [] // Solana 不使用 Uniswap V2
        };
        return commonTokens[this.network] || [];
    }
    // 获取所有交易对
    async getPairs() {
        try {
            const pairsLength = await this.factoryContract.allPairsLength();
            const pairs = [];
            // 限制获取的交易对数量以避免过多的RPC调用
            const maxPairs = Math.min(Number(pairsLength), 100);
            for (let i = 0; i < maxPairs; i++) {
                try {
                    const pairAddress = await this.factoryContract.allPairs(i);
                    const pairContract = new (await Promise.resolve().then(() => __importStar(require('ethers')))).ethers.Contract(pairAddress, this.getPairABI(), this.provider);
                    const [token0Address, token1Address, reserves] = await Promise.all([
                        pairContract.token0(),
                        pairContract.token1(),
                        pairContract.getReserves()
                    ]);
                    const [token0, token1] = await Promise.all([
                        this.getToken(token0Address),
                        this.getToken(token1Address)
                    ]);
                    pairs.push({
                        address: pairAddress,
                        token0,
                        token1,
                        reserve0: reserves[0].toString(),
                        reserve1: reserves[1].toString()
                    });
                }
                catch (error) {
                    // 跳过获取失败的交易对
                    continue;
                }
            }
            return pairs;
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get pairs: ${error.message}`, this.protocol, this.network);
        }
    }
    // 获取交易路径
    async getRoute(tokenIn, tokenOut, amountIn) {
        try {
            const path = [tokenIn, tokenOut];
            const amounts = await this.routerContract.getAmountsOut(amountIn, path);
            // 获取交易对信息
            const pair = await this.getPair(tokenIn, tokenOut);
            if (!pair) {
                throw new DEXInterface_1.DEXError('No pair found for the given tokens', this.protocol, this.network);
            }
            // 计算价格影响
            const priceImpact = this.calculatePriceImpact(amountIn, amounts[1].toString(), pair.reserve0, pair.reserve1);
            return {
                path,
                pairs: [pair],
                amountIn,
                amountOut: amounts[1].toString(),
                priceImpact,
                fee: 0.003 // Uniswap V2 固定费率 0.3%
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get route: ${error.message}`, this.protocol, this.network);
        }
    }
    // 获取最佳路径（对于 Uniswap V2，通常是直接路径或通过 WETH 的路径）
    async getBestRoute(tokenIn, tokenOut, amountIn) {
        try {
            // 首先尝试直接路径
            try {
                return await this.getRoute(tokenIn, tokenOut, amountIn);
            }
            catch (error) {
                // 如果直接路径失败，尝试通过 WETH 的路径
                const wethAddress = this.getWETHAddress();
                if (tokenIn !== wethAddress && tokenOut !== wethAddress) {
                    const pathThroughWETH = [tokenIn, wethAddress, tokenOut];
                    const amounts = await this.routerContract.getAmountsOut(amountIn, pathThroughWETH);
                    // 获取交易对信息
                    const [pair1, pair2] = await Promise.all([
                        this.getPair(tokenIn, wethAddress),
                        this.getPair(wethAddress, tokenOut)
                    ]);
                    if (!pair1 || !pair2) {
                        throw new DEXInterface_1.DEXError('No route found through WETH', this.protocol, this.network);
                    }
                    // 计算总价格影响
                    const priceImpact1 = this.calculatePriceImpact(amountIn, amounts[1].toString(), pair1.reserve0, pair1.reserve1);
                    const priceImpact2 = this.calculatePriceImpact(amounts[1].toString(), amounts[2].toString(), pair2.reserve0, pair2.reserve1);
                    const totalPriceImpact = priceImpact1 + priceImpact2;
                    return {
                        path: pathThroughWETH,
                        pairs: [pair1, pair2],
                        amountIn,
                        amountOut: amounts[2].toString(),
                        priceImpact: totalPriceImpact,
                        fee: 0.006 // 两次交易，每次 0.3%
                    };
                }
                throw error;
            }
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get best route: ${error.message}`, this.protocol, this.network);
        }
    }
    // 计算价格影响
    calculatePriceImpact(amountIn, amountOut, reserveIn, reserveOut) {
        const amountInBig = BigInt(amountIn);
        const amountOutBig = BigInt(amountOut);
        const reserveInBig = BigInt(reserveIn);
        const reserveOutBig = BigInt(reserveOut);
        // 计算理论价格（不考虑滑点）
        const theoreticalAmountOut = (amountInBig * reserveOutBig) / reserveInBig;
        // 计算价格影响百分比
        const priceImpact = Number(((theoreticalAmountOut - amountOutBig) * 10000n) / theoreticalAmountOut) / 100;
        return Math.max(0, priceImpact);
    }
    // 获取 WETH 地址
    getWETHAddress() {
        const wethAddresses = {
            [types_1.BlockchainNetwork.ETH]: '******************************************',
            [types_1.BlockchainNetwork.BSC]: '******************************************',
            [types_1.BlockchainNetwork.BASE]: '******************************************',
            [types_1.BlockchainNetwork.SOLANA]: '' // Solana 不适用
        };
        return wethAddresses[this.network] || '';
    }
}
exports.UniswapV2 = UniswapV2;
//# sourceMappingURL=UniswapV2.js.map
import { BaseDEX } from '../base/BaseDEX';
import { BlockchainNetwork } from '../../../shared/types';
import { DEXToken, DEXPair, DEXRoute } from '../base/DEXInterface';
export declare class UniswapV2 extends BaseDEX {
    constructor(network: BlockchainNetwork);
    protected getRouterABI(): any[];
    protected getFactoryABI(): any[];
    protected getERC20ABI(): any[];
    protected getPairABI(): any[];
    getTokens(): Promise<DEXToken[]>;
    getPairs(): Promise<DEXPair[]>;
    getRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
    getBestRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
    private calculatePriceImpact;
    private getWETHAddress;
}
//# sourceMappingURL=UniswapV2.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEXFactory = void 0;
const types_1 = require("../../shared/types");
const UniswapV2_1 = require("./uniswap/UniswapV2");
const PancakeSwap_1 = require("./pancakeswap/PancakeSwap");
// 其他DEX的导入将在后续添加
// import { UniswapV3 } from './uniswap/UniswapV3';
// import { Raydium } from './raydium/Raydium';
// import { Jupiter } from './jupiter/Jupiter';
class DEXFactory {
    /**
     * 创建DEX实例
     * @param protocol DEX协议
     * @param network 区块链网络
     * @param config 配置信息
     * @returns DEX实例
     */
    static async createDEX(protocol, network, config) {
        const key = `${protocol}_${network}`;
        // 检查是否已存在实例
        if (this.instances.has(key)) {
            const instance = this.instances.get(key);
            if (instance.isConnected) {
                return instance;
            }
        }
        // 创建新实例
        let dexInstance;
        switch (protocol) {
            case types_1.DEXProtocol.UNISWAP_V2:
                if (network === types_1.BlockchainNetwork.ETH) {
                    dexInstance = new UniswapV2_1.UniswapV2(network);
                }
                else {
                    throw new Error(`Uniswap V2 is not supported on ${network}`);
                }
                break;
            // case DEXProtocol.UNISWAP_V3:
            //   if ([BlockchainNetwork.ETH, BlockchainNetwork.BASE].includes(network)) {
            //     dexInstance = new UniswapV3(network);
            //   } else {
            //     throw new Error(`Uniswap V3 is not supported on ${network}`);
            //   }
            //   break;
            case types_1.DEXProtocol.PANCAKESWAP:
                if (network === types_1.BlockchainNetwork.BSC) {
                    dexInstance = new PancakeSwap_1.PancakeSwap();
                }
                else {
                    throw new Error(`PancakeSwap is not supported on ${network}`);
                }
                break;
            // case DEXProtocol.RAYDIUM:
            //   if (network === BlockchainNetwork.SOLANA) {
            //     dexInstance = new Raydium();
            //   } else {
            //     throw new Error(`Raydium is not supported on ${network}`);
            //   }
            //   break;
            // case DEXProtocol.JUPITER:
            //   if (network === BlockchainNetwork.SOLANA) {
            //     dexInstance = new Jupiter();
            //   } else {
            //     throw new Error(`Jupiter is not supported on ${network}`);
            //   }
            //   break;
            default:
                throw new Error(`Unsupported DEX protocol: ${protocol}`);
        }
        // 初始化并连接
        await dexInstance.initialize(config);
        await dexInstance.connect();
        // 缓存实例
        this.instances.set(key, dexInstance);
        return dexInstance;
    }
    /**
     * 获取已存在的DEX实例
     * @param protocol DEX协议
     * @param network 区块链网络
     * @returns DEX实例或null
     */
    static getDEX(protocol, network) {
        const key = `${protocol}_${network}`;
        return this.instances.get(key) || null;
    }
    /**
     * 移除DEX实例
     * @param protocol DEX协议
     * @param network 区块链网络
     */
    static async removeDEX(protocol, network) {
        const key = `${protocol}_${network}`;
        const instance = this.instances.get(key);
        if (instance) {
            await instance.disconnect();
            this.instances.delete(key);
        }
    }
    /**
     * 获取所有活跃的DEX实例
     * @returns 活跃的DEX实例数组
     */
    static getActiveDEXes() {
        return Array.from(this.instances.values()).filter(dex => dex.isConnected);
    }
    /**
     * 断开所有DEX连接
     */
    static async disconnectAll() {
        const disconnectPromises = Array.from(this.instances.values()).map(dex => dex.disconnect());
        await Promise.all(disconnectPromises);
        this.instances.clear();
    }
    /**
     * 获取支持的DEX协议列表
     * @param network 区块链网络
     * @returns 支持的DEX协议数组
     */
    static getSupportedProtocols(network) {
        const supportedProtocols = {
            [types_1.BlockchainNetwork.ETH]: [
                types_1.DEXProtocol.UNISWAP_V2,
                // DEXProtocol.UNISWAP_V3
            ],
            [types_1.BlockchainNetwork.BSC]: [
                types_1.DEXProtocol.PANCAKESWAP,
                types_1.DEXProtocol.UNISWAP_V2 // PancakeSwap 基于 Uniswap V2
            ],
            [types_1.BlockchainNetwork.BASE]: [
            // DEXProtocol.UNISWAP_V3
            ],
            [types_1.BlockchainNetwork.SOLANA]: [
            // DEXProtocol.RAYDIUM,
            // DEXProtocol.JUPITER
            ]
        };
        return supportedProtocols[network] || [];
    }
    /**
     * 检查协议是否在指定网络上受支持
     * @param protocol DEX协议
     * @param network 区块链网络
     * @returns 是否支持
     */
    static isSupported(protocol, network) {
        return this.getSupportedProtocols(network).includes(protocol);
    }
    /**
     * 获取DEX的默认配置
     * @param protocol DEX协议
     * @param network 区块链网络
     * @returns 默认配置
     */
    static getDefaultConfig(protocol, network) {
        const configs = {
            [`${types_1.DEXProtocol.UNISWAP_V2}_${types_1.BlockchainNetwork.ETH}`]: {
                routerAddress: '******************************************',
                factoryAddress: '******************************************',
                gasLimit: 300000,
                slippage: 0.5
            },
            [`${types_1.DEXProtocol.PANCAKESWAP}_${types_1.BlockchainNetwork.BSC}`]: {
                routerAddress: '******************************************',
                factoryAddress: '******************************************',
                gasLimit: 300000,
                slippage: 0.5
            },
            // [`${DEXProtocol.UNISWAP_V3}_${BlockchainNetwork.ETH}`]: {
            //   routerAddress: '******************************************',
            //   factoryAddress: '******************************************',
            //   gasLimit: 500000,
            //   slippage: 0.5
            // },
            // [`${DEXProtocol.UNISWAP_V3}_${BlockchainNetwork.BASE}`]: {
            //   routerAddress: '******************************************',
            //   factoryAddress: '******************************************',
            //   gasLimit: 500000,
            //   slippage: 0.5
            // }
        };
        const key = `${protocol}_${network}`;
        return configs[key] || {};
    }
    /**
     * 获取网络的默认RPC URL
     * @param network 区块链网络
     * @returns RPC URL
     */
    static getDefaultRPCUrl(network) {
        const rpcUrls = {
            [types_1.BlockchainNetwork.ETH]: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
            [types_1.BlockchainNetwork.BSC]: 'https://bsc-dataseed.binance.org/',
            [types_1.BlockchainNetwork.BASE]: 'https://mainnet.base.org',
            [types_1.BlockchainNetwork.SOLANA]: 'https://api.mainnet-beta.solana.com'
        };
        return rpcUrls[network] || '';
    }
    /**
     * 验证DEX配置
     * @param protocol DEX协议
     * @param network 区块链网络
     * @param config 配置信息
     * @returns 验证结果
     */
    static validateConfig(protocol, network, config) {
        const errors = [];
        // 基础验证
        if (!config.rpcUrl) {
            errors.push('RPC URL is required');
        }
        if (!config.routerAddress) {
            errors.push('Router address is required');
        }
        if (!config.factoryAddress) {
            errors.push('Factory address is required');
        }
        // 网络特定验证
        if (!this.isSupported(protocol, network)) {
            errors.push(`${protocol} is not supported on ${network}`);
        }
        // 地址格式验证（简化版）
        const addressRegex = /^0x[a-fA-F0-9]{40}$/;
        if (config.routerAddress && !addressRegex.test(config.routerAddress)) {
            errors.push('Invalid router address format');
        }
        if (config.factoryAddress && !addressRegex.test(config.factoryAddress)) {
            errors.push('Invalid factory address format');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    /**
     * 测试DEX连接
     * @param protocol DEX协议
     * @param network 区块链网络
     * @param config 配置信息
     * @returns 测试结果
     */
    static async testConnection(protocol, network, config) {
        try {
            // 验证配置
            const validation = this.validateConfig(protocol, network, config);
            if (!validation.valid) {
                return {
                    success: false,
                    message: `Configuration error: ${validation.errors.join(', ')}`
                };
            }
            // 创建临时实例进行测试
            const tempInstance = await this.createDEX(protocol, network, config);
            // 获取网络信息
            const tokens = await tempInstance.getTokens();
            // 测试完成后断开连接
            await tempInstance.disconnect();
            return {
                success: true,
                message: 'Connection successful',
                networkInfo: {
                    protocol,
                    network,
                    tokensCount: tokens.length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * 获取系统统计信息
     * @returns 统计信息
     */
    static getSystemStats() {
        let totalConnections = 0;
        const protocolStats = {};
        const networkStats = {};
        // 初始化统计
        Object.values(types_1.DEXProtocol).forEach(protocol => {
            protocolStats[protocol] = 0;
        });
        Object.values(types_1.BlockchainNetwork).forEach(network => {
            networkStats[network] = 0;
        });
        // 统计连接数
        for (const instance of this.instances.values()) {
            if (instance.isConnected) {
                totalConnections++;
                protocolStats[instance.protocol]++;
                networkStats[instance.network]++;
            }
        }
        return {
            totalConnections,
            protocolStats,
            networkStats
        };
    }
}
exports.DEXFactory = DEXFactory;
DEXFactory.instances = new Map();
//# sourceMappingURL=DEXFactory.js.map
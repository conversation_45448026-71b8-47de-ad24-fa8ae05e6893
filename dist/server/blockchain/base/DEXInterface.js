"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GasError = exports.SlippageError = exports.InsufficientBalanceError = exports.LiquidityError = exports.SwapError = exports.BlockchainNetworkError = exports.DEXError = void 0;
// DEX 错误类
class DEXError extends Error {
    constructor(message, protocol, network, code) {
        super(message);
        this.protocol = protocol;
        this.network = network;
        this.code = code;
        this.name = 'DEXError';
    }
}
exports.DEXError = DEXError;
// 网络错误
class BlockchainNetworkError extends DEXError {
    constructor(message, protocol, network) {
        super(message, protocol, network, 'NETWORK_ERROR');
        this.name = 'BlockchainNetworkError';
    }
}
exports.BlockchainNetworkError = BlockchainNetworkError;
// 交易错误
class SwapError extends DEXError {
    constructor(message, protocol, network) {
        super(message, protocol, network, 'SWAP_ERROR');
        this.name = 'SwapError';
    }
}
exports.SwapError = SwapError;
// 流动性错误
class LiquidityError extends DEXError {
    constructor(message, protocol, network) {
        super(message, protocol, network, 'LIQUIDITY_ERROR');
        this.name = 'LiquidityError';
    }
}
exports.LiquidityError = LiquidityError;
// 余额不足错误
class InsufficientBalanceError extends DEXError {
    constructor(message, protocol, network) {
        super(message, protocol, network, 'INSUFFICIENT_BALANCE');
        this.name = 'InsufficientBalanceError';
    }
}
exports.InsufficientBalanceError = InsufficientBalanceError;
// 滑点过大错误
class SlippageError extends DEXError {
    constructor(message, protocol, network) {
        super(message, protocol, network, 'SLIPPAGE_ERROR');
        this.name = 'SlippageError';
    }
}
exports.SlippageError = SlippageError;
// Gas 费用错误
class GasError extends DEXError {
    constructor(message, protocol, network) {
        super(message, protocol, network, 'GAS_ERROR');
        this.name = 'GasError';
    }
}
exports.GasError = GasError;
//# sourceMappingURL=DEXInterface.js.map
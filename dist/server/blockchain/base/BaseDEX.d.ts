import { ethers } from 'ethers';
import { EventEmitter } from 'events';
import { BlockchainNetwork, DEXProtocol } from '../../../shared/types';
import { IDEX, DEXConfig, DEXToken, DEXPair, DEXRoute, DEXPrice, DEXSwapParams, DEXSwapResult, DEXLiquidityParams, DEXLiquidityResult, WalletConfig } from './DEXInterface';
export declare abstract class BaseDEX extends EventEmitter implements IDEX {
    readonly protocol: DEXProtocol;
    readonly network: BlockchainNetwork;
    protected config: DEXConfig;
    protected provider: ethers.JsonRpcProvider;
    protected routerContract: ethers.Contract;
    protected factoryContract: ethers.Contract;
    protected _isConnected: boolean;
    constructor(protocol: DEXProtocol, network: BlockchainNetwork);
    get isConnected(): boolean;
    protected abstract getRouterABI(): any[];
    protected abstract getFactoryABI(): any[];
    protected abstract getERC20ABI(): any[];
    protected abstract getPairABI(): any[];
    initialize(config: DEXConfig): Promise<void>;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getToken(address: string): Promise<DEXToken>;
    getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string>;
    getPair(tokenA: string, tokenB: string): Promise<DEXPair | null>;
    getReserves(pairAddress: string): Promise<{
        reserve0: string;
        reserve1: string;
    }>;
    getPrice(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXPrice>;
    swap(params: DEXSwapParams, wallet: WalletConfig): Promise<DEXSwapResult>;
    swapExactTokensForTokens(amountIn: string, amountOutMin: string, path: string[], to: string, deadline: number, wallet: WalletConfig): Promise<DEXSwapResult>;
    addLiquidity(params: DEXLiquidityParams, wallet: WalletConfig): Promise<DEXLiquidityResult>;
    removeLiquidity(tokenA: string, tokenB: string, liquidity: string, amountAMin: string, amountBMin: string, to: string, deadline: number, wallet: WalletConfig): Promise<DEXLiquidityResult>;
    formatUnits(value: string, decimals: number): string;
    parseUnits(value: string, decimals: number): string;
    calculateSlippage(amountOut: string, slippage: number): string;
    estimateGas(transaction: any): Promise<string>;
    abstract getTokens(): Promise<DEXToken[]>;
    abstract getPairs(): Promise<DEXPair[]>;
    abstract getRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
    abstract getBestRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
}
//# sourceMappingURL=BaseDEX.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDEX = void 0;
const ethers_1 = require("ethers");
const events_1 = require("events");
const logger_1 = require("../../utils/logger");
const DEXInterface_1 = require("./DEXInterface");
class BaseDEX extends events_1.EventEmitter {
    constructor(protocol, network) {
        super();
        this._isConnected = false;
        this.protocol = protocol;
        this.network = network;
    }
    get isConnected() {
        return this._isConnected;
    }
    // 初始化DEX
    async initialize(config) {
        this.config = config;
        try {
            // 创建提供者
            this.provider = new ethers_1.ethers.JsonRpcProvider(config.rpcUrl);
            // 创建合约实例
            this.routerContract = new ethers_1.ethers.Contract(config.routerAddress, this.getRouterABI(), this.provider);
            this.factoryContract = new ethers_1.ethers.Contract(config.factoryAddress, this.getFactoryABI(), this.provider);
            logger_1.logger.info(`${this.protocol} on ${this.network} initialized`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to initialize ${this.protocol}:`, error);
            throw new DEXInterface_1.DEXError(`Initialization failed: ${error.message}`, this.protocol, this.network);
        }
    }
    // 连接到区块链网络
    async connect() {
        try {
            // 测试网络连接
            const network = await this.provider.getNetwork();
            const blockNumber = await this.provider.getBlockNumber();
            this._isConnected = true;
            this.emit('connected');
            logger_1.logger.info(`Connected to ${this.protocol} on ${this.network}, block: ${blockNumber}`);
        }
        catch (error) {
            this._isConnected = false;
            this.emit('error', error);
            throw new DEXInterface_1.BlockchainNetworkError(`Connection failed: ${error.message}`, this.protocol, this.network);
        }
    }
    // 断开连接
    async disconnect() {
        this._isConnected = false;
        this.emit('disconnected');
        logger_1.logger.info(`Disconnected from ${this.protocol} on ${this.network}`);
    }
    // 获取代币信息
    async getToken(address) {
        try {
            const tokenContract = new ethers_1.ethers.Contract(address, this.getERC20ABI(), this.provider);
            const [symbol, name, decimals] = await Promise.all([
                tokenContract.symbol(),
                tokenContract.name(),
                tokenContract.decimals()
            ]);
            return {
                address,
                symbol,
                name,
                decimals: Number(decimals)
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get token info: ${error.message}`, this.protocol, this.network);
        }
    }
    // 获取代币余额
    async getTokenBalance(tokenAddress, walletAddress) {
        try {
            if (tokenAddress === ethers_1.ethers.ZeroAddress) {
                // 原生代币余额
                const balance = await this.provider.getBalance(walletAddress);
                return balance.toString();
            }
            else {
                // ERC20代币余额
                const tokenContract = new ethers_1.ethers.Contract(tokenAddress, this.getERC20ABI(), this.provider);
                const balance = await tokenContract.balanceOf(walletAddress);
                return balance.toString();
            }
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get token balance: ${error.message}`, this.protocol, this.network);
        }
    }
    // 获取交易对信息
    async getPair(tokenA, tokenB) {
        try {
            const pairAddress = await this.factoryContract.getPair(tokenA, tokenB);
            if (pairAddress === ethers_1.ethers.ZeroAddress) {
                return null;
            }
            const pairContract = new ethers_1.ethers.Contract(pairAddress, this.getPairABI(), this.provider);
            const [token0Address, token1Address, reserves] = await Promise.all([
                pairContract.token0(),
                pairContract.token1(),
                pairContract.getReserves()
            ]);
            const [token0, token1] = await Promise.all([
                this.getToken(token0Address),
                this.getToken(token1Address)
            ]);
            return {
                address: pairAddress,
                token0,
                token1,
                reserve0: reserves[0].toString(),
                reserve1: reserves[1].toString()
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get pair info: ${error.message}`, this.protocol, this.network);
        }
    }
    // 获取储备量
    async getReserves(pairAddress) {
        try {
            const pairContract = new ethers_1.ethers.Contract(pairAddress, this.getPairABI(), this.provider);
            const reserves = await pairContract.getReserves();
            return {
                reserve0: reserves[0].toString(),
                reserve1: reserves[1].toString()
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get reserves: ${error.message}`, this.protocol, this.network);
        }
    }
    // 获取价格
    async getPrice(tokenIn, tokenOut, amountIn) {
        try {
            const route = await this.getRoute(tokenIn, tokenOut, amountIn);
            return {
                tokenIn,
                tokenOut,
                amountIn,
                amountOut: route.amountOut,
                priceImpact: route.priceImpact,
                route
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Failed to get price: ${error.message}`, this.protocol, this.network);
        }
    }
    // 执行交换
    async swap(params, wallet) {
        try {
            const signer = new ethers_1.ethers.Wallet(wallet.privateKey, this.provider);
            const routerWithSigner = this.routerContract.connect(signer);
            // 构建交易路径
            const path = [params.tokenIn, params.tokenOut];
            // 估算Gas
            const gasEstimate = await routerWithSigner.swapExactTokensForTokens.estimateGas(params.amountIn, params.amountOutMin, path, params.to, params.deadline);
            // 执行交易
            const tx = await routerWithSigner.swapExactTokensForTokens(params.amountIn, params.amountOutMin, path, params.to, params.deadline, {
                gasLimit: gasEstimate,
                gasPrice: this.config.gasPrice
            });
            const receipt = await tx.wait();
            return {
                txHash: receipt.hash,
                amountIn: params.amountIn,
                amountOut: params.amountOutMin, // 实际输出需要从事件中解析
                gasUsed: receipt.gasUsed.toString(),
                gasPrice: receipt.gasPrice?.toString() || '0',
                fee: (receipt.gasUsed * (receipt.gasPrice || 0n)).toString()
            };
        }
        catch (error) {
            throw new DEXInterface_1.SwapError(`Swap failed: ${error.message}`, this.protocol, this.network);
        }
    }
    // 精确代币交换
    async swapExactTokensForTokens(amountIn, amountOutMin, path, to, deadline, wallet) {
        return this.swap({
            tokenIn: path[0],
            tokenOut: path[path.length - 1],
            amountIn,
            amountOutMin,
            to,
            deadline,
            slippage: this.config.slippage || 0.5
        }, wallet);
    }
    // 添加流动性
    async addLiquidity(params, wallet) {
        try {
            const signer = new ethers_1.ethers.Wallet(wallet.privateKey, this.provider);
            const routerWithSigner = this.routerContract.connect(signer);
            const tx = await routerWithSigner.addLiquidity(params.tokenA, params.tokenB, params.amountADesired, params.amountBDesired, params.amountAMin, params.amountBMin, params.to, params.deadline);
            const receipt = await tx.wait();
            return {
                txHash: receipt.hash,
                amountA: params.amountADesired,
                amountB: params.amountBDesired,
                liquidity: '0', // 需要从事件中解析
                gasUsed: receipt.gasUsed.toString(),
                gasPrice: receipt.gasPrice?.toString() || '0'
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Add liquidity failed: ${error.message}`, this.protocol, this.network);
        }
    }
    // 移除流动性
    async removeLiquidity(tokenA, tokenB, liquidity, amountAMin, amountBMin, to, deadline, wallet) {
        try {
            const signer = new ethers_1.ethers.Wallet(wallet.privateKey, this.provider);
            const routerWithSigner = this.routerContract.connect(signer);
            const tx = await routerWithSigner.removeLiquidity(tokenA, tokenB, liquidity, amountAMin, amountBMin, to, deadline);
            const receipt = await tx.wait();
            return {
                txHash: receipt.hash,
                amountA: amountAMin,
                amountB: amountBMin,
                liquidity,
                gasUsed: receipt.gasUsed.toString(),
                gasPrice: receipt.gasPrice?.toString() || '0'
            };
        }
        catch (error) {
            throw new DEXInterface_1.DEXError(`Remove liquidity failed: ${error.message}`, this.protocol, this.network);
        }
    }
    // 格式化单位
    formatUnits(value, decimals) {
        return ethers_1.ethers.formatUnits(value, decimals);
    }
    // 解析单位
    parseUnits(value, decimals) {
        return ethers_1.ethers.parseUnits(value, decimals).toString();
    }
    // 计算滑点
    calculateSlippage(amountOut, slippage) {
        const slippageAmount = (BigInt(amountOut) * BigInt(Math.floor(slippage * 100))) / 10000n;
        return (BigInt(amountOut) - slippageAmount).toString();
    }
    // 估算Gas费用
    async estimateGas(transaction) {
        try {
            const gasEstimate = await this.provider.estimateGas(transaction);
            return gasEstimate.toString();
        }
        catch (error) {
            throw new DEXInterface_1.GasError(`Gas estimation failed: ${error.message}`, this.protocol, this.network);
        }
    }
}
exports.BaseDEX = BaseDEX;
//# sourceMappingURL=BaseDEX.js.map
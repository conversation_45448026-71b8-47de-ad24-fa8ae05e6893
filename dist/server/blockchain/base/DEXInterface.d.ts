import { BlockchainNetwork, DEXProtocol } from '../../../shared/types';
export interface DEXPair {
    address: string;
    token0: DEXToken;
    token1: DEXToken;
    fee?: number;
    liquidity?: string;
    reserve0?: string;
    reserve1?: string;
}
export interface DEXToken {
    address: string;
    symbol: string;
    name: string;
    decimals: number;
    logoURI?: string;
}
export interface DEXRoute {
    path: string[];
    pairs: DEXPair[];
    amountIn: string;
    amountOut: string;
    priceImpact: number;
    fee: number;
}
export interface DEXSwapParams {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    amountOutMin: string;
    to: string;
    deadline: number;
    slippage: number;
}
export interface DEXLiquidityParams {
    tokenA: string;
    tokenB: string;
    amountADesired: string;
    amountBDesired: string;
    amountAMin: string;
    amountBMin: string;
    to: string;
    deadline: number;
}
export interface DEXSwapResult {
    txHash: string;
    amountIn: string;
    amountOut: string;
    gasUsed: string;
    gasPrice: string;
    fee: string;
}
export interface DEXLiquidityResult {
    txHash: string;
    amountA: string;
    amountB: string;
    liquidity: string;
    gasUsed: string;
    gasPrice: string;
}
export interface DEXPrice {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    amountOut: string;
    priceImpact: number;
    route: DEXRoute;
}
export interface WalletConfig {
    privateKey: string;
    address: string;
}
export interface DEXConfig {
    network: BlockchainNetwork;
    rpcUrl: string;
    routerAddress: string;
    factoryAddress: string;
    gasLimit?: number;
    gasPrice?: string;
    slippage?: number;
}
export interface IDEX {
    readonly protocol: DEXProtocol;
    readonly network: BlockchainNetwork;
    readonly isConnected: boolean;
    initialize(config: DEXConfig): Promise<void>;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getToken(address: string): Promise<DEXToken>;
    getTokens(): Promise<DEXToken[]>;
    getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string>;
    getPair(tokenA: string, tokenB: string): Promise<DEXPair | null>;
    getPairs(): Promise<DEXPair[]>;
    getReserves(pairAddress: string): Promise<{
        reserve0: string;
        reserve1: string;
    }>;
    getPrice(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXPrice>;
    getRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
    getBestRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
    swap(params: DEXSwapParams, wallet: WalletConfig): Promise<DEXSwapResult>;
    swapExactTokensForTokens(amountIn: string, amountOutMin: string, path: string[], to: string, deadline: number, wallet: WalletConfig): Promise<DEXSwapResult>;
    addLiquidity(params: DEXLiquidityParams, wallet: WalletConfig): Promise<DEXLiquidityResult>;
    removeLiquidity(tokenA: string, tokenB: string, liquidity: string, amountAMin: string, amountBMin: string, to: string, deadline: number, wallet: WalletConfig): Promise<DEXLiquidityResult>;
    formatUnits(value: string, decimals: number): string;
    parseUnits(value: string, decimals: number): string;
    calculateSlippage(amountOut: string, slippage: number): string;
    estimateGas(transaction: any): Promise<string>;
    onSwap?(callback: (event: any) => void): void;
    onLiquidityAdd?(callback: (event: any) => void): void;
    onLiquidityRemove?(callback: (event: any) => void): void;
}
export declare class DEXError extends Error {
    protocol: DEXProtocol;
    network: BlockchainNetwork;
    code?: string | undefined;
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork, code?: string | undefined);
}
export declare class BlockchainNetworkError extends DEXError {
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork);
}
export declare class SwapError extends DEXError {
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork);
}
export declare class LiquidityError extends DEXError {
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork);
}
export declare class InsufficientBalanceError extends DEXError {
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork);
}
export declare class SlippageError extends DEXError {
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork);
}
export declare class GasError extends DEXError {
    constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork);
}
//# sourceMappingURL=DEXInterface.d.ts.map
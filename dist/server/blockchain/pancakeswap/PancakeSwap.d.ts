import { UniswapV2 } from '../uniswap/UniswapV2';
import { DEXToken } from '../base/DEXInterface';
export declare class PancakeSwap extends UniswapV2 {
    constructor();
    getTokens(): Promise<DEXToken[]>;
    protected getWETHAddress(): string;
    getCakePrice(): Promise<string>;
    getBNBPrice(): Promise<string>;
    getFarmInfo(farmId: number): Promise<{
        farmId: number;
        lpToken: string;
        allocPoint: number;
        lastRewardBlock: number;
        accCakePerShare: string;
        totalStaked: string;
        apr: number;
    }>;
    getPoolInfo(poolId: number): Promise<{
        poolId: number;
        stakingToken: string;
        rewardToken: string;
        totalStaked: string;
        rewardPerBlock: string;
        apr: number;
    }>;
    getLotteryInfo(): Promise<{
        currentLotteryId: number;
        status: string;
        prizePool: string;
        ticketPrice: string;
        endTime: number;
    }>;
    getPredictionInfo(): Promise<{
        currentEpoch: number;
        bullAmount: string;
        bearAmount: string;
        rewardBaseCalAmount: string;
        rewardAmount: string;
        lockPrice: string;
        closePrice: string;
    }>;
}
//# sourceMappingURL=PancakeSwap.d.ts.map
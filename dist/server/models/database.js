"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = void 0;
const pg_1 = require("pg");
const redis_1 = require("redis");
const logger_1 = require("../utils/logger");
// PostgreSQL 连接池
class DatabaseManager {
    constructor() {
        this.pool = new pg_1.Pool({
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '5432'),
            database: process.env.DB_NAME || 'tradeai_bot',
            user: process.env.DB_USER || 'postgres',
            password: process.env.DB_PASSWORD || '',
            max: 20, // 最大连接数
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
        this.redisClient = (0, redis_1.createClient)({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_PASSWORD || undefined,
        });
        this.initializeDatabase();
    }
    async initializeDatabase() {
        try {
            // 测试 PostgreSQL 连接
            const client = await this.pool.connect();
            await client.query('SELECT NOW()');
            client.release();
            logger_1.logger.info('PostgreSQL connected successfully');
            // 测试 Redis 连接
            await this.redisClient.connect();
            logger_1.logger.info('Redis connected successfully');
            // 创建数据库表
            await this.createTables();
        }
        catch (error) {
            logger_1.logger.error('Database initialization failed:', error);
            throw error;
        }
    }
    async createTables() {
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            // 用户表
            await client.query(`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          role VARCHAR(50) DEFAULT 'user',
          is_active BOOLEAN DEFAULT true,
          subscription_expiry TIMESTAMP,
          total_rewards DECIMAL(20, 8) DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // API 密钥表
            await client.query(`
        CREATE TABLE IF NOT EXISTS api_keys (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          exchange VARCHAR(50) NOT NULL,
          api_key VARCHAR(255) NOT NULL,
          secret_key VARCHAR(255) NOT NULL,
          passphrase VARCHAR(255),
          is_active BOOLEAN DEFAULT true,
          encrypted_data TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 钱包地址表
            await client.query(`
        CREATE TABLE IF NOT EXISTS wallet_addresses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          network VARCHAR(50) NOT NULL,
          address VARCHAR(255) NOT NULL,
          private_key TEXT NOT NULL,
          is_active BOOLEAN DEFAULT true,
          balance DECIMAL(20, 8) DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 策略配置表
            await client.query(`
        CREATE TABLE IF NOT EXISTS strategy_configs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL,
          exchange_type VARCHAR(10) NOT NULL,
          exchange VARCHAR(50),
          network VARCHAR(50),
          dex_protocol VARCHAR(50),
          status VARCHAR(50) DEFAULT 'inactive',
          config JSONB NOT NULL,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 订单表
            await client.query(`
        CREATE TABLE IF NOT EXISTS orders (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          strategy_id UUID REFERENCES strategy_configs(id) ON DELETE CASCADE,
          exchange VARCHAR(50) NOT NULL,
          symbol VARCHAR(50) NOT NULL,
          type VARCHAR(50) NOT NULL,
          side VARCHAR(10) NOT NULL,
          amount DECIMAL(20, 8) NOT NULL,
          price DECIMAL(20, 8),
          status VARCHAR(50) DEFAULT 'pending',
          executed_amount DECIMAL(20, 8) DEFAULT 0,
          executed_price DECIMAL(20, 8),
          fees DECIMAL(20, 8) DEFAULT 0,
          tx_hash VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 交易记录表
            await client.query(`
        CREATE TABLE IF NOT EXISTS trades (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          strategy_id UUID REFERENCES strategy_configs(id) ON DELETE CASCADE,
          order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
          exchange VARCHAR(50) NOT NULL,
          symbol VARCHAR(50) NOT NULL,
          side VARCHAR(10) NOT NULL,
          amount DECIMAL(20, 8) NOT NULL,
          price DECIMAL(20, 8) NOT NULL,
          fees DECIMAL(20, 8) DEFAULT 0,
          profit DECIMAL(20, 8),
          tx_hash VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 奖励记录表
            await client.query(`
        CREATE TABLE IF NOT EXISTS rewards (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          amount DECIMAL(20, 8) NOT NULL,
          reason VARCHAR(255) NOT NULL,
          token_symbol VARCHAR(10) DEFAULT 'TAI',
          tx_hash VARCHAR(255),
          is_distributed BOOLEAN DEFAULT false,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 订阅记录表
            await client.query(`
        CREATE TABLE IF NOT EXISTS subscriptions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          plan VARCHAR(50) NOT NULL,
          status VARCHAR(50) DEFAULT 'active',
          start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          end_date TIMESTAMP NOT NULL,
          payment_id VARCHAR(255),
          amount DECIMAL(10, 2) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 创建索引
            await client.query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_wallet_addresses_user_id ON wallet_addresses(user_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_strategy_configs_user_id ON strategy_configs(user_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_orders_strategy_id ON orders(strategy_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_trades_user_id ON trades(user_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_rewards_user_id ON rewards(user_id)');
            await client.query('CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id)');
            await client.query('COMMIT');
            logger_1.logger.info('Database tables created successfully');
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Failed to create database tables:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    // 获取数据库连接
    async getConnection() {
        return await this.pool.connect();
    }
    // 执行查询
    async query(text, params) {
        const client = await this.pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        }
        finally {
            client.release();
        }
    }
    // Redis 操作
    async setCache(key, value, expireInSeconds) {
        const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
        if (expireInSeconds) {
            await this.redisClient.setEx(key, expireInSeconds, stringValue);
        }
        else {
            await this.redisClient.set(key, stringValue);
        }
    }
    async getCache(key) {
        const value = await this.redisClient.get(key);
        if (!value)
            return null;
        try {
            return JSON.parse(value);
        }
        catch {
            return value;
        }
    }
    async deleteCache(key) {
        await this.redisClient.del(key);
    }
    async clearCache(pattern) {
        if (pattern) {
            const keys = await this.redisClient.keys(pattern);
            if (keys.length > 0) {
                await this.redisClient.del(keys);
            }
        }
        else {
            await this.redisClient.flushAll();
        }
    }
    // 关闭连接
    async close() {
        await this.pool.end();
        await this.redisClient.quit();
        logger_1.logger.info('Database connections closed');
    }
}
exports.db = new DatabaseManager();
//# sourceMappingURL=database.js.map
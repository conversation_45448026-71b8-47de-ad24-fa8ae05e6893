import { PoolClient } from 'pg';
declare class DatabaseManager {
    private pool;
    private redisClient;
    constructor();
    private initializeDatabase;
    private createTables;
    getConnection(): Promise<PoolClient>;
    query(text: string, params?: any[]): Promise<any>;
    setCache(key: string, value: any, expireInSeconds?: number): Promise<void>;
    getCache(key: string): Promise<any>;
    deleteCache(key: string): Promise<void>;
    clearCache(pattern?: string): Promise<void>;
    close(): Promise<void>;
}
export declare const db: DatabaseManager;
export {};
//# sourceMappingURL=database.d.ts.map
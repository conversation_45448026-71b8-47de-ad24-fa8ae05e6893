"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeatureEngineeringService = void 0;
const AIInterface_1 = require("../base/AIInterface");
class FeatureEngineeringService {
    /**
     * 从原始价格数据提取特征
     * @param rawData 原始OHLCV数据
     * @returns 特征数据
     */
    static async extractFeatures(rawData) {
        if (!rawData || rawData.length === 0) {
            throw new AIInterface_1.DataError('Raw data is empty');
        }
        const features = [];
        for (let i = 0; i < rawData.length; i++) {
            const candle = rawData[i];
            const feature = {
                timestamp: new Date(candle.timestamp),
                open: parseFloat(candle.open),
                high: parseFloat(candle.high),
                low: parseFloat(candle.low),
                close: parseFloat(candle.close),
                volume: parseFloat(candle.volume)
            };
            // 计算技术指标
            if (i >= 19) { // 需要至少20个数据点
                feature.sma_20 = this.calculateSMA(rawData.slice(i - 19, i + 1), 'close');
            }
            if (i >= 49) { // 需要至少50个数据点
                feature.sma_50 = this.calculateSMA(rawData.slice(i - 49, i + 1), 'close');
            }
            if (i >= 11) {
                feature.ema_12 = this.calculateEMA(rawData.slice(0, i + 1), 'close', 12);
            }
            if (i >= 25) {
                feature.ema_26 = this.calculateEMA(rawData.slice(0, i + 1), 'close', 26);
            }
            if (i >= 13) {
                feature.rsi = this.calculateRSI(rawData.slice(i - 13, i + 1), 'close');
            }
            if (feature.ema_12 && feature.ema_26) {
                feature.macd = feature.ema_12 - feature.ema_26;
                if (i >= 34) { // MACD信号线需要更多数据
                    const macdData = [];
                    for (let j = Math.max(0, i - 34); j <= i; j++) {
                        if (features[j] && features[j].macd !== undefined) {
                            macdData.push({ macd: features[j].macd });
                        }
                    }
                    if (macdData.length >= 9) {
                        feature.macd_signal = this.calculateEMA(macdData, 'macd', 9);
                    }
                }
            }
            if (i >= 19) {
                const bollinger = this.calculateBollingerBands(rawData.slice(i - 19, i + 1), 'close', 20, 2);
                feature.bollinger_upper = bollinger.upper;
                feature.bollinger_lower = bollinger.lower;
            }
            if (i >= 13) {
                feature.atr = this.calculateATR(rawData.slice(i - 13, i + 1));
            }
            features.push(feature);
        }
        return features;
    }
    /**
     * 计算简单移动平均线
     */
    static calculateSMA(data, field) {
        const values = data.map(item => parseFloat(item[field]));
        const sum = values.reduce((acc, val) => acc + val, 0);
        return sum / values.length;
    }
    /**
     * 计算指数移动平均线
     */
    static calculateEMA(data, field, period) {
        const values = data.map(item => parseFloat(item[field]));
        const multiplier = 2 / (period + 1);
        let ema = values[0];
        for (let i = 1; i < values.length; i++) {
            ema = (values[i] * multiplier) + (ema * (1 - multiplier));
        }
        return ema;
    }
    /**
     * 计算相对强弱指数
     */
    static calculateRSI(data, field, period = 14) {
        const values = data.map(item => parseFloat(item[field]));
        const changes = [];
        for (let i = 1; i < values.length; i++) {
            changes.push(values[i] - values[i - 1]);
        }
        const gains = changes.map(change => change > 0 ? change : 0);
        const losses = changes.map(change => change < 0 ? Math.abs(change) : 0);
        const avgGain = gains.slice(-period).reduce((sum, gain) => sum + gain, 0) / period;
        const avgLoss = losses.slice(-period).reduce((sum, loss) => sum + loss, 0) / period;
        if (avgLoss === 0)
            return 100;
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }
    /**
     * 计算布林带
     */
    static calculateBollingerBands(data, field, period, stdDev) {
        const values = data.map(item => parseFloat(item[field]));
        const sma = this.calculateSMA(data, field);
        // 计算标准差
        const squaredDiffs = values.map(value => Math.pow(value - sma, 2));
        const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
        const standardDeviation = Math.sqrt(variance);
        return {
            upper: sma + (standardDeviation * stdDev),
            middle: sma,
            lower: sma - (standardDeviation * stdDev)
        };
    }
    /**
     * 计算平均真实范围
     */
    static calculateATR(data, period = 14) {
        const trueRanges = [];
        for (let i = 1; i < data.length; i++) {
            const current = data[i];
            const previous = data[i - 1];
            const tr1 = parseFloat(current.high) - parseFloat(current.low);
            const tr2 = Math.abs(parseFloat(current.high) - parseFloat(previous.close));
            const tr3 = Math.abs(parseFloat(current.low) - parseFloat(previous.close));
            trueRanges.push(Math.max(tr1, tr2, tr3));
        }
        const recentTR = trueRanges.slice(-period);
        return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
    }
    /**
     * 计算MACD
     */
    static calculateMACD(data, field = 'close') {
        const ema12 = [];
        const ema26 = [];
        const macd = [];
        const signal = [];
        const histogram = [];
        // 计算EMA12和EMA26
        for (let i = 0; i < data.length; i++) {
            if (i >= 11) {
                ema12.push(this.calculateEMA(data.slice(0, i + 1), field, 12));
            }
            if (i >= 25) {
                ema26.push(this.calculateEMA(data.slice(0, i + 1), field, 26));
            }
        }
        // 计算MACD线
        const minLength = Math.min(ema12.length, ema26.length);
        for (let i = 0; i < minLength; i++) {
            macd.push(ema12[ema12.length - minLength + i] - ema26[ema26.length - minLength + i]);
        }
        // 计算信号线（MACD的9期EMA）
        for (let i = 0; i < macd.length; i++) {
            if (i >= 8) {
                const macdData = macd.slice(i - 8, i + 1).map(val => ({ macd: val }));
                signal.push(this.calculateEMA(macdData, 'macd', 9));
            }
        }
        // 计算柱状图
        const minSignalLength = Math.min(macd.length, signal.length);
        for (let i = 0; i < minSignalLength; i++) {
            histogram.push(macd[macd.length - minSignalLength + i] - signal[signal.length - minSignalLength + i]);
        }
        return { macd, signal, histogram };
    }
    /**
     * 计算随机指标
     */
    static calculateStochastic(data, kPeriod = 14, dPeriod = 3) {
        const k = [];
        const d = [];
        for (let i = kPeriod - 1; i < data.length; i++) {
            const period = data.slice(i - kPeriod + 1, i + 1);
            const high = Math.max(...period.map(item => parseFloat(item.high)));
            const low = Math.min(...period.map(item => parseFloat(item.low)));
            const close = parseFloat(data[i].close);
            const kValue = ((close - low) / (high - low)) * 100;
            k.push(kValue);
        }
        // 计算%D（%K的移动平均）
        for (let i = dPeriod - 1; i < k.length; i++) {
            const dValue = k.slice(i - dPeriod + 1, i + 1).reduce((sum, val) => sum + val, 0) / dPeriod;
            d.push(dValue);
        }
        return { k, d };
    }
    /**
     * 计算威廉指标
     */
    static calculateWilliamsR(data, period = 14) {
        const williamsR = [];
        for (let i = period - 1; i < data.length; i++) {
            const periodData = data.slice(i - period + 1, i + 1);
            const high = Math.max(...periodData.map(item => parseFloat(item.high)));
            const low = Math.min(...periodData.map(item => parseFloat(item.low)));
            const close = parseFloat(data[i].close);
            const wr = ((high - close) / (high - low)) * -100;
            williamsR.push(wr);
        }
        return williamsR;
    }
    /**
     * 计算商品通道指数
     */
    static calculateCCI(data, period = 20) {
        const cci = [];
        for (let i = period - 1; i < data.length; i++) {
            const periodData = data.slice(i - period + 1, i + 1);
            // 计算典型价格
            const typicalPrices = periodData.map(item => {
                const high = parseFloat(item.high);
                const low = parseFloat(item.low);
                const close = parseFloat(item.close);
                return (high + low + close) / 3;
            });
            // 计算简单移动平均
            const sma = typicalPrices.reduce((sum, tp) => sum + tp, 0) / period;
            // 计算平均偏差
            const meanDeviation = typicalPrices.reduce((sum, tp) => sum + Math.abs(tp - sma), 0) / period;
            // 计算CCI
            const currentTypicalPrice = typicalPrices[typicalPrices.length - 1];
            const cciValue = (currentTypicalPrice - sma) / (0.015 * meanDeviation);
            cci.push(cciValue);
        }
        return cci;
    }
    /**
     * 计算动量指标
     */
    static calculateMomentum(data, period = 10, field = 'close') {
        const momentum = [];
        for (let i = period; i < data.length; i++) {
            const current = parseFloat(data[i][field]);
            const previous = parseFloat(data[i - period][field]);
            momentum.push(current - previous);
        }
        return momentum;
    }
    /**
     * 计算变化率
     */
    static calculateROC(data, period = 10, field = 'close') {
        const roc = [];
        for (let i = period; i < data.length; i++) {
            const current = parseFloat(data[i][field]);
            const previous = parseFloat(data[i - period][field]);
            const rocValue = ((current - previous) / previous) * 100;
            roc.push(rocValue);
        }
        return roc;
    }
    /**
     * 数据标准化
     */
    static normalizeData(data) {
        const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
        const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
        const stdDev = Math.sqrt(variance);
        return data.map(val => (val - mean) / stdDev);
    }
    /**
     * 最小-最大标准化
     */
    static minMaxNormalize(data) {
        const min = Math.min(...data);
        const max = Math.max(...data);
        const range = max - min;
        if (range === 0)
            return data.map(() => 0);
        return data.map(val => (val - min) / range);
    }
    /**
     * 创建滞后特征
     */
    static createLagFeatures(data, lags) {
        const laggedData = [];
        for (let i = Math.max(...lags); i < data.length; i++) {
            const row = [];
            for (const lag of lags) {
                row.push(data[i - lag]);
            }
            laggedData.push(row);
        }
        return laggedData;
    }
    /**
     * 计算滚动统计特征
     */
    static calculateRollingStats(data, window) {
        const mean = [];
        const std = [];
        const min = [];
        const max = [];
        for (let i = window - 1; i < data.length; i++) {
            const windowData = data.slice(i - window + 1, i + 1);
            const windowMean = windowData.reduce((sum, val) => sum + val, 0) / window;
            const windowVariance = windowData.reduce((sum, val) => sum + Math.pow(val - windowMean, 2), 0) / window;
            const windowStd = Math.sqrt(windowVariance);
            const windowMin = Math.min(...windowData);
            const windowMax = Math.max(...windowData);
            mean.push(windowMean);
            std.push(windowStd);
            min.push(windowMin);
            max.push(windowMax);
        }
        return { mean, std, min, max };
    }
}
exports.FeatureEngineeringService = FeatureEngineeringService;
//# sourceMappingURL=FeatureEngineeringService.js.map
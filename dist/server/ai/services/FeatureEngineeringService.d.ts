import { MarketFeatures } from '../base/AIInterface';
export declare class FeatureEngineeringService {
    /**
     * 从原始价格数据提取特征
     * @param rawData 原始OHLCV数据
     * @returns 特征数据
     */
    static extractFeatures(rawData: any[]): Promise<MarketFeatures[]>;
    /**
     * 计算简单移动平均线
     */
    private static calculateSMA;
    /**
     * 计算指数移动平均线
     */
    private static calculateEMA;
    /**
     * 计算相对强弱指数
     */
    private static calculateRSI;
    /**
     * 计算布林带
     */
    private static calculateBollingerBands;
    /**
     * 计算平均真实范围
     */
    private static calculateATR;
    /**
     * 计算MACD
     */
    static calculateMACD(data: any[], field?: string): {
        macd: number[];
        signal: number[];
        histogram: number[];
    };
    /**
     * 计算随机指标
     */
    static calculateStochastic(data: any[], kPeriod?: number, dPeriod?: number): {
        k: number[];
        d: number[];
    };
    /**
     * 计算威廉指标
     */
    static calculateWilliamsR(data: any[], period?: number): number[];
    /**
     * 计算商品通道指数
     */
    static calculateCCI(data: any[], period?: number): number[];
    /**
     * 计算动量指标
     */
    static calculateMomentum(data: any[], period?: number, field?: string): number[];
    /**
     * 计算变化率
     */
    static calculateROC(data: any[], period?: number, field?: string): number[];
    /**
     * 数据标准化
     */
    static normalizeData(data: number[]): number[];
    /**
     * 最小-最大标准化
     */
    static minMaxNormalize(data: number[]): number[];
    /**
     * 创建滞后特征
     */
    static createLagFeatures(data: number[], lags: number[]): number[][];
    /**
     * 计算滚动统计特征
     */
    static calculateRollingStats(data: number[], window: number): {
        mean: number[];
        std: number[];
        min: number[];
        max: number[];
    };
}
//# sourceMappingURL=FeatureEngineeringService.d.ts.map
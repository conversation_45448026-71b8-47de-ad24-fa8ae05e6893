import { EventEmitter } from 'events';
import { IAIService, MarketFeatures, PredictionResult, TrainingConfig, ModelInfo, ModelMetrics } from '../base/AIInterface';
export declare class PredictionService extends EventEmitter implements IAIService {
    private models;
    private modelConfigs;
    private modelMetrics;
    private realTimePredictions;
    constructor();
    createModel(config: TrainingConfig): Promise<ModelInfo>;
    trainModel(modelId: string, data: MarketFeatures[]): Promise<ModelMetrics>;
    loadModel(modelId: string): Promise<void>;
    saveModel(modelId: string, path?: string): Promise<string>;
    deleteModel(modelId: string): Promise<void>;
    predict(modelId: string, features: MarketFeatures[]): Promise<PredictionResult>;
    batchPredict(modelId: string, featuresArray: MarketFeatures[][]): Promise<PredictionResult[]>;
    evaluateModel(modelId: string, testData: MarketFeatures[]): Promise<ModelMetrics>;
    backtestModel(modelId: string, historicalData: MarketFeatures[]): Promise<any>;
    extractFeatures(rawData: any[]): Promise<MarketFeatures[]>;
    calculateTechnicalIndicators(priceData: any[]): Promise<any>;
    optimizeHyperparameters(config: TrainingConfig, data: MarketFeatures[]): Promise<TrainingConfig>;
    ensemblePredict(modelIds: string[], features: MarketFeatures[]): Promise<PredictionResult>;
    startRealTimePrediction(modelId: string, symbol: string, callback: (prediction: PredictionResult) => void): Promise<void>;
    stopRealTimePrediction(modelId: string): Promise<void>;
    getModelPerformance(modelId: string, startDate?: Date, endDate?: Date): Promise<any>;
    detectModelDrift(modelId: string, recentData: MarketFeatures[]): Promise<boolean>;
    getAvailableModels(): Promise<ModelInfo[]>;
    getModelInfo(modelId: string): Promise<ModelInfo>;
    updateModelConfig(modelId: string, config: Partial<TrainingConfig>): Promise<void>;
    private generateModelId;
    private validateTrainingConfig;
    private initializeModel;
    private splitData;
    private preprocessData;
    private performTraining;
    private performPrediction;
    private performEvaluation;
}
//# sourceMappingURL=PredictionService.d.ts.map
{"version": 3, "file": "AIInterface.js", "sourceRoot": "", "sources": ["../../../../src/server/ai/base/AIInterface.ts"], "names": [], "mappings": ";;;AAAA,SAAS;AACT,IAAY,WASX;AATD,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,0BAAW,CAAA;IACX,0CAA2B,CAAA;IAC3B,0BAAW,CAAA;IACX,8CAA+B,CAAA;IAC/B,mCAAoB,CAAA;IACpB,0BAAW,CAAA;IACX,sDAAuC,CAAA;AACzC,CAAC,EATW,WAAW,2BAAX,WAAW,QAStB;AAED,OAAO;AACP,IAAY,cAOX;AAPD,WAAY,cAAc;IACxB,iCAAe,CAAA;IACf,yCAAuB,CAAA;IACvB,2CAAyB,CAAA;IACzB,mCAAiB,CAAA;IACjB,mDAAiC,CAAA;IACjC,2DAAyC,CAAA;AAC3C,CAAC,EAPW,cAAc,8BAAd,cAAc,QAOzB;AAED,OAAO;AACP,IAAY,SAUX;AAVD,WAAY,SAAS;IACnB,4BAAe,CAAA;IACf,4BAAe,CAAA;IACf,8BAAiB,CAAA;IACjB,8BAAiB,CAAA;IACjB,0BAAa,CAAA;IACb,0BAAa,CAAA;IACb,4BAAe,CAAA;IACf,yBAAY,CAAA;IACZ,0BAAa,CAAA;AACf,CAAC,EAVW,SAAS,yBAAT,SAAS,QAUpB;AAiLD,QAAQ;AACR,MAAa,OAAQ,SAAQ,KAAK;IAChC,YACE,OAAe,EACR,IAAa,EACb,OAAgB;QAEvB,KAAK,CAAC,OAAO,CAAC,CAAC;QAHR,SAAI,GAAJ,IAAI,CAAS;QACb,YAAO,GAAP,OAAO,CAAS;QAGvB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;IACxB,CAAC;CACF;AATD,0BASC;AAED,SAAS;AACT,MAAa,kBAAmB,SAAQ,OAAO;IAC7C,YAAY,OAAe,EAAE,OAAgB;QAC3C,KAAK,CAAC,OAAO,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF;AALD,gDAKC;AAED,OAAO;AACP,MAAa,eAAgB,SAAQ,OAAO;IAC1C,YAAY,OAAe,EAAE,OAAgB;QAC3C,KAAK,CAAC,OAAO,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AALD,0CAKC;AAED,OAAO;AACP,MAAa,SAAU,SAAQ,OAAO;IACpC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC1B,CAAC;CACF;AALD,8BAKC;AAED,UAAU;AACV,MAAa,kBAAmB,SAAQ,OAAO;IAC7C,YAAY,OAAe;QACzB,KAAK,CAAC,oBAAoB,OAAO,EAAE,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF;AALD,gDAKC"}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelNotFoundError = exports.DataError = exports.PredictionError = exports.ModelTrainingError = exports.AIError = exports.TimeFrame = exports.PredictionType = exports.AIModelType = void 0;
// AI模型类型
var AIModelType;
(function (AIModelType) {
    AIModelType["LSTM"] = "lstm";
    AIModelType["GRU"] = "gru";
    AIModelType["TRANSFORMER"] = "transformer";
    AIModelType["CNN"] = "cnn";
    AIModelType["RANDOM_FOREST"] = "random_forest";
    AIModelType["XG_BOOST"] = "xgboost";
    AIModelType["SVM"] = "svm";
    AIModelType["LINEAR_REGRESSION"] = "linear_regression";
})(AIModelType || (exports.AIModelType = AIModelType = {}));
// 预测类型
var PredictionType;
(function (PredictionType) {
    PredictionType["PRICE"] = "price";
    PredictionType["DIRECTION"] = "direction";
    PredictionType["VOLATILITY"] = "volatility";
    PredictionType["VOLUME"] = "volume";
    PredictionType["TREND_STRENGTH"] = "trend_strength";
    PredictionType["SUPPORT_RESISTANCE"] = "support_resistance";
})(PredictionType || (exports.PredictionType = PredictionType = {}));
// 时间框架
var TimeFrame;
(function (TimeFrame) {
    TimeFrame["MINUTE_1"] = "1m";
    TimeFrame["MINUTE_5"] = "5m";
    TimeFrame["MINUTE_15"] = "15m";
    TimeFrame["MINUTE_30"] = "30m";
    TimeFrame["HOUR_1"] = "1h";
    TimeFrame["HOUR_4"] = "4h";
    TimeFrame["HOUR_12"] = "12h";
    TimeFrame["DAY_1"] = "1d";
    TimeFrame["WEEK_1"] = "1w";
})(TimeFrame || (exports.TimeFrame = TimeFrame = {}));
// AI错误类
class AIError extends Error {
    constructor(message, code, modelId) {
        super(message);
        this.code = code;
        this.modelId = modelId;
        this.name = 'AIError';
    }
}
exports.AIError = AIError;
// 模型训练错误
class ModelTrainingError extends AIError {
    constructor(message, modelId) {
        super(message, 'MODEL_TRAINING_ERROR', modelId);
        this.name = 'ModelTrainingError';
    }
}
exports.ModelTrainingError = ModelTrainingError;
// 预测错误
class PredictionError extends AIError {
    constructor(message, modelId) {
        super(message, 'PREDICTION_ERROR', modelId);
        this.name = 'PredictionError';
    }
}
exports.PredictionError = PredictionError;
// 数据错误
class DataError extends AIError {
    constructor(message) {
        super(message, 'DATA_ERROR');
        this.name = 'DataError';
    }
}
exports.DataError = DataError;
// 模型不存在错误
class ModelNotFoundError extends AIError {
    constructor(modelId) {
        super(`Model not found: ${modelId}`, 'MODEL_NOT_FOUND', modelId);
        this.name = 'ModelNotFoundError';
    }
}
exports.ModelNotFoundError = ModelNotFoundError;
//# sourceMappingURL=AIInterface.js.map
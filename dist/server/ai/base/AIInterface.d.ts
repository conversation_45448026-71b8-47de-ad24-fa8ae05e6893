export declare enum AIModelType {
    LSTM = "lstm",
    GRU = "gru",
    TRANSFORMER = "transformer",
    CNN = "cnn",
    RANDOM_FOREST = "random_forest",
    XG_BOOST = "xgboost",
    SVM = "svm",
    LINEAR_REGRESSION = "linear_regression"
}
export declare enum PredictionType {
    PRICE = "price",
    DIRECTION = "direction",
    VOLATILITY = "volatility",
    VOLUME = "volume",
    TREND_STRENGTH = "trend_strength",
    SUPPORT_RESISTANCE = "support_resistance"
}
export declare enum TimeFrame {
    MINUTE_1 = "1m",
    MINUTE_5 = "5m",
    MINUTE_15 = "15m",
    MINUTE_30 = "30m",
    HOUR_1 = "1h",
    HOUR_4 = "4h",
    HOUR_12 = "12h",
    DAY_1 = "1d",
    WEEK_1 = "1w"
}
export interface MarketFeatures {
    timestamp: Date;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    sma_20?: number;
    sma_50?: number;
    ema_12?: number;
    ema_26?: number;
    rsi?: number;
    macd?: number;
    macd_signal?: number;
    bollinger_upper?: number;
    bollinger_lower?: number;
    atr?: number;
    fear_greed_index?: number;
    social_sentiment?: number;
    news_sentiment?: number;
    active_addresses?: number;
    transaction_count?: number;
    network_hash_rate?: number;
    exchange_inflow?: number;
    exchange_outflow?: number;
}
export interface PredictionResult {
    type: PredictionType;
    value: number;
    confidence: number;
    timestamp: Date;
    timeframe: TimeFrame;
    metadata?: {
        model_version?: string;
        feature_importance?: Record<string, number>;
        prediction_interval?: [number, number];
        model_accuracy?: number;
    };
}
export interface TrainingConfig {
    modelType: AIModelType;
    symbol: string;
    timeframe: TimeFrame;
    lookbackPeriod: number;
    predictionHorizon: number;
    features: string[];
    epochs?: number;
    batchSize?: number;
    learningRate?: number;
    hiddenLayers?: number[];
    dropout?: number;
    trainRatio: number;
    validationRatio: number;
    testRatio: number;
    normalizeData?: boolean;
    useGpu?: boolean;
    saveModel?: boolean;
}
export interface ModelMetrics {
    accuracy?: number;
    precision?: number;
    recall?: number;
    f1Score?: number;
    mse?: number;
    mae?: number;
    rmse?: number;
    mape?: number;
    sharpeRatio?: number;
    maxDrawdown?: number;
    winRate?: number;
    profitFactor?: number;
}
export interface ModelInfo {
    id: string;
    name: string;
    type: AIModelType;
    symbol: string;
    timeframe: TimeFrame;
    version: string;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    metrics: ModelMetrics;
    config: TrainingConfig;
}
export interface IAIService {
    createModel(config: TrainingConfig): Promise<ModelInfo>;
    trainModel(modelId: string, data: MarketFeatures[]): Promise<ModelMetrics>;
    loadModel(modelId: string): Promise<void>;
    saveModel(modelId: string, path?: string): Promise<string>;
    deleteModel(modelId: string): Promise<void>;
    predict(modelId: string, features: MarketFeatures[]): Promise<PredictionResult>;
    batchPredict(modelId: string, featuresArray: MarketFeatures[][]): Promise<PredictionResult[]>;
    evaluateModel(modelId: string, testData: MarketFeatures[]): Promise<ModelMetrics>;
    backtestModel(modelId: string, historicalData: MarketFeatures[]): Promise<any>;
    extractFeatures(rawData: any[]): Promise<MarketFeatures[]>;
    calculateTechnicalIndicators(priceData: any[]): Promise<any>;
    optimizeHyperparameters(config: TrainingConfig, data: MarketFeatures[]): Promise<TrainingConfig>;
    ensemblePredict(modelIds: string[], features: MarketFeatures[]): Promise<PredictionResult>;
    startRealTimePrediction(modelId: string, symbol: string, callback: (prediction: PredictionResult) => void): Promise<void>;
    stopRealTimePrediction(modelId: string): Promise<void>;
    getModelPerformance(modelId: string, startDate?: Date, endDate?: Date): Promise<any>;
    detectModelDrift(modelId: string, recentData: MarketFeatures[]): Promise<boolean>;
    getAvailableModels(): Promise<ModelInfo[]>;
    getModelInfo(modelId: string): Promise<ModelInfo>;
    updateModelConfig(modelId: string, config: Partial<TrainingConfig>): Promise<void>;
}
export interface AISignal {
    action: 'buy' | 'sell' | 'hold';
    confidence: number;
    price: number;
    quantity: number;
    stopLoss?: number;
    takeProfit?: number;
    reasoning: string;
    predictions: PredictionResult[];
    timestamp: Date;
}
export interface AIStrategyConfig {
    modelIds: string[];
    symbol: string;
    timeframe: TimeFrame;
    minConfidence: number;
    ensembleMethod: 'average' | 'weighted' | 'voting';
    riskManagement: {
        maxPositionSize: number;
        stopLossPercent: number;
        takeProfitPercent: number;
        maxDailyLoss: number;
    };
    rebalanceFrequency: number;
}
export declare class AIError extends Error {
    code?: string | undefined;
    modelId?: string | undefined;
    constructor(message: string, code?: string | undefined, modelId?: string | undefined);
}
export declare class ModelTrainingError extends AIError {
    constructor(message: string, modelId?: string);
}
export declare class PredictionError extends AIError {
    constructor(message: string, modelId?: string);
}
export declare class DataError extends AIError {
    constructor(message: string);
}
export declare class ModelNotFoundError extends AIError {
    constructor(modelId: string);
}
//# sourceMappingURL=AIInterface.d.ts.map
import winston from 'winston';
export declare const logger: winston.Logger;
export declare const strategyLogger: winston.Logger;
export declare const tradeLogger: winston.Logger;
export declare const securityLogger: winston.Logger;
export declare const logUtils: {
    logUserAction: (userId: string, action: string, details?: any) => void;
    logStrategyAction: (strategyId: string, action: string, details?: any) => void;
    logTrade: (userId: string, strategyId: string, tradeData: any) => void;
    logSecurityEvent: (event: string, details: any) => void;
    logError: (error: Error, context?: any) => void;
    logPerformance: (operation: string, duration: number, details?: any) => void;
};
export default logger;
//# sourceMappingURL=logger.d.ts.map
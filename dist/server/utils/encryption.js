"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptionUtils = void 0;
const crypto_js_1 = __importDefault(require("crypto-js"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = require("./logger");
// 加密工具类
class EncryptionUtils {
    // 获取加密密钥
    static getEncryptionKey() {
        const key = process.env.ENCRYPTION_KEY;
        if (!key || key.length < 32) {
            throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
        }
        return key;
    }
    // 获取 JWT 密钥
    static getJWTSecret() {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is required');
        }
        return secret;
    }
    // 加密敏感数据（API 密钥、私钥等）
    static encryptSensitiveData(data) {
        try {
            const key = this.getEncryptionKey();
            const encrypted = crypto_js_1.default.AES.encrypt(data, key).toString();
            return encrypted;
        }
        catch (error) {
            logger_1.logger.error('Failed to encrypt sensitive data:', error);
            throw new Error('Encryption failed');
        }
    }
    // 解密敏感数据
    static decryptSensitiveData(encryptedData) {
        try {
            const key = this.getEncryptionKey();
            const decrypted = crypto_js_1.default.AES.decrypt(encryptedData, key);
            const originalData = decrypted.toString(crypto_js_1.default.enc.Utf8);
            if (!originalData) {
                throw new Error('Failed to decrypt data');
            }
            return originalData;
        }
        catch (error) {
            logger_1.logger.error('Failed to decrypt sensitive data:', error);
            throw new Error('Decryption failed');
        }
    }
    // 哈希密码
    static async hashPassword(password) {
        try {
            const hashedPassword = await bcrypt_1.default.hash(password, this.SALT_ROUNDS);
            return hashedPassword;
        }
        catch (error) {
            logger_1.logger.error('Failed to hash password:', error);
            throw new Error('Password hashing failed');
        }
    }
    // 验证密码
    static async verifyPassword(password, hashedPassword) {
        try {
            const isValid = await bcrypt_1.default.compare(password, hashedPassword);
            return isValid;
        }
        catch (error) {
            logger_1.logger.error('Failed to verify password:', error);
            throw new Error('Password verification failed');
        }
    }
    // 生成 JWT Token
    static generateJWTToken(payload, expiresIn) {
        try {
            const secret = this.getJWTSecret();
            const token = jsonwebtoken_1.default.sign(payload, secret, {
                expiresIn: expiresIn || this.JWT_EXPIRES_IN
            });
            return token;
        }
        catch (error) {
            logger_1.logger.error('Failed to generate JWT token:', error);
            throw new Error('Token generation failed');
        }
    }
    // 验证 JWT Token
    static verifyJWTToken(token) {
        try {
            const secret = this.getJWTSecret();
            const decoded = jsonwebtoken_1.default.verify(token, secret);
            return decoded;
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                throw new Error('Token expired');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw new Error('Invalid token');
            }
            else {
                logger_1.logger.error('Failed to verify JWT token:', error);
                throw new Error('Token verification failed');
            }
        }
    }
    // 生成随机字符串
    static generateRandomString(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    // 生成 API 密钥
    static generateAPIKey() {
        return this.generateRandomString(64);
    }
    // 加密 CSV 文件内容
    static encryptCSVContent(content, password) {
        try {
            const encrypted = crypto_js_1.default.AES.encrypt(content, password).toString();
            return encrypted;
        }
        catch (error) {
            logger_1.logger.error('Failed to encrypt CSV content:', error);
            throw new Error('CSV encryption failed');
        }
    }
    // 解密 CSV 文件内容
    static decryptCSVContent(encryptedContent, password) {
        try {
            const decrypted = crypto_js_1.default.AES.decrypt(encryptedContent, password);
            const originalContent = decrypted.toString(crypto_js_1.default.enc.Utf8);
            if (!originalContent) {
                throw new Error('Invalid password or corrupted data');
            }
            return originalContent;
        }
        catch (error) {
            logger_1.logger.error('Failed to decrypt CSV content:', error);
            throw new Error('CSV decryption failed');
        }
    }
    // 创建数字签名
    static createSignature(data, secret) {
        try {
            const signature = crypto_js_1.default.HmacSHA256(data, secret).toString();
            return signature;
        }
        catch (error) {
            logger_1.logger.error('Failed to create signature:', error);
            throw new Error('Signature creation failed');
        }
    }
    // 验证数字签名
    static verifySignature(data, signature, secret) {
        try {
            const expectedSignature = this.createSignature(data, secret);
            return signature === expectedSignature;
        }
        catch (error) {
            logger_1.logger.error('Failed to verify signature:', error);
            return false;
        }
    }
    // 生成安全的随机数
    static generateSecureRandom(min, max) {
        const range = max - min + 1;
        const randomBytes = crypto_js_1.default.lib.WordArray.random(4);
        const randomValue = randomBytes.words[0] >>> 0; // 转换为无符号整数
        return min + (randomValue % range);
    }
    // 混淆数值（用于交易金额随机化）
    static obfuscateAmount(amount, variationPercent = 5) {
        const variation = amount * (variationPercent / 100);
        const randomVariation = (Math.random() - 0.5) * 2 * variation;
        return Math.max(0, amount + randomVariation);
    }
    // 生成时间戳签名（防重放攻击）
    static generateTimestampSignature(data, timestamp, secret) {
        const payload = `${data}:${timestamp}`;
        return this.createSignature(payload, secret);
    }
    // 验证时间戳签名
    static verifyTimestampSignature(data, timestamp, signature, secret, maxAge = 300000 // 5分钟
    ) {
        // 检查时间戳是否过期
        const now = Date.now();
        if (now - timestamp > maxAge) {
            return false;
        }
        // 验证签名
        const expectedSignature = this.generateTimestampSignature(data, timestamp, secret);
        return signature === expectedSignature;
    }
}
exports.EncryptionUtils = EncryptionUtils;
EncryptionUtils.SALT_ROUNDS = 12;
EncryptionUtils.JWT_EXPIRES_IN = '24h';
exports.default = EncryptionUtils;
//# sourceMappingURL=encryption.js.map
{"version": 3, "file": "encryption.js", "sourceRoot": "", "sources": ["../../../src/server/utils/encryption.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAAiC;AACjC,oDAA4B;AAC5B,gEAA+B;AAC/B,qCAAkC;AAElC,QAAQ;AACR,MAAa,eAAe;IAI1B,SAAS;IACD,MAAM,CAAC,gBAAgB;QAC7B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,YAAY;IACJ,MAAM,CAAC,YAAY;QACzB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qBAAqB;IACrB,MAAM,CAAC,oBAAoB,CAAC,IAAY;QACtC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG,mBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC7D,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,SAAS;IACT,MAAM,CAAC,oBAAoB,CAAC,aAAqB;QAC/C,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG,mBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;YAC3D,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE3D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,OAAO;IACP,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACrE,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,OAAO;IACP,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,cAAsB;QAClE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAC/D,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,eAAe;IACf,MAAM,CAAC,gBAAgB,CAAC,OAAY,EAAE,SAAkB;QACtD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;gBACtC,SAAS,EAAE,SAAS,IAAI,IAAI,CAAC,cAAc;aAC5C,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,eAAe;IACf,MAAM,CAAC,cAAc,CAAC,KAAa;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1C,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAClD,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU;IACV,MAAM,CAAC,oBAAoB,CAAC,SAAiB,EAAE;QAC7C,MAAM,KAAK,GAAG,gEAAgE,CAAC;QAC/E,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,YAAY;IACZ,MAAM,CAAC,cAAc;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,cAAc;IACd,MAAM,CAAC,iBAAiB,CAAC,OAAe,EAAE,QAAgB;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,mBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrE,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,cAAc;IACd,MAAM,CAAC,iBAAiB,CAAC,gBAAwB,EAAE,QAAgB;QACjE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,mBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,eAAe,GAAG,SAAS,CAAC,QAAQ,CAAC,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE9D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,SAAS;IACT,MAAM,CAAC,eAAe,CAAC,IAAY,EAAE,MAAc;QACjD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,mBAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC/D,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,SAAS;IACT,MAAM,CAAC,eAAe,CAAC,IAAY,EAAE,SAAiB,EAAE,MAAc;QACpE,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC7D,OAAO,SAAS,KAAK,iBAAiB,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,WAAW;IACX,MAAM,CAAC,oBAAoB,CAAC,GAAW,EAAE,GAAW;QAClD,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QAC5B,MAAM,WAAW,GAAG,mBAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;QAC3D,OAAO,GAAG,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,eAAe,CAAC,MAAc,EAAE,mBAA2B,CAAC;QACjE,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,eAAe,CAAC,CAAC;IAC/C,CAAC;IAED,iBAAiB;IACjB,MAAM,CAAC,0BAA0B,CAAC,IAAY,EAAE,SAAiB,EAAE,MAAc;QAC/E,MAAM,OAAO,GAAG,GAAG,IAAI,IAAI,SAAS,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,UAAU;IACV,MAAM,CAAC,wBAAwB,CAC7B,IAAY,EACZ,SAAiB,EACjB,SAAiB,EACjB,MAAc,EACd,SAAiB,MAAM,CAAC,MAAM;;QAE9B,YAAY;QACZ,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,SAAS,GAAG,MAAM,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;QACP,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACnF,OAAO,SAAS,KAAK,iBAAiB,CAAC;IACzC,CAAC;;AAjNH,0CAkNC;AAjNyB,2BAAW,GAAG,EAAE,CAAC;AACjB,8BAAc,GAAG,KAAK,CAAC;AAkNjD,kBAAe,eAAe,CAAC"}
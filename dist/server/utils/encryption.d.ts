export declare class EncryptionUtils {
    private static readonly SALT_ROUNDS;
    private static readonly JWT_EXPIRES_IN;
    private static getEncryptionKey;
    private static getJWTSecret;
    static encryptSensitiveData(data: string): string;
    static decryptSensitiveData(encryptedData: string): string;
    static hashPassword(password: string): Promise<string>;
    static verifyPassword(password: string, hashedPassword: string): Promise<boolean>;
    static generateJWTToken(payload: any, expiresIn?: string): string;
    static verifyJWTToken(token: string): any;
    static generateRandomString(length?: number): string;
    static generateAPIKey(): string;
    static encryptCSVContent(content: string, password: string): string;
    static decryptCSVContent(encryptedContent: string, password: string): string;
    static createSignature(data: string, secret: string): string;
    static verifySignature(data: string, signature: string, secret: string): boolean;
    static generateSecureRandom(min: number, max: number): number;
    static obfuscateAmount(amount: number, variationPercent?: number): number;
    static generateTimestampSignature(data: string, timestamp: number, secret: string): string;
    static verifyTimestampSignature(data: string, timestamp: number, signature: string, secret: string, maxAge?: number): boolean;
}
export default EncryptionUtils;
//# sourceMappingURL=encryption.d.ts.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logUtils = exports.securityLogger = exports.tradeLogger = exports.strategyLogger = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
// 创建日志格式
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.prettyPrint());
// 创建控制台格式
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
}), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
}));
// 创建 Winston logger
exports.logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: { service: 'tradeai-bot' },
    transports: [
        // 错误日志文件
        new winston_1.default.transports.File({
            filename: path_1.default.join(process.cwd(), 'logs', 'error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // 组合日志文件
        new winston_1.default.transports.File({
            filename: path_1.default.join(process.cwd(), 'logs', 'combined.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
});
// 在非生产环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
    exports.logger.add(new winston_1.default.transports.Console({
        format: consoleFormat
    }));
}
// 策略专用日志器
exports.strategyLogger = winston_1.default.createLogger({
    level: 'info',
    format: logFormat,
    defaultMeta: { service: 'strategy-engine' },
    transports: [
        new winston_1.default.transports.File({
            filename: path_1.default.join(process.cwd(), 'logs', 'strategy.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 10,
        }),
    ],
});
// 交易专用日志器
exports.tradeLogger = winston_1.default.createLogger({
    level: 'info',
    format: logFormat,
    defaultMeta: { service: 'trade-engine' },
    transports: [
        new winston_1.default.transports.File({
            filename: path_1.default.join(process.cwd(), 'logs', 'trades.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 10,
        }),
    ],
});
// 安全专用日志器
exports.securityLogger = winston_1.default.createLogger({
    level: 'info',
    format: logFormat,
    defaultMeta: { service: 'security' },
    transports: [
        new winston_1.default.transports.File({
            filename: path_1.default.join(process.cwd(), 'logs', 'security.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 10,
        }),
    ],
});
// 日志工具函数
exports.logUtils = {
    // 记录用户操作
    logUserAction: (userId, action, details) => {
        exports.logger.info('User action', {
            userId,
            action,
            details,
            timestamp: new Date().toISOString()
        });
    },
    // 记录策略操作
    logStrategyAction: (strategyId, action, details) => {
        exports.strategyLogger.info('Strategy action', {
            strategyId,
            action,
            details,
            timestamp: new Date().toISOString()
        });
    },
    // 记录交易操作
    logTrade: (userId, strategyId, tradeData) => {
        exports.tradeLogger.info('Trade executed', {
            userId,
            strategyId,
            tradeData,
            timestamp: new Date().toISOString()
        });
    },
    // 记录安全事件
    logSecurityEvent: (event, details) => {
        exports.securityLogger.warn('Security event', {
            event,
            details,
            timestamp: new Date().toISOString()
        });
    },
    // 记录错误
    logError: (error, context) => {
        exports.logger.error('Application error', {
            message: error.message,
            stack: error.stack,
            context,
            timestamp: new Date().toISOString()
        });
    },
    // 记录性能指标
    logPerformance: (operation, duration, details) => {
        exports.logger.info('Performance metric', {
            operation,
            duration,
            details,
            timestamp: new Date().toISOString()
        });
    }
};
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map
declare const app: import("express-serve-static-core").Express;
declare const server: import("http").Server<typeof import("http").IncomingMessage, typeof import("http").ServerResponse>;
declare const wss: import("ws").Server<typeof import("ws"), typeof import("http").IncomingMessage>;
export declare function broadcastToChannel(channel: string, message: any): void;
export declare function sendToUser(userId: string, message: any): void;
export { app, server, wss };
//# sourceMappingURL=index-backup.d.ts.map
{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/server/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,oDAAsD;AACtD,iDAAwC;AACxC,4CAAmD;AACnD,8CAA2D;AAiB3D,WAAW;AACJ,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAoB,EACpB,GAAqB,EACrB,IAA0B,EAC1B,EAAE;IACF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;QAErE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;aACrB,CAAC,CAAC;QACpB,CAAC;QAED,UAAU;QACV,MAAM,OAAO,GAAG,4BAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAEtD,eAAe;QACf,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,KAAK,CAC3B,iFAAiF,EACjF,CAAC,OAAO,CAAC,MAAM,CAAC,CACjB,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gBAAgB;aACX,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wBAAwB;aACnB,CAAC,CAAC;QACpB,CAAC;QAED,eAAe;QACf,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;SAC7C,CAAC;QAEF,IAAI,EAAE,CAAC;IAET,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,CAAC,OAAO,KAAK,eAAe,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,eAAe;aACV,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,KAAK,eAAe,EAAE,CAAC;YAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,eAAe;aACV,CAAC,CAAC;QACpB,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC7C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC;AAxEW,QAAA,iBAAiB,qBAwE5B;AAEF,YAAY;AACL,MAAM,WAAW,GAAG,CAAC,KAAiB,EAAE,EAAE;IAC/C,OAAO,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;QACjF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACpB,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,iBAAQ,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;gBAC/C,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;gBACnB,aAAa,EAAE,KAAK;gBACpB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;gBACvB,QAAQ,EAAE,GAAG,CAAC,IAAI;aACnB,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;aACrB,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAzBW,QAAA,WAAW,eAyBtB;AAEF,cAAc;AACP,MAAM,cAAc,GAAG,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;IACxG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,yBAAyB;SACpB,CAAC,CAAC;IAClB,CAAC;IAEH,WAAW;IACX,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,EAAE,CAAC;QACrC,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,YAAY;IACZ,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,OAAO,EAAE,CAAC;QACvC,WAAW;QACX,IAAI,GAAG,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5E,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8BAA8B;aACzB,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,+BAA+B;KAC1B,CAAC,CAAC;AACpB,CAAC,CAAC;AA7BW,QAAA,cAAc,kBA6BzB;AAEF,UAAU;AACV,MAAM,cAAc,GAAG,IAAI,GAAG,EAAgD,CAAC;AAExE,MAAM,SAAS,GAAG,CAAC,WAAmB,EAAE,QAAgB,EAAE,EAAE;IACjE,OAAO,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;QACjF,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,WAAW,CAAC,CAAC;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,UAAU;QACV,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;gBACtB,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;YACjE,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3B,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;YACjE,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC;YAChC,iBAAQ,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;gBAC/C,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;gBACpB,QAAQ,EAAE,GAAG,CAAC,IAAI;aACnB,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB;aACd,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAxCW,QAAA,SAAS,aAwCpB;AAEF,wBAAwB;AACjB,MAAM,cAAc,GAAG,KAAK,EACjC,GAAoB,EACpB,GAAqB,EACrB,IAA0B,EAC1B,EAAE;IACF,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;QAElD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qBAAqB;aAChB,CAAC,CAAC;QACpB,CAAC;QAED,kBAAkB;QAClB,qBAAqB;QAErB,iBAAiB;QACjB,IAAI,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iBAAiB;aACZ,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,EAAE,CAAC;IAET,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,cAAc,kBAmCzB;AAEF,UAAU;AACH,MAAM,aAAa,GAAG,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;IACvG,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEzB,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QAEpC,eAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YAC1B,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ;YACR,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;SACrB,CAAC,CAAC;QAEH,QAAQ;QACR,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,QAAQ;gBACR,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA5BW,QAAA,aAAa,iBA4BxB;AAEF,UAAU;AACH,MAAM,YAAY,GAAG,CAC1B,KAAU,EACV,GAAoB,EACpB,GAAqB,EACrB,IAA0B,EAC1B,EAAE;IACF,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;QAC/B,KAAK,EAAE,KAAK,CAAC,OAAO;QACpB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;KACrB,CAAC,CAAC;IAEH,iBAAiB;IACjB,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAE7D,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QACnC,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB;QAChE,GAAG,CAAC,aAAa,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;KAC9B,CAAC,CAAC;AACpB,CAAC,CAAC;AAtBW,QAAA,YAAY,gBAsBvB;AAEF,YAAY;AACC,QAAA,UAAU,GAAG;IACxB,MAAM,EAAE,CAAC,MAA0B,EAAE,QAAsD,EAAE,EAAE;QAC7F,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI;YAChE,uBAAuB;YACvB,uBAAuB;SACxB,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEzC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;IACpD,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,WAAW,CAAC;CAC/D,CAAC"}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.corsConfig = exports.errorHandler = exports.requestLogger = exports.validateApiKey = exports.rateLimit = exports.requirePremium = exports.requireRole = exports.authenticateToken = void 0;
const encryption_1 = require("../utils/encryption");
const database_1 = require("../models/database");
const logger_1 = require("../utils/logger");
const types_1 = require("../../shared/types");
// JWT认证中间件
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access token is required'
            });
        }
        // 验证token
        const decoded = encryption_1.EncryptionUtils.verifyJWTToken(token);
        // 从数据库获取最新用户信息
        const result = await database_1.db.query('SELECT id, email, role, is_active, subscription_expiry FROM users WHERE id = $1', [decoded.userId]);
        if (result.rows.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'User not found'
            });
        }
        const user = result.rows[0];
        // 检查用户是否激活
        if (!user.is_active) {
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated'
            });
        }
        // 将用户信息添加到请求对象
        req.user = {
            id: user.id,
            email: user.email,
            role: user.role,
            isActive: user.is_active,
            subscriptionExpiry: user.subscription_expiry
        };
        next();
    }
    catch (error) {
        if (error.message === 'Token expired') {
            return res.status(401).json({
                success: false,
                message: 'Token expired'
            });
        }
        else if (error.message === 'Invalid token') {
            return res.status(401).json({
                success: false,
                message: 'Invalid token'
            });
        }
        logger_1.logger.error('Authentication error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.authenticateToken = authenticateToken;
// 角色权限检查中间件
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        if (!roles.includes(req.user.role)) {
            logger_1.logUtils.logSecurityEvent('unauthorized_access', {
                userId: req.user.id,
                requiredRoles: roles,
                userRole: req.user.role,
                endpoint: req.path
            });
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions'
            });
        }
        next();
    };
};
exports.requireRole = requireRole;
// 高级功能权限检查中间件
const requirePremium = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Authentication required'
        });
    }
    // 检查是否为管理员
    if (req.user.role === types_1.UserRole.ADMIN) {
        return next();
    }
    // 检查是否为高级用户
    if (req.user.role === types_1.UserRole.PREMIUM) {
        // 检查订阅是否过期
        if (req.user.subscriptionExpiry && new Date() > req.user.subscriptionExpiry) {
            return res.status(403).json({
                success: false,
                message: 'Premium subscription expired'
            });
        }
        return next();
    }
    return res.status(403).json({
        success: false,
        message: 'Premium subscription required'
    });
};
exports.requirePremium = requirePremium;
// 速率限制中间件
const rateLimitStore = new Map();
const rateLimit = (maxRequests, windowMs) => {
    return (req, res, next) => {
        const key = req.ip + (req.user?.id || 'anonymous');
        const now = Date.now();
        // 清理过期的记录
        for (const [k, v] of rateLimitStore.entries()) {
            if (now > v.resetTime) {
                rateLimitStore.delete(k);
            }
        }
        const record = rateLimitStore.get(key);
        if (!record) {
            rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
            return next();
        }
        if (now > record.resetTime) {
            rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
            return next();
        }
        if (record.count >= maxRequests) {
            logger_1.logUtils.logSecurityEvent('rate_limit_exceeded', {
                ip: req.ip,
                userId: req.user?.id,
                endpoint: req.path
            });
            return res.status(429).json({
                success: false,
                message: 'Too many requests'
            });
        }
        record.count++;
        next();
    };
};
exports.rateLimit = rateLimit;
// API密钥验证中间件（用于外部API调用）
const validateApiKey = async (req, res, next) => {
    try {
        const apiKey = req.headers['x-api-key'];
        if (!apiKey) {
            return res.status(401).json({
                success: false,
                message: 'API key is required'
            });
        }
        // 这里可以实现API密钥验证逻辑
        // 例如从数据库查询API密钥对应的用户
        // 临时实现：检查是否为有效格式
        if (apiKey.length < 32) {
            return res.status(401).json({
                success: false,
                message: 'Invalid API key'
            });
        }
        next();
    }
    catch (error) {
        logger_1.logger.error('API key validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.validateApiKey = validateApiKey;
// 请求日志中间件
const requestLogger = (req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        logger_1.logger.info('HTTP Request', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id
        });
        // 记录慢请求
        if (duration > 1000) {
            logger_1.logger.warn('Slow request detected', {
                method: req.method,
                url: req.url,
                duration,
                userId: req.user?.id
            });
        }
    });
    next();
};
exports.requestLogger = requestLogger;
// 错误处理中间件
const errorHandler = (error, req, res, next) => {
    logger_1.logger.error('Unhandled error:', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        userId: req.user?.id
    });
    // 不要在生产环境中暴露错误详情
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(error.status || 500).json({
        success: false,
        message: isDevelopment ? error.message : 'Internal server error',
        ...(isDevelopment && { stack: error.stack })
    });
};
exports.errorHandler = errorHandler;
// CORS配置中间件
exports.corsConfig = {
    origin: (origin, callback) => {
        const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
            'http://localhost:3000',
            'http://localhost:5173'
        ];
        // 允许没有origin的请求（如移动应用）
        if (!origin)
            return callback(null, true);
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key']
};
//# sourceMappingURL=auth.js.map
import express from 'express';
import { UserRole } from '../../shared/types';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: UserRole;
                isActive: boolean;
                subscriptionExpiry?: Date;
            };
        }
    }
}
export declare const authenticateToken: (req: express.Request, res: express.Response, next: express.NextFunction) => Promise<express.Response<any, Record<string, any>> | undefined>;
export declare const requireRole: (roles: UserRole[]) => (req: express.Request, res: express.Response, next: express.NextFunction) => express.Response<any, Record<string, any>> | undefined;
export declare const requirePremium: (req: express.Request, res: express.Response, next: express.NextFunction) => void | express.Response<any, Record<string, any>>;
export declare const rateLimit: (maxRequests: number, windowMs: number) => (req: express.Request, res: express.Response, next: express.NextFunction) => void | express.Response<any, Record<string, any>>;
export declare const validateApiKey: (req: express.Request, res: express.Response, next: express.NextFunction) => Promise<express.Response<any, Record<string, any>> | undefined>;
export declare const requestLogger: (req: express.Request, res: express.Response, next: express.NextFunction) => void;
export declare const errorHandler: (error: any, req: express.Request, res: express.Response, next: express.NextFunction) => void;
export declare const corsConfig: {
    origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void;
    credentials: boolean;
    methods: string[];
    allowedHeaders: string[];
};
//# sourceMappingURL=auth.d.ts.map
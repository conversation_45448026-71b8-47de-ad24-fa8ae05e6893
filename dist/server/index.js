"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.server = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// 简化的日志函数
const log = {
    info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
    error: (msg, error) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`, error || ''),
    warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`)
};
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000', 'http://localhost:5173'],
        methods: ['GET', 'POST']
    }
});
exports.io = io;
const PORT = process.env.PORT || 3001;
// 确保日志目录存在
const logsDir = path_1.default.join(process.cwd(), 'logs');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
// 开发环境下禁用 CSP，生产环境启用
if (process.env.NODE_ENV === 'production') {
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'", "'unsafe-eval'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
    }));
}
else {
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: false,
    }));
}
app.use((0, cors_1.default)({
    origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000', 'http://localhost:5173'],
    credentials: true
}));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// 限流中间件
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: {
        error: 'Too many requests from this IP, please try again later.'
    }
});
app.use('/api/', limiter);
// 静态文件服务
if (process.env.NODE_ENV === 'production') {
    const clientDistPath = path_1.default.join(__dirname, '../../client/dist');
    if (fs_1.default.existsSync(clientDistPath)) {
        app.use(express_1.default.static(clientDistPath));
    }
}
// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
    });
});
// API 基础路由
app.get('/api', (req, res) => {
    res.json({
        success: true,
        message: 'TradeAI Bot API',
        version: '1.0.0',
        endpoints: [
            '/api/health',
            '/api/auth',
            '/api/exchanges',
            '/api/strategies',
            '/api/wallets',
            '/api/rewards'
        ]
    });
});
// 临时API路由
app.use('/api/auth', (req, res) => {
    res.json({
        success: true,
        message: 'Auth API - Coming soon',
        endpoints: ['POST /login', 'POST /register', 'GET /profile']
    });
});
app.use('/api/exchanges', (req, res) => {
    res.json({
        success: true,
        message: 'Exchanges API - Coming soon',
        supported: ['binance', 'okx', 'htx', 'pancakeswap', 'uniswap']
    });
});
app.use('/api/strategies', (req, res) => {
    res.json({
        success: true,
        message: 'Strategies API - Coming soon',
        types: ['grid', 'trend_following', 'ai', 'dex_grid']
    });
});
app.use('/api/wallets', (req, res) => {
    res.json({
        success: true,
        message: 'Wallets API - Coming soon',
        networks: ['ethereum', 'bsc', 'base', 'solana']
    });
});
app.use('/api/rewards', (req, res) => {
    res.json({
        success: true,
        message: 'Rewards API - Coming soon',
        features: ['trading_rewards', 'referral', 'achievements']
    });
});
app.use('/api/orders', (req, res) => {
    res.json({ success: true, message: 'Orders API - Coming soon' });
});
app.use('/api/trades', (req, res) => {
    res.json({ success: true, message: 'Trades API - Coming soon' });
});
app.use('/api/subscriptions', (req, res) => {
    res.json({ success: true, message: 'Subscriptions API - Coming soon' });
});
// 404 处理
app.use('/api/*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'API endpoint not found',
        path: req.path
    });
});
// 生产环境下的前端路由处理
if (process.env.NODE_ENV === 'production') {
    app.get('*', (req, res) => {
        const indexPath = path_1.default.join(__dirname, '../../client/dist/index.html');
        if (fs_1.default.existsSync(indexPath)) {
            res.sendFile(indexPath);
        }
        else {
            res.status(404).send('Frontend not built. Please run: npm run build');
        }
    });
}
// 全局错误处理
app.use((err, req, res, next) => {
    log.error('Unhandled error:', err);
    res.status(err.status || 500).json({
        success: false,
        message: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
        ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
    });
});
// WebSocket 连接处理
io.on('connection', (socket) => {
    log.info(`Client connected: ${socket.id}`);
    socket.on('disconnect', () => {
        log.info(`Client disconnected: ${socket.id}`);
    });
    socket.on('authenticate', (token) => {
        socket.emit('authenticated', { success: true, message: 'Authentication coming soon' });
    });
    socket.on('subscribe', (data) => {
        const { channel, symbol } = data;
        socket.join(`${channel}:${symbol}`);
        log.info(`Client ${socket.id} subscribed to ${channel}:${symbol}`);
        socket.emit('data', {
            channel,
            symbol,
            price: Math.random() * 50000 + 30000,
            timestamp: new Date().toISOString()
        });
    });
    socket.on('unsubscribe', (data) => {
        const { channel, symbol } = data;
        socket.leave(`${channel}:${symbol}`);
        log.info(`Client ${socket.id} unsubscribed from ${channel}:${symbol}`);
    });
});
// 启动服务器
const startServer = async () => {
    try {
        log.info('Starting TradeAI Bot server...');
        server.listen(PORT, () => {
            log.info(`🚀 TradeAI Bot server is running on port ${PORT}`);
            log.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
            log.info(`🔗 Health check: http://localhost:${PORT}/health`);
            log.info(`🔗 API Base: http://localhost:${PORT}/api`);
            if (process.env.NODE_ENV !== 'production') {
                log.info(`🌐 Frontend: http://localhost:5173`);
            }
            log.info('✅ Server started successfully!');
        });
    }
    catch (error) {
        log.error('Failed to start server:', error);
        process.exit(1);
    }
};
// 优雅关闭
process.on('SIGTERM', async () => {
    log.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        log.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', async () => {
    log.info('SIGINT received, shutting down gracefully');
    server.close(() => {
        log.info('Process terminated');
        process.exit(0);
    });
});
// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    log.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    log.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// 启动服务器
startServer();
//# sourceMappingURL=index.js.map
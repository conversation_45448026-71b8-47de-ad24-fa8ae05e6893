import { User } from '../../shared/types';
export declare class UserService {
    static getUserById(userId: string): Promise<User | null>;
    static getUsers(page?: number, limit?: number, search?: string): Promise<{
        users: User[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    static updateUser(userId: string, updates: Partial<User>): Promise<User>;
    static changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void>;
    static deleteUser(userId: string): Promise<void>;
    static upgradeToPremium(userId: string, subscriptionExpiry: Date): Promise<void>;
    static downgradePremium(userId: string): Promise<void>;
    static getUserStats(userId: string): Promise<{
        totalStrategies: number;
        activeStrategies: number;
        totalTrades: number;
        totalProfit: number;
        totalRewards: number;
    }>;
    static checkUserPermissions(userId: string, feature: string): Promise<boolean>;
    static updateUserRewards(userId: string, amount: number): Promise<void>;
}
//# sourceMappingURL=userService.d.ts.map
import { BlockchainNetwork } from '../../shared/types';
export interface WalletAddress {
    id: string;
    userId: string;
    network: BlockchainNetwork;
    address: string;
    privateKey: string;
    isActive: boolean;
    balance: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare class WalletService {
    /**
     * 生成新钱包
     * @param network 区块链网络
     * @returns 钱包信息
     */
    static generateWallet(network: BlockchainNetwork): {
        address: string;
        privateKey: string;
    };
    /**
     * 从私钥恢复钱包
     * @param privateKey 私钥
     * @param network 区块链网络
     * @returns 钱包地址
     */
    static recoverWallet(privateKey: string, network: BlockchainNetwork): string;
    /**
     * 添加钱包地址
     * @param userId 用户ID
     * @param network 区块链网络
     * @param privateKey 私钥（可选，如果不提供则生成新钱包）
     * @returns 钱包地址信息
     */
    static addWalletAddress(userId: string, network: BlockchainNetwork, privateKey?: string): Promise<WalletAddress>;
    /**
     * 获取用户的钱包地址列表
     * @param userId 用户ID
     * @param network 区块链网络（可选）
     * @returns 钱包地址列表
     */
    static getUserWalletAddresses(userId: string, network?: BlockchainNetwork): Promise<WalletAddress[]>;
    /**
     * 获取解密的私钥（用于交易）
     * @param userId 用户ID
     * @param walletId 钱包ID
     * @returns 解密的私钥
     */
    static getDecryptedPrivateKey(userId: string, walletId: string): Promise<{
        address: string;
        privateKey: string;
        network: BlockchainNetwork;
    } | null>;
    /**
     * 更新钱包余额
     * @param walletId 钱包ID
     * @param balance 新余额
     */
    static updateWalletBalance(walletId: string, balance: number): Promise<void>;
    /**
     * 删除钱包地址
     * @param userId 用户ID
     * @param walletId 钱包ID
     */
    static deleteWalletAddress(userId: string, walletId: string): Promise<void>;
    /**
     * 激活/停用钱包地址
     * @param userId 用户ID
     * @param walletId 钱包ID
     * @param isActive 是否激活
     */
    static toggleWalletAddress(userId: string, walletId: string, isActive: boolean): Promise<WalletAddress>;
    /**
     * 从CSV文件导入钱包地址
     * @param userId 用户ID
     * @param csvContent CSV文件内容
     * @param password 解密密码
     * @returns 导入结果
     */
    static importWalletsFromCSV(userId: string, csvContent: string, password: string): Promise<{
        success: number;
        failed: number;
        errors: string[];
    }>;
    /**
     * 导出钱包地址到CSV
     * @param userId 用户ID
     * @param password 加密密码
     * @param network 区块链网络（可选）
     * @returns 加密的CSV内容
     */
    static exportWalletsToCSV(userId: string, password: string, network?: BlockchainNetwork): Promise<string>;
    /**
     * 获取用户钱包统计信息
     * @param userId 用户ID
     * @returns 统计信息
     */
    static getUserWalletStats(userId: string): Promise<{
        totalWallets: number;
        activeWallets: number;
        networkStats: Record<BlockchainNetwork, number>;
        totalBalance: Record<BlockchainNetwork, number>;
    }>;
}
//# sourceMappingURL=walletService.d.ts.map
{"version": 3, "file": "exchangeService.js", "sourceRoot": "", "sources": ["../../../src/server/services/exchangeService.ts"], "names": [], "mappings": ";;;AAAA,kEAA+D;AAC/D,mDAAgD;AAChD,4CAAmD;AACnD,8CAAiD;AAWjD,MAAa,eAAe;IAG1B;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,QAAqB;QAChE,OAAO;QACP,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,eAAe,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;YAChD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACzB,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,aAAa;QACb,MAAM,WAAW,GAAG,MAAM,6BAAa,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC7E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,OAAO;QACP,MAAM,MAAM,GAAmB;YAC7B,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,GAAG,iCAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC;SAC9C,CAAC;QAEF,UAAU;QACV,MAAM,gBAAgB,GAAG,MAAM,iCAAe,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEhF,OAAO;QACP,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAEhE,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,oBAAoB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEnE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAc,EAAE,QAAqB;QAK3E,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,cAAc,EAAE,CAAC;YAE5D,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,uBAAuB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEtE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,uBAAuB;gBAChC,WAAW;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAEzE,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,sBAAsB,EAAE;gBACrD,QAAQ;gBACR,KAAK,EAAE,OAAO;aACf,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO;aACR,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,MAAc,EACd,QAAqB,EACrB,KAAc;QAEd,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEtE,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,MAAM,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,gBAAgB,CAAC,WAAW,EAAE,CAAC;YAC9C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,SAAS,CACpB,MAAc,EACd,QAAqB,EACrB,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,OAAO,MAAM,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,MAAc,EACd,QAAqB,EACrB,MAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAEzD,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE;gBAC9C,QAAQ;gBACR,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAEjF,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,uBAAuB,EAAE;gBACtD,QAAQ;gBACR,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,MAAc,EACd,QAAqB,EACrB,OAAe,EACf,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAElE,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,iBAAiB,EAAE;gBAChD,QAAQ;gBACR,OAAO;gBACP,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,OAAO,aAAa,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CACxB,MAAc,EACd,QAAqB,EACrB,MAAe;QAEf,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,OAAO,MAAM,gBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,eAAe,CAC1B,MAAc,EACd,QAAqB,EACrB,MAAe,EACf,KAAc;QAEd,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,OAAO,MAAM,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACtF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,QAAqB;QAC3D,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,OAAO,MAAM,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,MAAc,EACd,QAAqB,EACrB,MAAe;QAEf,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtE,OAAO,MAAM,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,MAAM,OAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,QAAsB;QACxE,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC5B,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACjC,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,uBAAuB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,YAAY;YACZ,KAAK,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,eAAe,EAAE,CAAC;gBACvD,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC5B,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,uBAAuB,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;YACtF,CAAC;YACD,eAAe,CAAC,KAAK,EAAE,CAAC;QAC1B,CAAC;QAED,uBAAuB;QACvB,IAAI,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,yBAAyB,CAAC,MAAc;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe;YAAE,OAAO,EAAE,CAAC;QAEhC,OAAO,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC1D,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/C,OAAO,QAAQ,EAAE,WAAW,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,cAAc;QAKnB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,MAAM,aAAa,GAAgC,EAAS,CAAC;QAE7D,QAAQ;QACR,MAAM,CAAC,MAAM,CAAC,mBAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5C,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,QAAQ;QACR,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YAC1D,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,eAAe,EAAE,CAAC;gBACnD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;oBACzB,gBAAgB,EAAE,CAAC;oBACnB,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACnC,gBAAgB;YAChB,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,4BAA4B;QACvC,KAAK,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3D,MAAM,qBAAqB,GAAkB,EAAE,CAAC;YAEhD,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,eAAe,EAAE,CAAC;gBACnD,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;oBAC1B,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YAED,UAAU;YACV,KAAK,MAAM,QAAQ,IAAI,qBAAqB,EAAE,CAAC;gBAC7C,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACjC,eAAM,CAAC,IAAI,CAAC,2BAA2B,QAAQ,sBAAsB,MAAM,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,oBAAoB;YACpB,IAAI,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;;AAnYH,0CAoYC;AAnYgB,6BAAa,GAA6C,IAAI,GAAG,EAAE,CAAC"}
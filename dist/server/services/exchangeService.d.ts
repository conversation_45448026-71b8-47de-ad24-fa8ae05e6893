import { CEXExchange } from '../../shared/types';
import { IExchange, ExchangeAccountInfo, ExchangeBalance, ExchangeTicker, CreateOrderParams, ExchangeOrder } from '../exchanges/base/ExchangeInterface';
export declare class ExchangeService {
    private static userExchanges;
    /**
     * 获取用户的交易所实例
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @returns 交易所实例
     */
    static getUserExchange(userId: string, exchange: CEXExchange): Promise<IExchange>;
    /**
     * 测试用户的交易所连接
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @returns 测试结果
     */
    static testUserExchangeConnection(userId: string, exchange: CEXExchange): Promise<{
        success: boolean;
        message: string;
        accountInfo?: ExchangeAccountInfo;
    }>;
    /**
     * 获取用户在指定交易所的余额
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param asset 资产符号（可选）
     * @returns 余额信息
     */
    static getUserBalance(userId: string, exchange: CEXExchange, asset?: string): Promise<ExchangeBalance | ExchangeBalance[]>;
    /**
     * 获取交易对行情
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号
     * @returns 行情信息
     */
    static getTicker(userId: string, exchange: CEXExchange, symbol: string): Promise<ExchangeTicker>;
    /**
     * 创建订单
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param params 订单参数
     * @returns 订单信息
     */
    static createOrder(userId: string, exchange: CEXExchange, params: CreateOrderParams): Promise<ExchangeOrder>;
    /**
     * 取消订单
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param orderId 订单ID
     * @param symbol 交易对符号
     * @returns 订单信息
     */
    static cancelOrder(userId: string, exchange: CEXExchange, orderId: string, symbol: string): Promise<ExchangeOrder>;
    /**
     * 获取用户的开放订单
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号（可选）
     * @returns 订单列表
     */
    static getOpenOrders(userId: string, exchange: CEXExchange, symbol?: string): Promise<ExchangeOrder[]>;
    /**
     * 获取用户的订单历史
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号（可选）
     * @param limit 限制数量
     * @returns 订单列表
     */
    static getOrderHistory(userId: string, exchange: CEXExchange, symbol?: string, limit?: number): Promise<ExchangeOrder[]>;
    /**
     * 获取交易所支持的交易对
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @returns 交易对列表
     */
    static getSymbols(userId: string, exchange: CEXExchange): Promise<string[]>;
    /**
     * 获取交易费率
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号（可选）
     * @returns 费率信息
     */
    static getTradingFees(userId: string, exchange: CEXExchange, symbol?: string): Promise<{
        maker: number;
        taker: number;
    }>;
    /**
     * 断开用户的交易所连接
     * @param userId 用户ID
     * @param exchange 交易所类型（可选，不指定则断开所有）
     */
    static disconnectUserExchange(userId: string, exchange?: CEXExchange): Promise<void>;
    /**
     * 获取用户已连接的交易所列表
     * @param userId 用户ID
     * @returns 已连接的交易所列表
     */
    static getUserConnectedExchanges(userId: string): CEXExchange[];
    /**
     * 获取系统统计信息
     * @returns 统计信息
     */
    static getSystemStats(): {
        totalUsers: number;
        totalConnections: number;
        exchangeStats: Record<CEXExchange, number>;
    };
    /**
     * 清理断开的连接
     */
    static cleanupDisconnectedExchanges(): Promise<void>;
}
//# sourceMappingURL=exchangeService.d.ts.map
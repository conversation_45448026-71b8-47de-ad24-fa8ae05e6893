"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIKeyService = void 0;
const database_1 = require("../models/database");
const encryption_1 = require("../utils/encryption");
const logger_1 = require("../utils/logger");
class APIKeyService {
    // 添加API密钥
    static async addAPIKey(userId, exchange, apiKey, secretKey, passphrase) {
        try {
            // 检查是否已存在该交易所的API密钥
            const existingKey = await database_1.db.query('SELECT id FROM api_keys WHERE user_id = $1 AND exchange = $2 AND is_active = true', [userId, exchange]);
            if (existingKey.rows.length > 0) {
                throw new Error(`API key for ${exchange} already exists`);
            }
            // 加密敏感数据
            const encryptedApiKey = encryption_1.EncryptionUtils.encryptSensitiveData(apiKey);
            const encryptedSecretKey = encryption_1.EncryptionUtils.encryptSensitiveData(secretKey);
            const encryptedPassphrase = passphrase ? encryption_1.EncryptionUtils.encryptSensitiveData(passphrase) : null;
            // 创建加密数据对象
            const encryptedData = encryption_1.EncryptionUtils.encryptSensitiveData(JSON.stringify({
                apiKey: encryptedApiKey,
                secretKey: encryptedSecretKey,
                passphrase: encryptedPassphrase
            }));
            // 存储到数据库
            const result = await database_1.db.query(`INSERT INTO api_keys (user_id, exchange, api_key, secret_key, passphrase, encrypted_data, is_active) 
         VALUES ($1, $2, $3, $4, $5, $6, $7) 
         RETURNING id, user_id, exchange, is_active, created_at, updated_at`, [userId, exchange, encryptedApiKey, encryptedSecretKey, encryptedPassphrase, encryptedData, true]);
            const apiKeyRecord = result.rows[0];
            logger_1.logUtils.logUserAction(userId, 'api_key_added', { exchange });
            return {
                id: apiKeyRecord.id,
                userId: apiKeyRecord.user_id,
                exchange: apiKeyRecord.exchange,
                apiKey: '***masked***',
                secretKey: '***masked***',
                passphrase: passphrase ? '***masked***' : undefined,
                isActive: apiKeyRecord.is_active,
                encryptedData: apiKeyRecord.encrypted_data,
                createdAt: apiKeyRecord.created_at,
                updatedAt: apiKeyRecord.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error adding API key:', error);
            throw error;
        }
    }
    // 获取用户的API密钥列表
    static async getUserAPIKeys(userId) {
        try {
            const result = await database_1.db.query('SELECT id, user_id, exchange, is_active, created_at, updated_at FROM api_keys WHERE user_id = $1 ORDER BY created_at DESC', [userId]);
            return result.rows.map(row => ({
                id: row.id,
                userId: row.user_id,
                exchange: row.exchange,
                apiKey: '***masked***',
                secretKey: '***masked***',
                isActive: row.is_active,
                encryptedData: '',
                createdAt: row.created_at,
                updatedAt: row.updated_at
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting user API keys:', error);
            throw new Error('Failed to get API keys');
        }
    }
    // 获取解密的API密钥（用于交易）
    static async getDecryptedAPIKey(userId, exchange) {
        try {
            const result = await database_1.db.query('SELECT encrypted_data FROM api_keys WHERE user_id = $1 AND exchange = $2 AND is_active = true', [userId, exchange]);
            if (result.rows.length === 0) {
                return null;
            }
            const encryptedData = result.rows[0].encrypted_data;
            const decryptedDataStr = encryption_1.EncryptionUtils.decryptSensitiveData(encryptedData);
            const decryptedData = JSON.parse(decryptedDataStr);
            return {
                apiKey: encryption_1.EncryptionUtils.decryptSensitiveData(decryptedData.apiKey),
                secretKey: encryption_1.EncryptionUtils.decryptSensitiveData(decryptedData.secretKey),
                passphrase: decryptedData.passphrase ? encryption_1.EncryptionUtils.decryptSensitiveData(decryptedData.passphrase) : undefined
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting decrypted API key:', error);
            throw new Error('Failed to decrypt API key');
        }
    }
    // 更新API密钥
    static async updateAPIKey(userId, apiKeyId, updates) {
        try {
            // 验证API密钥属于该用户
            const existingKey = await database_1.db.query('SELECT exchange, encrypted_data FROM api_keys WHERE id = $1 AND user_id = $2', [apiKeyId, userId]);
            if (existingKey.rows.length === 0) {
                throw new Error('API key not found');
            }
            const exchange = existingKey.rows[0].exchange;
            let encryptedData = existingKey.rows[0].encrypted_data;
            // 如果更新了密钥信息，重新加密
            if (updates.apiKey || updates.secretKey || updates.passphrase !== undefined) {
                const currentData = JSON.parse(encryption_1.EncryptionUtils.decryptSensitiveData(encryptedData));
                const newData = {
                    apiKey: updates.apiKey ? encryption_1.EncryptionUtils.encryptSensitiveData(updates.apiKey) : currentData.apiKey,
                    secretKey: updates.secretKey ? encryption_1.EncryptionUtils.encryptSensitiveData(updates.secretKey) : currentData.secretKey,
                    passphrase: updates.passphrase !== undefined
                        ? (updates.passphrase ? encryption_1.EncryptionUtils.encryptSensitiveData(updates.passphrase) : null)
                        : currentData.passphrase
                };
                encryptedData = encryption_1.EncryptionUtils.encryptSensitiveData(JSON.stringify(newData));
            }
            // 构建更新查询
            const updateFields = [];
            const updateValues = [];
            let paramIndex = 1;
            if (updates.apiKey) {
                updateFields.push(`api_key = $${paramIndex}`);
                updateValues.push(encryption_1.EncryptionUtils.encryptSensitiveData(updates.apiKey));
                paramIndex++;
            }
            if (updates.secretKey) {
                updateFields.push(`secret_key = $${paramIndex}`);
                updateValues.push(encryption_1.EncryptionUtils.encryptSensitiveData(updates.secretKey));
                paramIndex++;
            }
            if (updates.passphrase !== undefined) {
                updateFields.push(`passphrase = $${paramIndex}`);
                updateValues.push(updates.passphrase ? encryption_1.EncryptionUtils.encryptSensitiveData(updates.passphrase) : null);
                paramIndex++;
            }
            if (updates.isActive !== undefined) {
                updateFields.push(`is_active = $${paramIndex}`);
                updateValues.push(updates.isActive);
                paramIndex++;
            }
            updateFields.push(`encrypted_data = $${paramIndex}`);
            updateValues.push(encryptedData);
            paramIndex++;
            updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
            updateValues.push(apiKeyId, userId);
            const result = await database_1.db.query(`UPDATE api_keys SET ${updateFields.join(', ')} 
         WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1}
         RETURNING id, user_id, exchange, is_active, created_at, updated_at`, updateValues);
            const updatedKey = result.rows[0];
            logger_1.logUtils.logUserAction(userId, 'api_key_updated', { apiKeyId, exchange, updates: Object.keys(updates) });
            return {
                id: updatedKey.id,
                userId: updatedKey.user_id,
                exchange: updatedKey.exchange,
                apiKey: '***masked***',
                secretKey: '***masked***',
                isActive: updatedKey.is_active,
                encryptedData: '',
                createdAt: updatedKey.created_at,
                updatedAt: updatedKey.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error updating API key:', error);
            throw error;
        }
    }
    // 删除API密钥
    static async deleteAPIKey(userId, apiKeyId) {
        try {
            const result = await database_1.db.query('DELETE FROM api_keys WHERE id = $1 AND user_id = $2 RETURNING exchange', [apiKeyId, userId]);
            if (result.rowCount === 0) {
                throw new Error('API key not found');
            }
            const exchange = result.rows[0].exchange;
            logger_1.logUtils.logUserAction(userId, 'api_key_deleted', { apiKeyId, exchange });
        }
        catch (error) {
            logger_1.logger.error('Error deleting API key:', error);
            throw error;
        }
    }
    // 测试API密钥连接
    static async testAPIKey(userId, exchange) {
        try {
            const credentials = await this.getDecryptedAPIKey(userId, exchange);
            if (!credentials) {
                return {
                    success: false,
                    message: 'API key not found'
                };
            }
            // 这里需要实现具体的交易所API测试逻辑
            // 暂时返回模拟结果
            const testResult = await this.performAPITest(exchange, credentials);
            logger_1.logUtils.logUserAction(userId, 'api_key_tested', {
                exchange,
                success: testResult.success
            });
            return testResult;
        }
        catch (error) {
            logger_1.logger.error('Error testing API key:', error);
            return {
                success: false,
                message: 'Failed to test API key'
            };
        }
    }
    // 执行API测试（需要根据具体交易所实现）
    static async performAPITest(exchange, credentials) {
        // 这里应该实现具体的交易所API调用
        // 例如获取账户信息来验证API密钥是否有效
        // 模拟实现
        try {
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            // 模拟成功响应
            return {
                success: true,
                message: 'API key is valid',
                accountInfo: {
                    exchange,
                    permissions: ['spot', 'futures'],
                    balances: []
                }
            };
        }
        catch (error) {
            return {
                success: false,
                message: 'Invalid API key or insufficient permissions'
            };
        }
    }
    // 获取所有活跃的API密钥（用于系统监控）
    static async getActiveAPIKeys() {
        try {
            const result = await database_1.db.query('SELECT exchange, COUNT(*) as count FROM api_keys WHERE is_active = true GROUP BY exchange');
            return result.rows.map(row => ({
                exchange: row.exchange,
                count: parseInt(row.count)
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting active API keys:', error);
            throw new Error('Failed to get active API keys');
        }
    }
}
exports.APIKeyService = APIKeyService;
//# sourceMappingURL=apiKeyService.js.map
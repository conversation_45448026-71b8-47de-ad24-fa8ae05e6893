"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExchangeService = void 0;
const ExchangeFactory_1 = require("../exchanges/ExchangeFactory");
const apiKeyService_1 = require("./apiKeyService");
const logger_1 = require("../utils/logger");
const types_1 = require("../../shared/types");
class ExchangeService {
    /**
     * 获取用户的交易所实例
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @returns 交易所实例
     */
    static async getUserExchange(userId, exchange) {
        // 检查缓存
        const userExchangeMap = this.userExchanges.get(userId);
        if (userExchangeMap?.has(exchange)) {
            const instance = userExchangeMap.get(exchange);
            if (instance.isConnected) {
                return instance;
            }
        }
        // 获取用户的API密钥
        const credentials = await apiKeyService_1.APIKeyService.getDecryptedAPIKey(userId, exchange);
        if (!credentials) {
            throw new Error(`No API key found for ${exchange}`);
        }
        // 创建配置
        const config = {
            apiKey: credentials.apiKey,
            secretKey: credentials.secretKey,
            passphrase: credentials.passphrase,
            ...ExchangeFactory_1.ExchangeFactory.getDefaultConfig(exchange)
        };
        // 创建交易所实例
        const exchangeInstance = await ExchangeFactory_1.ExchangeFactory.createExchange(exchange, config);
        // 缓存实例
        if (!this.userExchanges.has(userId)) {
            this.userExchanges.set(userId, new Map());
        }
        this.userExchanges.get(userId).set(exchange, exchangeInstance);
        logger_1.logUtils.logUserAction(userId, 'exchange_connected', { exchange });
        return exchangeInstance;
    }
    /**
     * 测试用户的交易所连接
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @returns 测试结果
     */
    static async testUserExchangeConnection(userId, exchange) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            const accountInfo = await exchangeInstance.getAccountInfo();
            logger_1.logUtils.logUserAction(userId, 'exchange_test_success', { exchange });
            return {
                success: true,
                message: 'Connection successful',
                accountInfo
            };
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logUtils.logUserAction(userId, 'exchange_test_failed', {
                exchange,
                error: message
            });
            return {
                success: false,
                message
            };
        }
    }
    /**
     * 获取用户在指定交易所的余额
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param asset 资产符号（可选）
     * @returns 余额信息
     */
    static async getUserBalance(userId, exchange, asset) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            if (asset) {
                return await exchangeInstance.getBalance(asset);
            }
            else {
                return await exchangeInstance.getBalances();
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to get balance for user ${userId} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 获取交易对行情
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号
     * @returns 行情信息
     */
    static async getTicker(userId, exchange, symbol) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            return await exchangeInstance.getTicker(symbol);
        }
        catch (error) {
            logger_1.logger.error(`Failed to get ticker for ${symbol} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 创建订单
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param params 订单参数
     * @returns 订单信息
     */
    static async createOrder(userId, exchange, params) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            const order = await exchangeInstance.createOrder(params);
            logger_1.logUtils.logUserAction(userId, 'order_created', {
                exchange,
                symbol: params.symbol,
                side: params.side,
                amount: params.amount,
                orderId: order.id
            });
            return order;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create order for user ${userId} on ${exchange}:`, error);
            logger_1.logUtils.logUserAction(userId, 'order_creation_failed', {
                exchange,
                symbol: params.symbol,
                side: params.side,
                amount: params.amount,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }
    /**
     * 取消订单
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param orderId 订单ID
     * @param symbol 交易对符号
     * @returns 订单信息
     */
    static async cancelOrder(userId, exchange, orderId, symbol) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            const order = await exchangeInstance.cancelOrder(orderId, symbol);
            logger_1.logUtils.logUserAction(userId, 'order_cancelled', {
                exchange,
                orderId,
                symbol
            });
            return order;
        }
        catch (error) {
            logger_1.logger.error(`Failed to cancel order ${orderId} for user ${userId} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 获取用户的开放订单
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号（可选）
     * @returns 订单列表
     */
    static async getOpenOrders(userId, exchange, symbol) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            return await exchangeInstance.getOpenOrders(symbol);
        }
        catch (error) {
            logger_1.logger.error(`Failed to get open orders for user ${userId} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 获取用户的订单历史
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号（可选）
     * @param limit 限制数量
     * @returns 订单列表
     */
    static async getOrderHistory(userId, exchange, symbol, limit) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            return await exchangeInstance.getOrderHistory(symbol, limit);
        }
        catch (error) {
            logger_1.logger.error(`Failed to get order history for user ${userId} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 获取交易所支持的交易对
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @returns 交易对列表
     */
    static async getSymbols(userId, exchange) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            return await exchangeInstance.getSymbols();
        }
        catch (error) {
            logger_1.logger.error(`Failed to get symbols for user ${userId} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 获取交易费率
     * @param userId 用户ID
     * @param exchange 交易所类型
     * @param symbol 交易对符号（可选）
     * @returns 费率信息
     */
    static async getTradingFees(userId, exchange, symbol) {
        try {
            const exchangeInstance = await this.getUserExchange(userId, exchange);
            return await exchangeInstance.getTradingFees(symbol);
        }
        catch (error) {
            logger_1.logger.error(`Failed to get trading fees for user ${userId} on ${exchange}:`, error);
            throw error;
        }
    }
    /**
     * 断开用户的交易所连接
     * @param userId 用户ID
     * @param exchange 交易所类型（可选，不指定则断开所有）
     */
    static async disconnectUserExchange(userId, exchange) {
        const userExchangeMap = this.userExchanges.get(userId);
        if (!userExchangeMap)
            return;
        if (exchange) {
            const instance = userExchangeMap.get(exchange);
            if (instance) {
                await instance.disconnect();
                userExchangeMap.delete(exchange);
                logger_1.logUtils.logUserAction(userId, 'exchange_disconnected', { exchange });
            }
        }
        else {
            // 断开所有交易所连接
            for (const [exchangeType, instance] of userExchangeMap) {
                await instance.disconnect();
                logger_1.logUtils.logUserAction(userId, 'exchange_disconnected', { exchange: exchangeType });
            }
            userExchangeMap.clear();
        }
        // 如果用户没有任何交易所连接，移除用户记录
        if (userExchangeMap.size === 0) {
            this.userExchanges.delete(userId);
        }
    }
    /**
     * 获取用户已连接的交易所列表
     * @param userId 用户ID
     * @returns 已连接的交易所列表
     */
    static getUserConnectedExchanges(userId) {
        const userExchangeMap = this.userExchanges.get(userId);
        if (!userExchangeMap)
            return [];
        return Array.from(userExchangeMap.keys()).filter(exchange => {
            const instance = userExchangeMap.get(exchange);
            return instance?.isConnected;
        });
    }
    /**
     * 获取系统统计信息
     * @returns 统计信息
     */
    static getSystemStats() {
        let totalConnections = 0;
        const exchangeStats = {};
        // 初始化统计
        Object.values(types_1.CEXExchange).forEach(exchange => {
            exchangeStats[exchange] = 0;
        });
        // 统计连接数
        for (const userExchangeMap of this.userExchanges.values()) {
            for (const [exchange, instance] of userExchangeMap) {
                if (instance.isConnected) {
                    totalConnections++;
                    exchangeStats[exchange]++;
                }
            }
        }
        return {
            totalUsers: this.userExchanges.size,
            totalConnections,
            exchangeStats
        };
    }
    /**
     * 清理断开的连接
     */
    static async cleanupDisconnectedExchanges() {
        for (const [userId, userExchangeMap] of this.userExchanges) {
            const disconnectedExchanges = [];
            for (const [exchange, instance] of userExchangeMap) {
                if (!instance.isConnected) {
                    disconnectedExchanges.push(exchange);
                }
            }
            // 移除断开的连接
            for (const exchange of disconnectedExchanges) {
                userExchangeMap.delete(exchange);
                logger_1.logger.info(`Cleaned up disconnected ${exchange} exchange for user ${userId}`);
            }
            // 如果用户没有任何连接，移除用户记录
            if (userExchangeMap.size === 0) {
                this.userExchanges.delete(userId);
            }
        }
    }
}
exports.ExchangeService = ExchangeService;
ExchangeService.userExchanges = new Map();
//# sourceMappingURL=exchangeService.js.map
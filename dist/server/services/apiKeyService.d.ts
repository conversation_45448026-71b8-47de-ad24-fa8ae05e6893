import { APIKey, CEXExchange } from '../../shared/types';
export declare class APIKeyService {
    static addAPIKey(userId: string, exchange: CEXExchange, apiKey: string, secretKey: string, passphrase?: string): Promise<APIKey>;
    static getUserAPIKeys(userId: string): Promise<APIKey[]>;
    static getDecryptedAPIKey(userId: string, exchange: CEXExchange): Promise<{
        apiKey: string;
        secretKey: string;
        passphrase?: string;
    } | null>;
    static updateAPIKey(userId: string, apiKeyId: string, updates: {
        apiKey?: string;
        secretKey?: string;
        passphrase?: string;
        isActive?: boolean;
    }): Promise<APIKey>;
    static deleteAPIKey(userId: string, apiKeyId: string): Promise<void>;
    static testAPIKey(userId: string, exchange: CEXExchange): Promise<{
        success: boolean;
        message: string;
        accountInfo?: any;
    }>;
    private static performAPITest;
    static getActiveAPIKeys(): Promise<{
        exchange: CEXExchange;
        count: number;
    }[]>;
}
//# sourceMappingURL=apiKeyService.d.ts.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const exchangeService_1 = require("../services/exchangeService");
const apiKeyService_1 = require("../services/apiKeyService");
const ExchangeFactory_1 = require("../exchanges/ExchangeFactory");
const logger_1 = require("../utils/logger");
const types_1 = require("../../shared/types");
const router = express_1.default.Router();
// 应用认证中间件
router.use(auth_1.authenticateToken);
// 获取支持的交易所列表
router.get('/supported', async (req, res) => {
    try {
        const supportedExchanges = ExchangeFactory_1.ExchangeFactory.getSupportedExchanges();
        res.json({
            success: true,
            data: supportedExchanges.map(exchange => ({
                name: exchange,
                displayName: exchange.charAt(0).toUpperCase() + exchange.slice(1),
                isSupported: true
            }))
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting supported exchanges:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get supported exchanges'
        });
    }
});
// 获取用户已连接的交易所
router.get('/connected', async (req, res) => {
    try {
        const userId = req.user.id;
        const connectedExchanges = exchangeService_1.ExchangeService.getUserConnectedExchanges(userId);
        const userApiKeys = await apiKeyService_1.APIKeyService.getUserAPIKeys(userId);
        const exchangeStatus = userApiKeys.map(apiKey => ({
            exchange: apiKey.exchange,
            isConnected: connectedExchanges.includes(apiKey.exchange),
            isActive: apiKey.isActive,
            createdAt: apiKey.createdAt
        }));
        res.json({
            success: true,
            data: exchangeStatus
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting connected exchanges:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get connected exchanges'
        });
    }
});
// 测试交易所连接
router.post('/test/:exchange', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange))
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const result = await exchangeService_1.ExchangeService.testUserExchangeConnection(userId, exchange);
        res.json({
            success: result.success,
            message: result.message,
            data: result.accountInfo
        });
    }
    catch (error) {
        logger_1.logger.error('Error testing exchange connection:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test exchange connection'
        });
    }
});
// 获取账户信息
router.get('/:exchange/account', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange))
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const exchangeInstance = await exchangeService_1.ExchangeService.getUserExchange(userId, exchange);
        const accountInfo = await exchangeInstance.getAccountInfo();
        res.json({
            success: true,
            data: accountInfo
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting account info:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get account information'
        });
    }
});
// 获取余额
router.get('/:exchange/balances', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange)),
    (0, express_validator_1.query)('asset').optional().isString()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const asset = req.query.asset;
        const balances = await exchangeService_1.ExchangeService.getUserBalance(userId, exchange, asset);
        res.json({
            success: true,
            data: balances
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting balances:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get balances'
        });
    }
});
// 获取交易对列表
router.get('/:exchange/symbols', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange))
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const symbols = await exchangeService_1.ExchangeService.getSymbols(userId, exchange);
        res.json({
            success: true,
            data: symbols
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting symbols:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get symbols'
        });
    }
});
// 获取行情数据
router.get('/:exchange/ticker/:symbol', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange)),
    (0, express_validator_1.param)('symbol').isString().notEmpty()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const symbol = req.params.symbol;
        const ticker = await exchangeService_1.ExchangeService.getTicker(userId, exchange, symbol);
        res.json({
            success: true,
            data: ticker
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting ticker:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get ticker data'
        });
    }
});
// 获取开放订单
router.get('/:exchange/orders/open', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange)),
    (0, express_validator_1.query)('symbol').optional().isString()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const symbol = req.query.symbol;
        const orders = await exchangeService_1.ExchangeService.getOpenOrders(userId, exchange, symbol);
        res.json({
            success: true,
            data: orders
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting open orders:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get open orders'
        });
    }
});
// 获取订单历史
router.get('/:exchange/orders/history', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange)),
    (0, express_validator_1.query)('symbol').optional().isString(),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 1000 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const symbol = req.query.symbol;
        const limit = req.query.limit ? parseInt(req.query.limit) : undefined;
        const orders = await exchangeService_1.ExchangeService.getOrderHistory(userId, exchange, symbol, limit);
        res.json({
            success: true,
            data: orders
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting order history:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get order history'
        });
    }
});
// 获取交易费率
router.get('/:exchange/fees', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange)),
    (0, express_validator_1.query)('symbol').optional().isString()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        const symbol = req.query.symbol;
        const fees = await exchangeService_1.ExchangeService.getTradingFees(userId, exchange, symbol);
        res.json({
            success: true,
            data: fees
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting trading fees:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get trading fees'
        });
    }
});
// 断开交易所连接
router.post('/:exchange/disconnect', [
    (0, express_validator_1.param)('exchange').isIn(Object.values(types_1.CEXExchange))
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const exchange = req.params.exchange;
        await exchangeService_1.ExchangeService.disconnectUserExchange(userId, exchange);
        res.json({
            success: true,
            message: `Disconnected from ${exchange}`
        });
    }
    catch (error) {
        logger_1.logger.error('Error disconnecting exchange:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to disconnect exchange'
        });
    }
});
// 获取系统统计信息（管理员功能）
router.get('/admin/stats', async (req, res) => {
    try {
        // 检查管理员权限
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
        }
        const stats = exchangeService_1.ExchangeService.getSystemStats();
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting system stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get system statistics'
        });
    }
});
module.exports = router;
//# sourceMappingURL=exchanges.js.map
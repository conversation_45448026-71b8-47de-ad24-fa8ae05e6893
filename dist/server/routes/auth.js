"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const database_1 = require("../models/database");
const encryption_1 = require("../utils/encryption");
const logger_1 = require("../utils/logger");
const types_1 = require("../../shared/types");
const router = express_1.default.Router();
// 注册
router.post('/register', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
    (0, express_validator_1.body)('confirmPassword').custom((value, { req }) => {
        if (value !== req.body.password) {
            throw new Error('Password confirmation does not match password');
        }
        return true;
    })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { email, password } = req.body;
        // 检查用户是否已存在
        const existingUser = await database_1.db.query('SELECT id FROM users WHERE email = $1', [email]);
        if (existingUser.rows.length > 0) {
            return res.status(409).json({
                success: false,
                message: 'User already exists'
            });
        }
        // 哈希密码
        const passwordHash = await encryption_1.EncryptionUtils.hashPassword(password);
        // 创建用户
        const result = await database_1.db.query(`INSERT INTO users (email, password_hash, role, is_active, total_rewards) 
       VALUES ($1, $2, $3, $4, $5) 
       RETURNING id, email, role, is_active, created_at`, [email, passwordHash, types_1.UserRole.USER, true, 0]);
        const user = result.rows[0];
        // 生成JWT token
        const token = encryption_1.EncryptionUtils.generateJWTToken({
            userId: user.id,
            email: user.email,
            role: user.role
        });
        logger_1.logUtils.logUserAction(user.id, 'register', { email });
        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: {
                user: {
                    id: user.id,
                    email: user.email,
                    role: user.role,
                    isActive: user.is_active,
                    createdAt: user.created_at
                },
                token
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// 登录
router.post('/login', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').notEmpty()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { email, password } = req.body;
        // 查找用户
        const result = await database_1.db.query('SELECT id, email, password_hash, role, is_active, subscription_expiry FROM users WHERE email = $1', [email]);
        if (result.rows.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }
        const user = result.rows[0];
        // 检查用户是否激活
        if (!user.is_active) {
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated'
            });
        }
        // 验证密码
        const isPasswordValid = await encryption_1.EncryptionUtils.verifyPassword(password, user.password_hash);
        if (!isPasswordValid) {
            logger_1.logUtils.logSecurityEvent('failed_login', { email, ip: req.ip });
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }
        // 生成JWT token
        const token = encryption_1.EncryptionUtils.generateJWTToken({
            userId: user.id,
            email: user.email,
            role: user.role
        });
        // 更新最后登录时间
        await database_1.db.query('UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1', [user.id]);
        logger_1.logUtils.logUserAction(user.id, 'login', { email, ip: req.ip });
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: {
                    id: user.id,
                    email: user.email,
                    role: user.role,
                    isActive: user.is_active,
                    subscriptionExpiry: user.subscription_expiry
                },
                token
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// 刷新token
router.post('/refresh', async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({
                success: false,
                message: 'Token is required'
            });
        }
        // 验证token
        const decoded = encryption_1.EncryptionUtils.verifyJWTToken(token);
        // 检查用户是否仍然存在且激活
        const result = await database_1.db.query('SELECT id, email, role, is_active FROM users WHERE id = $1 AND is_active = true', [decoded.userId]);
        if (result.rows.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'User not found or deactivated'
            });
        }
        const user = result.rows[0];
        // 生成新token
        const newToken = encryption_1.EncryptionUtils.generateJWTToken({
            userId: user.id,
            email: user.email,
            role: user.role
        });
        res.json({
            success: true,
            message: 'Token refreshed successfully',
            data: {
                token: newToken
            }
        });
    }
    catch (error) {
        if (error.message === 'Token expired' || error.message === 'Invalid token') {
            return res.status(401).json({
                success: false,
                message: error.message
            });
        }
        logger_1.logger.error('Token refresh error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// 登出
router.post('/logout', async (req, res) => {
    try {
        // 在实际应用中，这里可以将token加入黑名单
        // 目前只是返回成功响应
        res.json({
            success: true,
            message: 'Logout successful'
        });
    }
    catch (error) {
        logger_1.logger.error('Logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// 忘记密码
router.post('/forgot-password', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { email } = req.body;
        // 检查用户是否存在
        const result = await database_1.db.query('SELECT id FROM users WHERE email = $1 AND is_active = true', [email]);
        // 无论用户是否存在，都返回成功响应（安全考虑）
        res.json({
            success: true,
            message: 'If the email exists, a password reset link has been sent'
        });
        // 如果用户存在，发送重置邮件（这里需要实现邮件服务）
        if (result.rows.length > 0) {
            const userId = result.rows[0].id;
            // TODO: 实现邮件发送逻辑
            logger_1.logUtils.logUserAction(userId, 'password_reset_requested', { email });
        }
    }
    catch (error) {
        logger_1.logger.error('Forgot password error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
module.exports = router;
//# sourceMappingURL=auth.js.map
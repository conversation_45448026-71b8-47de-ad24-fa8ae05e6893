{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/server/routes/auth.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,yDAA2D;AAE3D,iDAAwC;AACxC,oDAAsD;AACtD,4CAAmD;AACnD,8CAAiE;AAEjE,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAEhC,KAAK;AACL,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;IACvB,IAAA,wBAAI,EAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,cAAc,EAAE;IACxC,IAAA,wBAAI,EAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,iEAAiE,CAAC;IAChH,IAAA,wBAAI,EAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QAChD,IAAI,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;CACH,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,EAAE;IACvD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB;gBAC5B,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE;aACR,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAErC,YAAY;QACZ,MAAM,YAAY,GAAG,MAAM,aAAE,CAAC,KAAK,CACjC,uCAAuC,EACvC,CAAC,KAAK,CAAC,CACR,CAAC;QAEF,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qBAAqB;aAChB,CAAC,CAAC;QACpB,CAAC;QAED,OAAO;QACP,MAAM,YAAY,GAAG,MAAM,4BAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAElE,OAAO;QACP,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,KAAK,CAC3B;;wDAEkD,EAClD,CAAC,KAAK,EAAE,YAAY,EAAE,gBAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAC9C,CAAC;QAEF,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE5B,cAAc;QACd,MAAM,KAAK,GAAG,4BAAe,CAAC,gBAAgB,CAAC;YAC7C,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;QAEH,iBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAEvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,8BAA8B;YACvC,IAAI,EAAE;gBACJ,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,SAAS;oBACxB,SAAS,EAAE,IAAI,CAAC,UAAU;iBAC3B;gBACD,KAAK;aACN;SACa,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,KAAK;AACL,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;IACpB,IAAA,wBAAI,EAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,cAAc,EAAE;IACxC,IAAA,wBAAI,EAAC,UAAU,CAAC,CAAC,QAAQ,EAAE;CAC5B,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,EAAE;IACvD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB;gBAC5B,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE;aACR,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAErC,OAAO;QACP,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,KAAK,CAC3B,mGAAmG,EACnG,CAAC,KAAK,CAAC,CACR,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qBAAqB;aAChB,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wBAAwB;aACnB,CAAC,CAAC;QACpB,CAAC;QAED,OAAO;QACP,MAAM,eAAe,GAAG,MAAM,4BAAe,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3F,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,iBAAQ,CAAC,gBAAgB,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YACjE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qBAAqB;aAChB,CAAC,CAAC;QACpB,CAAC;QAED,cAAc;QACd,MAAM,KAAK,GAAG,4BAAe,CAAC,gBAAgB,CAAC;YAC7C,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;QAEH,WAAW;QACX,MAAM,aAAE,CAAC,KAAK,CACZ,+DAA+D,EAC/D,CAAC,IAAI,CAAC,EAAE,CAAC,CACV,CAAC;QAEF,iBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhE,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE;gBACJ,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,SAAS;oBACxB,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;iBAC7C;gBACD,KAAK;aACN;SACa,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACpC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,UAAU;AACV,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,EAAE;IAC5E,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE3B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB;aACd,CAAC,CAAC;QACpB,CAAC;QAED,UAAU;QACV,MAAM,OAAO,GAAG,4BAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAEtD,gBAAgB;QAChB,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,KAAK,CAC3B,iFAAiF,EACjF,CAAC,OAAO,CAAC,MAAM,CAAC,CACjB,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+BAA+B;aAC1B,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE5B,WAAW;QACX,MAAM,QAAQ,GAAG,4BAAe,CAAC,gBAAgB,CAAC;YAChD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,8BAA8B;YACvC,IAAI,EAAE;gBACJ,KAAK,EAAE,QAAQ;aAChB;SACa,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,CAAC,OAAO,KAAK,eAAe,IAAI,KAAK,CAAC,OAAO,KAAK,eAAe,EAAE,CAAC;YAC3E,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO;aACR,CAAC,CAAC;QACpB,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC5C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,KAAK;AACL,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,EAAE;IAC3E,IAAI,CAAC;QACH,yBAAyB;QACzB,aAAa;QAEb,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,mBAAmB;SACd,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACrC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,OAAO;AACP,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC9B,IAAA,wBAAI,EAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,cAAc,EAAE;CACzC,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,EAAE;IACvD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB;gBAC5B,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE;aACR,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE3B,WAAW;QACX,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,KAAK,CAC3B,4DAA4D,EAC5D,CAAC,KAAK,CAAC,CACR,CAAC;QAEF,yBAAyB;QACzB,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,0DAA0D;SACrD,CAAC,CAAC;QAElB,4BAA4B;QAC5B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjC,iBAAiB;YACjB,iBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,0BAA0B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACxE,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SAClB,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC"}
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const RewardService_1 = require("../rewards/services/RewardService");
const logger_1 = require("../utils/logger");
const RewardInterface_1 = require("../rewards/base/RewardInterface");
const router = express_1.default.Router();
const rewardService = RewardService_1.RewardService.getInstance();
// 应用认证中间件
router.use(auth_1.authenticateToken);
// 获取用户奖励列表
router.get('/', [
    (0, express_validator_1.query)('status').optional().isIn(Object.values(RewardInterface_1.RewardStatus)),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('offset').optional().isInt({ min: 0 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const userId = req.user.id;
        const status = req.query.status;
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;
        const rewards = await rewardService.getUserRewards(userId, status, limit, offset);
        res.json({
            success: true,
            data: rewards
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting user rewards:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get rewards'
        });
    }
});
// 获取用户奖励统计
router.get('/stats', async (req, res) => {
    try {
        const userId = req.user.id;
        const stats = await rewardService.getUserRewardStats(userId);
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting reward stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get reward statistics'
        });
    }
});
// 获取单个奖励详情
router.get('/:rewardId', [
    (0, express_validator_1.param)('rewardId').isUUID()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const rewardId = req.params.rewardId;
        const reward = await rewardService.getReward(rewardId);
        if (!reward) {
            return res.status(404).json({
                success: false,
                message: 'Reward not found'
            });
        }
        // 检查权限：只能查看自己的奖励
        if (reward.userId !== req.user.id && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }
        res.json({
            success: true,
            data: reward
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting reward:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get reward'
        });
    }
});
// 领取奖励
router.post('/:rewardId/claim', [
    (0, express_validator_1.param)('rewardId').isUUID()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const rewardId = req.params.rewardId;
        const userId = req.user.id;
        // 验证奖励归属
        const reward = await rewardService.getReward(rewardId);
        if (!reward) {
            return res.status(404).json({
                success: false,
                message: 'Reward not found'
            });
        }
        if (reward.userId !== userId) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }
        if (reward.status === RewardInterface_1.RewardStatus.DISTRIBUTED) {
            return res.status(400).json({
                success: false,
                message: 'Reward already claimed'
            });
        }
        // 分发奖励
        const result = await rewardService.distributeReward(rewardId);
        res.json({
            success: result.success,
            message: result.message,
            data: result
        });
    }
    catch (error) {
        logger_1.logger.error('Error claiming reward:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to claim reward'
        });
    }
});
// 获取推荐统计
router.get('/referral/stats', async (req, res) => {
    try {
        const userId = req.user.id;
        const stats = await rewardService.getReferralStats(userId);
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting referral stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get referral statistics'
        });
    }
});
// 处理推荐
router.post('/referral', [
    (0, express_validator_1.body)('refereeId').isUUID()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const referrerId = req.user.id;
        const { refereeId } = req.body;
        if (referrerId === refereeId) {
            return res.status(400).json({
                success: false,
                message: 'Cannot refer yourself'
            });
        }
        const rewards = await rewardService.processReferral(referrerId, refereeId);
        res.json({
            success: true,
            message: 'Referral processed successfully',
            data: rewards
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing referral:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to process referral'
        });
    }
});
// 获取用户成就
router.get('/achievements/user', async (req, res) => {
    try {
        const userId = req.user.id;
        const achievements = await rewardService.getUserAchievements(userId);
        res.json({
            success: true,
            data: achievements
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting user achievements:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get achievements'
        });
    }
});
// 检查新成就
router.post('/achievements/check', async (req, res) => {
    try {
        const userId = req.user.id;
        const newAchievements = await rewardService.checkAchievements(userId);
        // 为新解锁的成就创建奖励
        const rewards = [];
        for (const achievement of newAchievements) {
            const achievementRewards = await rewardService.unlockAchievement(userId, achievement.id);
            rewards.push(...achievementRewards);
        }
        res.json({
            success: true,
            data: {
                newAchievements,
                rewards
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error checking achievements:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to check achievements'
        });
    }
});
// 获取活动列表
router.get('/campaigns', [
    (0, express_validator_1.query)('active').optional().isBoolean()
], async (req, res) => {
    try {
        const isActive = req.query.active === 'true' ? true : undefined;
        const campaigns = await rewardService.getCampaigns(isActive);
        res.json({
            success: true,
            data: campaigns
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting campaigns:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get campaigns'
        });
    }
});
// 管理员路由
// 获取系统奖励统计
router.get('/admin/stats', async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
        }
        const stats = await rewardService.getSystemStats();
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting system stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get system statistics'
        });
    }
});
// 批量分发奖励
router.post('/admin/distribute', [
    (0, express_validator_1.body)('rewardIds').isArray().notEmpty(),
    (0, express_validator_1.body)('rewardIds.*').isUUID()
], async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
        }
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { rewardIds } = req.body;
        const results = await rewardService.batchDistributeRewards(rewardIds);
        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;
        res.json({
            success: true,
            message: `Distributed ${successCount} rewards, ${failCount} failed`,
            data: results
        });
    }
    catch (error) {
        logger_1.logger.error('Error batch distributing rewards:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to distribute rewards'
        });
    }
});
// 导出奖励数据
router.get('/admin/export', [
    (0, express_validator_1.query)('startDate').isISO8601(),
    (0, express_validator_1.query)('endDate').isISO8601()
], async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
        }
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const startDate = new Date(req.query.startDate);
        const endDate = new Date(req.query.endDate);
        const data = await rewardService.exportRewards(startDate, endDate);
        res.json({
            success: true,
            data
        });
    }
    catch (error) {
        logger_1.logger.error('Error exporting rewards:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to export rewards'
        });
    }
});
// 创建手动奖励（管理员）
router.post('/admin/create', [
    (0, express_validator_1.body)('userId').isUUID(),
    (0, express_validator_1.body)('type').isIn(Object.values(RewardInterface_1.RewardType)),
    (0, express_validator_1.body)('amount').isFloat({ min: 0 }),
    (0, express_validator_1.body)('tokenType').isIn(Object.values(RewardInterface_1.TokenType)),
    (0, express_validator_1.body)('reason').isString().notEmpty()
], async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
        }
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { userId, type, amount, tokenType, reason } = req.body;
        const reward = await rewardService.createReward(userId, type, amount, tokenType, reason, { createdBy: req.user.id, manual: true });
        res.json({
            success: true,
            message: 'Reward created successfully',
            data: reward
        });
    }
    catch (error) {
        logger_1.logger.error('Error creating manual reward:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create reward'
        });
    }
});
module.exports = router;
//# sourceMappingURL=rewards.js.map
import { StrategyType } from '../../shared/types';
import { IStrategy, IStrategyFactory } from './base/StrategyInterface';
export declare class StrategyFactory implements IStrategyFactory {
    private static instance;
    private strategyCounter;
    private constructor();
    static getInstance(): StrategyFactory;
    /**
     * 创建策略实例
     * @param type 策略类型
     * @param config 策略配置
     * @returns 策略实例
     */
    createStrategy(type: StrategyType, config: any): Promise<IStrategy>;
    /**
     * 获取支持的策略类型
     * @returns 支持的策略类型数组
     */
    getSupportedTypes(): StrategyType[];
    /**
     * 验证策略配置
     * @param type 策略类型
     * @param config 配置对象
     * @returns 验证结果
     */
    validateConfig(type: StrategyType, config: any): {
        valid: boolean;
        errors: string[];
    };
    /**
     * 验证网格策略配置
     */
    private validateGridConfig;
    /**
     * 验证趋势跟踪策略配置
     */
    private validateTrendFollowingConfig;
    /**
     * 生成策略ID
     */
    private generateStrategyId;
    /**
     * 生成策略名称
     */
    private generateStrategyName;
    /**
     * 获取策略类型的默认配置
     * @param type 策略类型
     * @returns 默认配置
     */
    getDefaultConfig(type: StrategyType): any;
    /**
     * 获取策略类型的描述信息
     * @param type 策略类型
     * @returns 描述信息
     */
    getStrategyDescription(type: StrategyType): {
        name: string;
        description: string;
        category: 'cex' | 'dex';
        complexity: 'beginner' | 'intermediate' | 'advanced';
        riskLevel: 'low' | 'medium' | 'high';
    };
}
//# sourceMappingURL=StrategyFactory.d.ts.map
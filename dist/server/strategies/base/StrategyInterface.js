"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignalType = exports.MarketDataError = exports.RiskManagementError = exports.ExecutionError = exports.ConfigurationError = exports.StrategyError = void 0;
// 策略错误类
class StrategyError extends Error {
    constructor(message, strategyId, strategyType, code) {
        super(message);
        this.strategyId = strategyId;
        this.strategyType = strategyType;
        this.code = code;
        this.name = 'StrategyError';
    }
}
exports.StrategyError = StrategyError;
// 配置错误
class ConfigurationError extends StrategyError {
    constructor(message, strategyId, strategyType) {
        super(message, strategyId, strategyType, 'CONFIG_ERROR');
        this.name = 'ConfigurationError';
    }
}
exports.ConfigurationError = ConfigurationError;
// 执行错误
class ExecutionError extends StrategyError {
    constructor(message, strategyId, strategyType) {
        super(message, strategyId, strategyType, 'EXECUTION_ERROR');
        this.name = 'ExecutionError';
    }
}
exports.ExecutionError = ExecutionError;
// 风险管理错误
class RiskManagementError extends StrategyError {
    constructor(message, strategyId, strategyType) {
        super(message, strategyId, strategyType, 'RISK_ERROR');
        this.name = 'RiskManagementError';
    }
}
exports.RiskManagementError = RiskManagementError;
// 市场数据错误
class MarketDataError extends StrategyError {
    constructor(message, strategyId, strategyType) {
        super(message, strategyId, strategyType, 'MARKET_DATA_ERROR');
        this.name = 'MarketDataError';
    }
}
exports.MarketDataError = MarketDataError;
// 策略信号类型
var SignalType;
(function (SignalType) {
    SignalType["BUY"] = "buy";
    SignalType["SELL"] = "sell";
    SignalType["HOLD"] = "hold";
    SignalType["CLOSE"] = "close";
})(SignalType || (exports.SignalType = SignalType = {}));
//# sourceMappingURL=StrategyInterface.js.map
{"version": 3, "file": "StrategyInterface.js", "sourceRoot": "", "sources": ["../../../../src/server/strategies/base/StrategyInterface.ts"], "names": [], "mappings": ";;;AAwHA,QAAQ;AACR,MAAa,aAAc,SAAQ,KAAK;IACtC,YACE,OAAe,EACR,UAAkB,EAClB,YAA0B,EAC1B,IAAa;QAEpB,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,eAAU,GAAV,UAAU,CAAQ;QAClB,iBAAY,GAAZ,YAAY,CAAc;QAC1B,SAAI,GAAJ,IAAI,CAAS;QAGpB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AAVD,sCAUC;AAED,OAAO;AACP,MAAa,kBAAmB,SAAQ,aAAa;IACnD,YAAY,OAAe,EAAE,UAAkB,EAAE,YAA0B;QACzE,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF;AALD,gDAKC;AAED,OAAO;AACP,MAAa,cAAe,SAAQ,aAAa;IAC/C,YAAY,OAAe,EAAE,UAAkB,EAAE,YAA0B;QACzE,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,wCAKC;AAED,SAAS;AACT,MAAa,mBAAoB,SAAQ,aAAa;IACpD,YAAY,OAAe,EAAE,UAAkB,EAAE,YAA0B;QACzE,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AALD,kDAKC;AAED,SAAS;AACT,MAAa,eAAgB,SAAQ,aAAa;IAChD,YAAY,OAAe,EAAE,UAAkB,EAAE,YAA0B;QACzE,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9D,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AALD,0CAKC;AAED,SAAS;AACT,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,yBAAW,CAAA;IACX,2BAAa,CAAA;IACb,2BAAa,CAAA;IACb,6BAAe,CAAA;AACjB,CAAC,EALW,UAAU,0BAAV,UAAU,QAKrB"}
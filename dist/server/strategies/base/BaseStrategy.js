"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseStrategy = void 0;
const events_1 = require("events");
const logger_1 = require("../../utils/logger");
const types_1 = require("../../../shared/types");
const StrategyInterface_1 = require("./StrategyInterface");
class BaseStrategy extends events_1.EventEmitter {
    constructor(id, name, type) {
        super();
        this._status = types_1.StrategyStatus.INACTIVE;
        this._isRunning = false;
        this.positions = new Map();
        this.orders = new Map();
        this.trades = [];
        this.lastExecution = null;
        this.executionInterval = null;
        this.id = id;
        this.name = name;
        this.type = type;
        // 默认风险参数
        this.riskParameters = {
            maxPositionSize: 1000,
            maxDailyLoss: 100,
            maxDrawdown: 0.1,
            stopLossPercentage: 0.05,
            takeProfitPercentage: 0.1,
            maxConcurrentTrades: 5
        };
    }
    get status() {
        return this._status;
    }
    get isRunning() {
        return this._isRunning;
    }
    // 启动策略
    async start() {
        try {
            if (this._isRunning) {
                throw new StrategyInterface_1.StrategyError('Strategy is already running', this.id, this.type);
            }
            // 验证配置
            const isValid = await this.validate();
            if (!isValid) {
                throw new StrategyInterface_1.StrategyError('Strategy validation failed', this.id, this.type);
            }
            this._status = types_1.StrategyStatus.ACTIVE;
            this._isRunning = true;
            // 开始执行循环
            this.startExecutionLoop();
            this.emit('started');
            logger_1.strategyLogger.info(`Strategy ${this.name} (${this.id}) started`);
        }
        catch (error) {
            this._status = types_1.StrategyStatus.ERROR;
            this._isRunning = false;
            this.emit('error', error);
            throw error;
        }
    }
    // 停止策略
    async stop() {
        try {
            this._isRunning = false;
            this._status = types_1.StrategyStatus.INACTIVE;
            // 停止执行循环
            if (this.executionInterval) {
                clearInterval(this.executionInterval);
                this.executionInterval = null;
            }
            // 关闭所有持仓
            await this.closeAllPositions();
            this.emit('stopped');
            logger_1.strategyLogger.info(`Strategy ${this.name} (${this.id}) stopped`);
        }
        catch (error) {
            this._status = types_1.StrategyStatus.ERROR;
            this.emit('error', error);
            throw error;
        }
    }
    // 暂停策略
    async pause() {
        if (!this._isRunning) {
            throw new StrategyInterface_1.StrategyError('Strategy is not running', this.id, this.type);
        }
        this._status = types_1.StrategyStatus.PAUSED;
        if (this.executionInterval) {
            clearInterval(this.executionInterval);
            this.executionInterval = null;
        }
        this.emit('paused');
        logger_1.strategyLogger.info(`Strategy ${this.name} (${this.id}) paused`);
    }
    // 恢复策略
    async resume() {
        if (this._status !== types_1.StrategyStatus.PAUSED) {
            throw new StrategyInterface_1.StrategyError('Strategy is not paused', this.id, this.type);
        }
        this._status = types_1.StrategyStatus.ACTIVE;
        this.startExecutionLoop();
        this.emit('resumed');
        logger_1.strategyLogger.info(`Strategy ${this.name} (${this.id}) resumed`);
    }
    // 更新配置
    async updateConfig(config) {
        const oldConfig = { ...this.config };
        try {
            this.config = { ...this.config, ...config };
            // 如果策略正在运行，需要重新验证
            if (this._isRunning) {
                const isValid = await this.validate();
                if (!isValid) {
                    this.config = oldConfig; // 回滚配置
                    throw new StrategyInterface_1.StrategyError('Invalid configuration', this.id, this.type);
                }
            }
            this.emit('configUpdated', config);
            logger_1.strategyLogger.info(`Strategy ${this.name} (${this.id}) configuration updated`);
        }
        catch (error) {
            this.config = oldConfig; // 回滚配置
            throw error;
        }
    }
    // 获取配置
    getConfig() {
        return { ...this.config };
    }
    // 获取统计信息
    async getStats() {
        const successfulTrades = this.trades.filter(trade => trade.profit > 0).length;
        const failedTrades = this.trades.filter(trade => trade.profit <= 0).length;
        const totalProfit = this.trades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
        const totalFees = this.trades.reduce((sum, trade) => sum + (trade.fee || 0), 0);
        const profits = this.trades.map(trade => trade.profit || 0);
        return {
            totalTrades: this.trades.length,
            successfulTrades,
            failedTrades,
            totalProfit,
            totalFees,
            winRate: this.trades.length > 0 ? successfulTrades / this.trades.length : 0,
            averageProfit: this.trades.length > 0 ? totalProfit / this.trades.length : 0,
            maxProfit: profits.length > 0 ? Math.max(...profits) : 0,
            maxLoss: profits.length > 0 ? Math.min(...profits) : 0,
            sharpeRatio: this.calculateSharpeRatio(profits),
            maxDrawdown: this.calculateMaxDrawdown()
        };
    }
    // 获取性能数据
    async getPerformance(startDate, endDate) {
        let filteredTrades = this.trades;
        if (startDate || endDate) {
            filteredTrades = this.trades.filter(trade => {
                const tradeDate = new Date(trade.timestamp);
                if (startDate && tradeDate < startDate)
                    return false;
                if (endDate && tradeDate > endDate)
                    return false;
                return true;
            });
        }
        const equity = this.calculateEquityCurve(filteredTrades);
        const monthlyReturns = this.calculateMonthlyReturns(filteredTrades);
        return {
            trades: filteredTrades,
            equity,
            monthlyReturns,
            stats: await this.getStats()
        };
    }
    // 检查风险限制
    async checkRiskLimits() {
        try {
            // 检查最大持仓数量
            if (this.positions.size >= this.riskParameters.maxConcurrentTrades) {
                return false;
            }
            // 检查当日损失
            const todayLoss = this.calculateTodayLoss();
            if (todayLoss >= this.riskParameters.maxDailyLoss) {
                return false;
            }
            // 检查最大回撤
            const currentDrawdown = this.calculateMaxDrawdown();
            if (currentDrawdown >= this.riskParameters.maxDrawdown) {
                return false;
            }
            return true;
        }
        catch (error) {
            throw new StrategyInterface_1.RiskManagementError(`Risk check failed: ${error.message}`, this.id, this.type);
        }
    }
    // 计算持仓大小
    calculatePositionSize(signal) {
        const maxSize = this.riskParameters.maxPositionSize;
        const confidence = signal.confidence;
        // 基于信号置信度调整持仓大小
        return Math.min(maxSize * confidence, maxSize);
    }
    // 开始执行循环
    startExecutionLoop() {
        const interval = this.config.executionInterval || 60000; // 默认1分钟
        this.executionInterval = setInterval(async () => {
            if (this._status === types_1.StrategyStatus.ACTIVE) {
                try {
                    await this.executeStrategy();
                }
                catch (error) {
                    this.handleExecutionError(error);
                }
            }
        }, interval);
    }
    // 执行策略逻辑
    async executeStrategy() {
        try {
            // 检查风险限制
            const riskCheckPassed = await this.checkRiskLimits();
            if (!riskCheckPassed) {
                logger_1.strategyLogger.warn(`Strategy ${this.id} risk limits exceeded, skipping execution`);
                return;
            }
            // 生成信号
            const signal = await this.generateSignal();
            if (!signal) {
                return; // 没有信号，跳过执行
            }
            // 执行信号
            const result = await this.executeSignal(signal);
            // 记录执行结果
            this.recordExecution(result);
            this.lastExecution = new Date();
            this.emit('executed', result);
        }
        catch (error) {
            throw new StrategyInterface_1.ExecutionError(`Strategy execution failed: ${error.message}`, this.id, this.type);
        }
    }
    // 关闭所有持仓
    async closeAllPositions() {
        const closePromises = Array.from(this.positions.values()).map(position => this.closePosition(position.symbol));
        await Promise.all(closePromises);
    }
    // 关闭单个持仓
    async closePosition(symbol) {
        // 子类实现具体的平仓逻辑
    }
    // 记录执行结果
    recordExecution(result) {
        if (result.success && result.profit !== undefined) {
            this.trades.push({
                timestamp: result.timestamp,
                profit: result.profit,
                fee: result.fee || 0,
                amount: result.amount,
                price: result.price
            });
        }
    }
    // 处理执行错误
    handleExecutionError(error) {
        this._status = types_1.StrategyStatus.ERROR;
        this.emit('error', error);
        logger_1.strategyLogger.error(`Strategy ${this.id} execution error:`, error);
    }
    // 计算夏普比率
    calculateSharpeRatio(returns) {
        if (returns.length < 2)
            return 0;
        const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
        const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
        const stdDev = Math.sqrt(variance);
        return stdDev === 0 ? 0 : mean / stdDev;
    }
    // 计算最大回撤
    calculateMaxDrawdown() {
        if (this.trades.length === 0)
            return 0;
        let peak = 0;
        let maxDrawdown = 0;
        let runningTotal = 0;
        for (const trade of this.trades) {
            runningTotal += trade.profit || 0;
            if (runningTotal > peak) {
                peak = runningTotal;
            }
            const drawdown = (peak - runningTotal) / peak;
            if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
            }
        }
        return maxDrawdown;
    }
    // 计算当日损失
    calculateTodayLoss() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return this.trades
            .filter(trade => new Date(trade.timestamp) >= today)
            .reduce((sum, trade) => sum + Math.min(0, trade.profit || 0), 0);
    }
    // 计算权益曲线
    calculateEquityCurve(trades) {
        let runningTotal = 0;
        return trades.map(trade => {
            runningTotal += trade.profit || 0;
            return {
                timestamp: trade.timestamp,
                equity: runningTotal
            };
        });
    }
    // 计算月度收益
    calculateMonthlyReturns(trades) {
        const monthlyData = {};
        trades.forEach(trade => {
            const date = new Date(trade.timestamp);
            const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
            monthlyData[monthKey] = (monthlyData[monthKey] || 0) + (trade.profit || 0);
        });
        return Object.entries(monthlyData).map(([month, profit]) => ({
            month,
            profit
        }));
    }
    // 事件处理方法
    onMarketData(data) {
        // 子类可以重写此方法处理市场数据
    }
    onOrderFilled(order) {
        // 子类可以重写此方法处理订单成交
    }
    onError(error) {
        // 子类可以重写此方法处理错误
    }
}
exports.BaseStrategy = BaseStrategy;
//# sourceMappingURL=BaseStrategy.js.map
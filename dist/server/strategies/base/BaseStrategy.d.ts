import { EventEmitter } from 'events';
import { StrategyType, StrategyStatus } from '../../../shared/types';
import { IStrategy, StrategyExecutionResult, StrategyStats, RiskParameters, StrategySignal, MarketData, OrderInfo, PositionInfo } from './StrategyInterface';
export declare abstract class BaseStrategy extends EventEmitter implements IStrategy {
    readonly id: string;
    readonly name: string;
    readonly type: StrategyType;
    protected config: any;
    protected _status: StrategyStatus;
    protected _isRunning: boolean;
    protected riskParameters: RiskParameters;
    protected positions: Map<string, PositionInfo>;
    protected orders: Map<string, OrderInfo>;
    protected trades: any[];
    protected lastExecution: Date | null;
    protected executionInterval: NodeJS.Timeout | null;
    constructor(id: string, name: string, type: StrategyType);
    get status(): StrategyStatus;
    get isRunning(): boolean;
    abstract initialize(config: any): Promise<void>;
    abstract execute(): Promise<StrategyExecutionResult>;
    abstract validate(): Promise<boolean>;
    protected abstract generateSignal(): Promise<StrategySignal | null>;
    protected abstract executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult>;
    start(): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    updateConfig(config: any): Promise<void>;
    getConfig(): any;
    getStats(): Promise<StrategyStats>;
    getPerformance(startDate?: Date, endDate?: Date): Promise<any>;
    checkRiskLimits(): Promise<boolean>;
    calculatePositionSize(signal: StrategySignal): number;
    protected startExecutionLoop(): void;
    protected executeStrategy(): Promise<void>;
    protected closeAllPositions(): Promise<void>;
    protected closePosition(symbol: string): Promise<void>;
    protected recordExecution(result: StrategyExecutionResult): void;
    protected handleExecutionError(error: any): void;
    protected calculateSharpeRatio(returns: number[]): number;
    protected calculateMaxDrawdown(): number;
    protected calculateTodayLoss(): number;
    protected calculateEquityCurve(trades: any[]): any[];
    protected calculateMonthlyReturns(trades: any[]): any[];
    onMarketData?(data: MarketData): void;
    onOrderFilled?(order: OrderInfo): void;
    onError?(error: Error): void;
}
//# sourceMappingURL=BaseStrategy.d.ts.map
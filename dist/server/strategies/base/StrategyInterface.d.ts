import { CEXExchange, DEXProtocol, BlockchainNetwork, StrategyType, StrategyStatus } from '../../../shared/types';
export interface BaseStrategyConfig {
    id: string;
    userId: string;
    name: string;
    type: StrategyType;
    exchangeType: 'cex' | 'dex';
    exchange?: CEXExchange;
    network?: BlockchainNetwork;
    dexProtocol?: DEXProtocol;
    status: StrategyStatus;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface StrategyExecutionResult {
    success: boolean;
    message: string;
    orderId?: string;
    txHash?: string;
    amount?: number;
    price?: number;
    fee?: number;
    profit?: number;
    timestamp: Date;
}
export interface StrategyStats {
    totalTrades: number;
    successfulTrades: number;
    failedTrades: number;
    totalProfit: number;
    totalFees: number;
    winRate: number;
    averageProfit: number;
    maxProfit: number;
    maxLoss: number;
    sharpeRatio?: number;
    maxDrawdown?: number;
}
export interface RiskParameters {
    maxPositionSize: number;
    maxDailyLoss: number;
    maxDrawdown: number;
    stopLossPercentage: number;
    takeProfitPercentage: number;
    maxConcurrentTrades: number;
}
export interface IStrategy {
    readonly id: string;
    readonly name: string;
    readonly type: StrategyType;
    readonly status: StrategyStatus;
    readonly isRunning: boolean;
    initialize(config: any): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    execute(): Promise<StrategyExecutionResult>;
    validate(): Promise<boolean>;
    updateConfig(config: any): Promise<void>;
    getConfig(): any;
    getStats(): Promise<StrategyStats>;
    getPerformance(startDate?: Date, endDate?: Date): Promise<any>;
    checkRiskLimits(): Promise<boolean>;
    calculatePositionSize(signal: any): number;
    onMarketData?(data: any): void;
    onOrderFilled?(order: any): void;
    onError?(error: Error): void;
}
export interface IStrategyFactory {
    createStrategy(type: StrategyType, config: any): Promise<IStrategy>;
    getSupportedTypes(): StrategyType[];
    validateConfig(type: StrategyType, config: any): {
        valid: boolean;
        errors: string[];
    };
}
export interface IStrategyManager {
    addStrategy(strategy: IStrategy): Promise<void>;
    removeStrategy(strategyId: string): Promise<void>;
    getStrategy(strategyId: string): IStrategy | null;
    getAllStrategies(): IStrategy[];
    getActiveStrategies(): IStrategy[];
    startAll(): Promise<void>;
    stopAll(): Promise<void>;
    pauseAll(): Promise<void>;
    resumeAll(): Promise<void>;
    getSystemStats(): Promise<any>;
    getStrategyPerformance(strategyId: string): Promise<any>;
}
export declare class StrategyError extends Error {
    strategyId: string;
    strategyType: StrategyType;
    code?: string | undefined;
    constructor(message: string, strategyId: string, strategyType: StrategyType, code?: string | undefined);
}
export declare class ConfigurationError extends StrategyError {
    constructor(message: string, strategyId: string, strategyType: StrategyType);
}
export declare class ExecutionError extends StrategyError {
    constructor(message: string, strategyId: string, strategyType: StrategyType);
}
export declare class RiskManagementError extends StrategyError {
    constructor(message: string, strategyId: string, strategyType: StrategyType);
}
export declare class MarketDataError extends StrategyError {
    constructor(message: string, strategyId: string, strategyType: StrategyType);
}
export declare enum SignalType {
    BUY = "buy",
    SELL = "sell",
    HOLD = "hold",
    CLOSE = "close"
}
export interface StrategySignal {
    type: SignalType;
    symbol: string;
    price: number;
    amount: number;
    confidence: number;
    timestamp: Date;
    metadata?: any;
}
export interface MarketData {
    symbol: string;
    price: number;
    volume: number;
    timestamp: Date;
    bid?: number;
    ask?: number;
    high24h?: number;
    low24h?: number;
    change24h?: number;
}
export interface OrderInfo {
    id: string;
    symbol: string;
    side: 'buy' | 'sell';
    type: 'market' | 'limit';
    amount: number;
    price?: number;
    status: string;
    filled: number;
    remaining: number;
    timestamp: Date;
}
export interface PositionInfo {
    symbol: string;
    side: 'long' | 'short';
    size: number;
    entryPrice: number;
    currentPrice: number;
    unrealizedPnl: number;
    realizedPnl: number;
    timestamp: Date;
}
export interface BacktestResult {
    strategyId: string;
    startDate: Date;
    endDate: Date;
    initialBalance: number;
    finalBalance: number;
    totalReturn: number;
    annualizedReturn: number;
    maxDrawdown: number;
    sharpeRatio: number;
    winRate: number;
    totalTrades: number;
    profitFactor: number;
    trades: any[];
    equity: any[];
}
export interface OptimizationParameter {
    name: string;
    type: 'number' | 'boolean' | 'string';
    min?: number;
    max?: number;
    step?: number;
    values?: any[];
    default: any;
}
export interface OptimizationResult {
    parameters: Record<string, any>;
    performance: {
        totalReturn: number;
        sharpeRatio: number;
        maxDrawdown: number;
        winRate: number;
    };
    backtest: BacktestResult;
}
//# sourceMappingURL=StrategyInterface.d.ts.map
{"version": 3, "file": "StrategyFactory.js", "sourceRoot": "", "sources": ["../../../src/server/strategies/StrategyFactory.ts"], "names": [], "mappings": ";;;AAAA,8CAAkD;AAClD,gEAA2F;AAC3F,qDAAkD;AAClD,yEAAsE;AACtE,gBAAgB;AAChB,6EAA6E;AAC7E,qFAAqF;AACrF,uEAAuE;AACvE,2DAA2D;AAC3D,iDAAiD;AACjD,6EAA6E;AAC7E,+EAA+E;AAC/E,2EAA2E;AAC3E,mFAAmF;AACnF,uDAAuD;AAEvD,MAAa,eAAe;IAI1B;QAFQ,oBAAe,GAAW,CAAC,CAAC;IAEb,CAAC;IAExB,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAAC,IAAkB,EAAE,MAAW;QAClD,OAAO;QACP,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,IAAI,sCAAkB,CAC1B,0BAA0B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EACxD,MAAM,CAAC,EAAE,IAAI,SAAS,EACtB,IAAI,CACL,CAAC;QACJ,CAAC;QAED,YAAY;QACZ,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAEpE,IAAI,QAAmB,CAAC;QAExB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,oBAAY,CAAC,IAAI;gBACpB,QAAQ,GAAG,IAAI,2BAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBACtD,MAAM;YAER,KAAK,oBAAY,CAAC,eAAe;gBAC/B,QAAQ,GAAG,IAAI,+CAAsB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBAChE,MAAM;YAER,uCAAuC;YACvC,uEAAuE;YACvE,WAAW;YAEX,2CAA2C;YAC3C,2EAA2E;YAC3E,WAAW;YAEX,oCAAoC;YACpC,oEAAoE;YACpE,WAAW;YAEX,6BAA6B;YAC7B,8DAA8D;YAC9D,WAAW;YAEX,iCAAiC;YACjC,yDAAyD;YACzD,WAAW;YAEX,wCAAwC;YACxC,uEAAuE;YACvE,WAAW;YAEX,yCAAyC;YACzC,wEAAwE;YACxE,WAAW;YAEX,sCAAsC;YACtC,sEAAsE;YACtE,WAAW;YAEX,2CAA2C;YAC3C,0EAA0E;YAC1E,WAAW;YAEX,qCAAqC;YACrC,4DAA4D;YAC5D,WAAW;YAEX;gBACE,MAAM,IAAI,sCAAkB,CAC1B,8BAA8B,IAAI,EAAE,EACpC,UAAU,EACV,IAAI,CACL,CAAC;QACN,CAAC;QAED,QAAQ;QACR,MAAM,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAElC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,OAAO;YACL,oBAAY,CAAC,IAAI;YACjB,oBAAY,CAAC,eAAe;YAC5B,kCAAkC;YAClC,sCAAsC;YACtC,+BAA+B;YAC/B,wBAAwB;YACxB,4BAA4B;YAC5B,mCAAmC;YACnC,oCAAoC;YACpC,iCAAiC;YACjC,sCAAsC;YACtC,+BAA+B;SAChC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,cAAc,CAAC,IAAkB,EAAE,MAAW;QAC5C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,OAAO;QACP,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrC,CAAC;QAED,eAAe;QACf,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,oBAAY,CAAC,IAAI;gBACpB,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACxC,MAAM;YAER,KAAK,oBAAY,CAAC,eAAe;gBAC/B,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAClD,MAAM;YAER,uCAAuC;YACvC,yDAAyD;YACzD,WAAW;YAEX,2CAA2C;YAC3C,6DAA6D;YAC7D,WAAW;YAEX,oCAAoC;YACpC,sDAAsD;YACtD,WAAW;YAEX,6BAA6B;YAC7B,gDAAgD;YAChD,WAAW;YAEX,iCAAiC;YACjC,2CAA2C;YAC3C,WAAW;YAEX,aAAa;YAEb;gBACE,MAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;QAClD,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,MAAW,EAAE,MAAgB;QACtD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YACpE,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YACpE,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,QAAQ,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;YACtE,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,MAAW,EAAE,MAAgB;QAChE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACtE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,QAAQ,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,eAAe,KAAK,QAAQ,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,IAAI,MAAM,CAAC,eAAe,IAAI,GAAG,EAAE,CAAC;YAC/G,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,iBAAiB,KAAK,QAAQ,IAAI,MAAM,CAAC,iBAAiB,IAAI,CAAC,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAkB;QAC3C,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,GAAG,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,IAAkB;QAC7C,MAAM,SAAS,GAAiC;YAC9C,CAAC,oBAAY,CAAC,IAAI,CAAC,EAAE,cAAc;YACnC,CAAC,oBAAY,CAAC,iBAAiB,CAAC,EAAE,mBAAmB;YACrD,CAAC,oBAAY,CAAC,qBAAqB,CAAC,EAAE,uBAAuB;YAC7D,CAAC,oBAAY,CAAC,cAAc,CAAC,EAAE,gBAAgB;YAC/C,CAAC,oBAAY,CAAC,eAAe,CAAC,EAAE,iBAAiB;YACjD,CAAC,oBAAY,CAAC,OAAO,CAAC,EAAE,SAAS;YACjC,CAAC,oBAAY,CAAC,WAAW,CAAC,EAAE,aAAa;YACzC,CAAC,oBAAY,CAAC,kBAAkB,CAAC,EAAE,oBAAoB;YACvD,CAAC,oBAAY,CAAC,mBAAmB,CAAC,EAAE,qBAAqB;YACzD,CAAC,oBAAY,CAAC,gBAAgB,CAAC,EAAE,kBAAkB;YACnD,CAAC,oBAAY,CAAC,qBAAqB,CAAC,EAAE,uBAAuB;YAC7D,CAAC,oBAAY,CAAC,eAAe,CAAC,EAAE,iBAAiB;SAClD,CAAC;QAEF,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC;QACvD,OAAO,GAAG,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,IAAkB;QACjC,MAAM,cAAc,GAA8B;YAChD,CAAC,oBAAY,CAAC,IAAI,CAAC,EAAE;gBACnB,QAAQ,EAAE,YAAY;gBACtB,UAAU,EAAE,EAAE;gBACd,iBAAiB,EAAE,KAAK;gBACxB,kBAAkB,EAAE,IAAI;aACzB;YACD,CAAC,oBAAY,CAAC,eAAe,CAAC,EAAE;gBAC9B,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,CAAC;gBACf,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,gBAAgB,EAAE,GAAG;gBACrB,iBAAiB,EAAE,MAAM;aAC1B;YACD,CAAC,oBAAY,CAAC,iBAAiB,CAAC,EAAE;gBAChC,iBAAiB,EAAE,MAAM;gBACzB,WAAW,EAAE,GAAG;aACjB;YACD,CAAC,oBAAY,CAAC,qBAAqB,CAAC,EAAE;gBACpC,gBAAgB,EAAE,GAAG;gBACrB,iBAAiB,EAAE,KAAK;aACzB;YACD,CAAC,oBAAY,CAAC,cAAc,CAAC,EAAE;gBAC7B,iBAAiB,EAAE,MAAM;gBACzB,WAAW,EAAE,IAAI;aAClB;YACD,CAAC,oBAAY,CAAC,OAAO,CAAC,EAAE;gBACtB,UAAU,EAAE,GAAG;gBACf,iBAAiB,EAAE,KAAK;aACzB;YACD,CAAC,oBAAY,CAAC,WAAW,CAAC,EAAE;gBAC1B,SAAS,EAAE,MAAM;gBACjB,iBAAiB,EAAE,MAAM;aAC1B;YACD,CAAC,oBAAY,CAAC,kBAAkB,CAAC,EAAE;gBACjC,iBAAiB,EAAE,MAAM;gBACzB,QAAQ,EAAE,MAAM;aACjB;YACD,CAAC,oBAAY,CAAC,mBAAmB,CAAC,EAAE;gBAClC,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,MAAM;gBACzB,QAAQ,EAAE,MAAM;aACjB;YACD,CAAC,oBAAY,CAAC,gBAAgB,CAAC,EAAE;gBAC/B,iBAAiB,EAAE,KAAK;gBACxB,SAAS,EAAE,GAAG;aACf;YACD,CAAC,oBAAY,CAAC,qBAAqB,CAAC,EAAE;gBACpC,iBAAiB,EAAE,MAAM;gBACzB,QAAQ,EAAE,MAAM;aACjB;YACD,CAAC,oBAAY,CAAC,eAAe,CAAC,EAAE;gBAC9B,SAAS,EAAE,MAAM;gBACjB,iBAAiB,EAAE,MAAM;gBACzB,QAAQ,EAAE,MAAM;aACjB;SACF,CAAC;QAEF,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,sBAAsB,CAAC,IAAkB;QAOvC,MAAM,YAAY,GAA8B;YAC9C,CAAC,oBAAY,CAAC,IAAI,CAAC,EAAE;gBACnB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,0EAA0E;gBACvF,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,QAAQ;aACpB;YACD,CAAC,oBAAY,CAAC,eAAe,CAAC,EAAE;gBAC9B,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,uEAAuE;gBACpF,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,QAAQ;aACpB;YACD,CAAC,oBAAY,CAAC,iBAAiB,CAAC,EAAE;gBAChC,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,0DAA0D;gBACvE,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,CAAC,oBAAY,CAAC,qBAAqB,CAAC,EAAE;gBACpC,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,+DAA+D;gBAC5E,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,KAAK;aACjB;YACD,CAAC,oBAAY,CAAC,cAAc,CAAC,EAAE;gBAC7B,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,8DAA8D;gBAC3E,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,MAAM;aAClB;YACD,CAAC,oBAAY,CAAC,OAAO,CAAC,EAAE;gBACtB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,+CAA+C;gBAC5D,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,CAAC,oBAAY,CAAC,WAAW,CAAC,EAAE;gBAC1B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wDAAwD;gBACrE,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,MAAM;aAClB;YACD,CAAC,oBAAY,CAAC,kBAAkB,CAAC,EAAE;gBACjC,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,sDAAsD;gBACnE,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,MAAM;aAClB;YACD,CAAC,oBAAY,CAAC,mBAAmB,CAAC,EAAE;gBAClC,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,2CAA2C;gBACxD,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,QAAQ;aACpB;YACD,CAAC,oBAAY,CAAC,gBAAgB,CAAC,EAAE;gBAC/B,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,QAAQ;aACpB;YACD,CAAC,oBAAY,CAAC,qBAAqB,CAAC,EAAE;gBACpC,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,6CAA6C;gBAC1D,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,CAAC,oBAAY,CAAC,eAAe,CAAC,EAAE;gBAC9B,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,+CAA+C;gBAC5D,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,MAAM;aAClB;SACF,CAAC;QAEF,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI;YAC3B,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,uBAAuB;YACpC,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,KAAK;SACjB,CAAC;IACJ,CAAC;CACF;AAleD,0CAkeC"}
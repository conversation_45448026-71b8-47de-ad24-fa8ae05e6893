import { BaseStrategy } from '../base/BaseStrategy';
import { PredictionService } from '../../ai/services/PredictionService';
import { StrategyExecutionResult, StrategySignal } from '../base/StrategyInterface';
import { AIStrategyConfig, PredictionResult } from '../../ai/base/AIInterface';
export declare class AIStrategy extends BaseStrategy {
    private config;
    private predictionService;
    private lastPredictions;
    private currentPrice;
    private marketFeatures;
    constructor(id: string, name: string);
    initialize(config: AIStrategyConfig): Promise<void>;
    validate(): Promise<boolean>;
    execute(): Promise<StrategyExecutionResult>;
    protected generateSignal(): Promise<StrategySignal | null>;
    protected executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult>;
    private generateAISignal;
    private ensemblePredictions;
    private averageEnsemble;
    private weightedEnsemble;
    private votingEnsemble;
    private generateTradingSignal;
    private generateReasoning;
    private executeAISignal;
    private executeOrder;
    private validateConfig;
    private initializeMarketData;
    private validateModels;
    private updateMarketData;
    getAIStatus(): {
        modelCount: number;
        lastPredictions: Record<string, PredictionResult>;
        currentPrice: number;
        marketDataPoints: number;
        ensembleMethod: string;
        minConfidence: number;
    };
    getPredictionService(): PredictionService;
}
//# sourceMappingURL=AIStrategy.d.ts.map
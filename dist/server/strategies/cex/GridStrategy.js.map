{"version": 3, "file": "GridStrategy.js", "sourceRoot": "", "sources": ["../../../../src/server/strategies/cex/GridStrategy.ts"], "names": [], "mappings": ";;;AAAA,uDAAoD;AACpD,oEAAiE;AACjE,iDAA6E;AAC7E,iEAMmC;AA8BnC,MAAa,YAAa,SAAQ,2BAAY;IAO5C,YAAY,EAAU,EAAE,IAAY;QAClC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,oBAAY,CAAC,IAAI,CAAC,CAAC;QAN7B,eAAU,GAAgB,EAAE,CAAC;QAC7B,iBAAY,GAAW,CAAC,CAAC;QACzB,gBAAW,GAAW,CAAC,CAAC;QACxB,iBAAY,GAAW,CAAC,CAAC;IAIjC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAA0B;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,OAAO;QACP,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,QAAQ;QACR,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAE5B,SAAS;QACT,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC;YACH,OAAO;YACP,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,sCAAkB,CAAC,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9E,CAAC;YAED,UAAU;YACV,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACjG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC1B,MAAM,IAAI,sCAAkB,CAAC,wBAAwB,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,CAAC;YAED,OAAO;YACP,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAChE,IAAI,IAAI,CAAC,YAAY,GAAG,mBAAmB,EAAE,CAAC;gBAC5C,MAAM,IAAI,sCAAkB,CAAC,4BAA4B,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACjF,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,SAAS;YACT,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhC,YAAY;YACZ,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE7C,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kCAAc,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAES,KAAK,CAAC,cAAc;QAC5B,0BAA0B;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,aAAa,CAAC,MAAsB;QAClD,gBAAgB;QAChB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,gDAAgD;YACzD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED,OAAO;IACC,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC7E,MAAM,IAAI,sCAAkB,CAAC,uCAAuC,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACrD,MAAM,IAAI,sCAAkB,CAAC,8CAA8C,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,sCAAkB,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,sCAAkB,CAAC,+BAA+B,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED,QAAQ;IACA,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAExE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAEvD,cAAc;YACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,cAAc;gBACtB,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,cAAc;YACd,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACV,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,cAAc;oBACtB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,KAAK;iBACd,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS;IACD,kBAAkB;QACxB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAC1F,CAAC;aAAM,CAAC;YACN,OAAO;YACP,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1G,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,SAAS;IACD,KAAK,CAAC,gBAAgB;QAC5B,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,YAAY;QACZ,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,IAAI,SAAS,CAAC,MAAM;gBAAE,SAAS;YAE/B,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;oBACtD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACnB,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;wBACxB,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;wBACnC,WAAW,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;wBAClC,cAAc,EAAE,CAAC;wBAEjB,YAAY;wBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,gBAAgB;oBAChB,OAAO,CAAC,KAAK,CAAC,yCAAyC,SAAS,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;QACH,CAAC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC7B,CAAC;QAED,OAAO;YACL,OAAO,EAAE,cAAc,GAAG,CAAC;YAC3B,OAAO,EAAE,YAAY,cAAc,cAAc;YACjD,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED,aAAa;IACL,kBAAkB,CAAC,SAAoB;QAC7C,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,SAAS;IACD,KAAK,CAAC,gBAAgB,CAAC,SAAoB;QACjD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEjG,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC;gBACvC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC1B,IAAI,EAAE,iBAAS,CAAC,MAAM;gBACtB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM,EAAE,SAAS,CAAC,MAAM;aACzB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,gCAAgC,SAAS,CAAC,KAAK,EAAE;gBAC1D,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kCAAc,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED,SAAS;IACD,KAAK,CAAC,kBAAkB,CAAC,aAAwB;QACvD,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QAClE,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,KAAK,KAAK;YAC/C,CAAC,CAAC,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;YAC5D,CAAC,CAAC,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAE/D,MAAM,YAAY,GAAc;YAC9B,KAAK,EAAE,aAAa,CAAC,KAAK;YAC1B,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACrC,CAAC;IAED,eAAe;IACP,mBAAmB;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAE3C,OAAO,YAAY,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,iBAAiB;IAC5D,CAAC;IAED,SAAS;IACD,KAAK,CAAC,aAAa;QACzB,aAAa;QACb,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,UAAU;QACV,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAE5B,eAAe;QACf,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,CAAC;IAED,YAAY;IACJ,KAAK,CAAC,sBAAsB;QAClC,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEjG,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC3C,IAAI,CAAC;oBACH,MAAM,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,oBAAoB;gBACtB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS;IACD,KAAK,CAAC,gBAAgB;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACnE,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS;QACpF,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;IACpF,CAAC;IAED,SAAS;IACD,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACjG,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC5D,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kCAAc,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAED,OAAO;IACC,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEjG,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpD,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC1C,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;aAC5C,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kCAAc,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED,cAAc;IACN,4BAA4B;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACrF,CAAC;IAED,SAAS;IACF,aAAa;QAQlB,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAC1E,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;QAE5D,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACnC,YAAY;YACZ,aAAa;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;gBAC7B,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;aAC9B;YACD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;SAC7E,CAAC;IACJ,CAAC;CACF;AAjVD,oCAiVC"}
import { BaseStrategy } from '../base/BaseStrategy';
import { CEXExchange } from '../../../shared/types';
import { StrategyExecutionResult, StrategySignal } from '../base/StrategyInterface';
export interface GridStrategyConfig {
    userId: string;
    exchange: CEXExchange;
    symbol: string;
    baseAsset: string;
    quoteAsset: string;
    gridType: 'arithmetic' | 'geometric';
    gridLevels: number;
    upperPrice: number;
    lowerPrice: number;
    totalAmount: number;
    executionInterval: number;
    stopLoss?: number;
    takeProfit?: number;
    rebalanceThreshold: number;
}
export declare class GridStrategy extends BaseStrategy {
    private config;
    private gridOrders;
    private currentPrice;
    private baseBalance;
    private quoteBalance;
    constructor(id: string, name: string);
    initialize(config: GridStrategyConfig): Promise<void>;
    validate(): Promise<boolean>;
    execute(): Promise<StrategyExecutionResult>;
    protected generateSignal(): Promise<StrategySignal | null>;
    protected executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult>;
    private validateConfig;
    private initializeGrid;
    private calculatePriceStep;
    private executeGridLogic;
    private shouldExecuteOrder;
    private executeGridOrder;
    private createCounterOrder;
    private shouldRebalanceGrid;
    private rebalanceGrid;
    private cancelAllPendingOrders;
    private adjustGridCenter;
    private updateCurrentPrice;
    private updateBalances;
    private calculateRequiredQuoteAmount;
    getGridStatus(): {
        totalOrders: number;
        filledOrders: number;
        pendingOrders: number;
        currentPrice: number;
        gridRange: {
            lower: number;
            upper: number;
        };
        profitLoss: number;
    };
}
//# sourceMappingURL=GridStrategy.d.ts.map
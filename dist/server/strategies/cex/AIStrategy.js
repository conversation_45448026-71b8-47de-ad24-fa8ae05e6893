"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIStrategy = void 0;
const BaseStrategy_1 = require("../base/BaseStrategy");
const PredictionService_1 = require("../../ai/services/PredictionService");
const types_1 = require("../../../shared/types");
const StrategyInterface_1 = require("../base/StrategyInterface");
const AIInterface_1 = require("../../ai/base/AIInterface");
class AIStrategy extends BaseStrategy_1.BaseStrategy {
    constructor(id, name) {
        super(id, name, types_1.StrategyType.AI_STRATEGY);
        this.lastPredictions = new Map();
        this.currentPrice = 0;
        this.marketFeatures = [];
        this.predictionService = new PredictionService_1.PredictionService();
    }
    async initialize(config) {
        this.config = config;
        // 验证配置
        this.validateConfig();
        // 初始化市场数据
        await this.initializeMarketData();
        // 验证AI模型
        await this.validateModels();
    }
    async validate() {
        try {
            // 检查配置
            if (!this.config) {
                throw new StrategyInterface_1.ConfigurationError('AI strategy not configured', this.id, this.type);
            }
            // 检查模型可用性
            for (const modelId of this.config.modelIds) {
                try {
                    await this.predictionService.getModelInfo(modelId);
                }
                catch (error) {
                    throw new StrategyInterface_1.ConfigurationError(`Model ${modelId} not found`, this.id, this.type);
                }
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async execute() {
        try {
            // 更新市场数据
            await this.updateMarketData();
            // 生成AI信号
            const aiSignal = await this.generateAISignal();
            if (aiSignal && aiSignal.confidence >= this.config.minConfidence) {
                return await this.executeAISignal(aiSignal);
            }
            return {
                success: true,
                message: 'No AI signal generated or confidence too low',
                timestamp: new Date()
            };
        }
        catch (error) {
            throw new StrategyInterface_1.ExecutionError(`AI strategy execution failed: ${error.message}`, this.id, this.type);
        }
    }
    async generateSignal() {
        const aiSignal = await this.generateAISignal();
        if (!aiSignal || aiSignal.confidence < this.config.minConfidence) {
            return null;
        }
        let signalType;
        switch (aiSignal.action) {
            case 'buy':
                signalType = StrategyInterface_1.SignalType.BUY;
                break;
            case 'sell':
                signalType = StrategyInterface_1.SignalType.SELL;
                break;
            default:
                return null;
        }
        return {
            type: signalType,
            symbol: this.config.symbol,
            price: aiSignal.price,
            amount: aiSignal.quantity,
            confidence: aiSignal.confidence,
            timestamp: aiSignal.timestamp
        };
    }
    async executeSignal(signal) {
        // 转换为AI信号并执行
        const aiSignal = {
            action: signal.type === StrategyInterface_1.SignalType.BUY ? 'buy' : 'sell',
            confidence: signal.confidence,
            price: signal.price,
            quantity: signal.amount,
            reasoning: 'AI model prediction',
            predictions: Array.from(this.lastPredictions.values()),
            timestamp: signal.timestamp
        };
        return await this.executeAISignal(aiSignal);
    }
    // 生成AI信号
    async generateAISignal() {
        try {
            if (this.marketFeatures.length < 100) {
                return null; // 需要足够的历史数据
            }
            // 获取最近的市场特征
            const recentFeatures = this.marketFeatures.slice(-100);
            const predictions = [];
            // 从所有模型获取预测
            for (const modelId of this.config.modelIds) {
                try {
                    const prediction = await this.predictionService.predict(modelId, recentFeatures);
                    predictions.push(prediction);
                    this.lastPredictions.set(modelId, prediction);
                }
                catch (error) {
                    console.warn(`Failed to get prediction from model ${modelId}:`, error);
                }
            }
            if (predictions.length === 0) {
                return null;
            }
            // 集成预测结果
            const ensemblePrediction = this.ensemblePredictions(predictions);
            // 生成交易信号
            const signal = this.generateTradingSignal(ensemblePrediction, predictions);
            return signal;
        }
        catch (error) {
            throw new StrategyInterface_1.ExecutionError(`Failed to generate AI signal: ${error.message}`, this.id, this.type);
        }
    }
    // 集成多个预测结果
    ensemblePredictions(predictions) {
        switch (this.config.ensembleMethod) {
            case 'average':
                return this.averageEnsemble(predictions);
            case 'weighted':
                return this.weightedEnsemble(predictions);
            case 'voting':
                return this.votingEnsemble(predictions);
            default:
                return this.averageEnsemble(predictions);
        }
    }
    // 平均集成
    averageEnsemble(predictions) {
        const avgValue = predictions.reduce((sum, pred) => sum + pred.value, 0) / predictions.length;
        const avgConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;
        return {
            type: AIInterface_1.PredictionType.PRICE,
            value: avgValue,
            confidence: avgConfidence,
            timestamp: new Date(),
            timeframe: this.config.timeframe,
            metadata: {
                ensemble_method: 'average',
                model_count: predictions.length
            }
        };
    }
    // 加权集成
    weightedEnsemble(predictions) {
        const totalWeight = predictions.reduce((sum, pred) => sum + pred.confidence, 0);
        const weightedValue = predictions.reduce((sum, pred) => sum + (pred.value * pred.confidence), 0) / totalWeight;
        const avgConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;
        return {
            type: AIInterface_1.PredictionType.PRICE,
            value: weightedValue,
            confidence: avgConfidence,
            timestamp: new Date(),
            timeframe: this.config.timeframe,
            metadata: {
                ensemble_method: 'weighted',
                model_count: predictions.length
            }
        };
    }
    // 投票集成
    votingEnsemble(predictions) {
        const currentPrice = this.currentPrice;
        let buyVotes = 0;
        let sellVotes = 0;
        let totalConfidence = 0;
        predictions.forEach(pred => {
            if (pred.value > currentPrice) {
                buyVotes += pred.confidence;
            }
            else {
                sellVotes += pred.confidence;
            }
            totalConfidence += pred.confidence;
        });
        const avgValue = predictions.reduce((sum, pred) => sum + pred.value, 0) / predictions.length;
        const confidence = Math.max(buyVotes, sellVotes) / totalConfidence;
        return {
            type: AIInterface_1.PredictionType.DIRECTION,
            value: avgValue,
            confidence,
            timestamp: new Date(),
            timeframe: this.config.timeframe,
            metadata: {
                ensemble_method: 'voting',
                buy_votes: buyVotes,
                sell_votes: sellVotes,
                model_count: predictions.length
            }
        };
    }
    // 生成交易信号
    generateTradingSignal(prediction, allPredictions) {
        const currentPrice = this.currentPrice;
        const priceChange = (prediction.value - currentPrice) / currentPrice;
        const minChangeThreshold = 0.01; // 1% 最小变化阈值
        // 判断交易方向
        let action;
        if (Math.abs(priceChange) < minChangeThreshold) {
            action = 'hold';
        }
        else if (priceChange > 0) {
            action = 'buy';
        }
        else {
            action = 'sell';
        }
        if (action === 'hold') {
            return null;
        }
        // 计算持仓大小
        const baseQuantity = this.config.riskManagement.maxPositionSize;
        const confidenceMultiplier = prediction.confidence;
        const quantity = baseQuantity * confidenceMultiplier;
        // 计算止损和止盈
        const stopLossPercent = this.config.riskManagement.stopLossPercent / 100;
        const takeProfitPercent = this.config.riskManagement.takeProfitPercent / 100;
        let stopLoss;
        let takeProfit;
        if (action === 'buy') {
            stopLoss = currentPrice * (1 - stopLossPercent);
            takeProfit = currentPrice * (1 + takeProfitPercent);
        }
        else {
            stopLoss = currentPrice * (1 + stopLossPercent);
            takeProfit = currentPrice * (1 - takeProfitPercent);
        }
        // 生成推理说明
        const reasoning = this.generateReasoning(prediction, allPredictions, priceChange);
        return {
            action,
            confidence: prediction.confidence,
            price: currentPrice,
            quantity,
            stopLoss,
            takeProfit,
            reasoning,
            predictions: allPredictions,
            timestamp: new Date()
        };
    }
    // 生成推理说明
    generateReasoning(prediction, allPredictions, priceChange) {
        const direction = priceChange > 0 ? 'upward' : 'downward';
        const magnitude = Math.abs(priceChange * 100).toFixed(2);
        const modelCount = allPredictions.length;
        const avgConfidence = (allPredictions.reduce((sum, pred) => sum + pred.confidence, 0) / modelCount * 100).toFixed(1);
        return `AI ensemble of ${modelCount} models predicts ${direction} price movement of ${magnitude}% with ${avgConfidence}% average confidence. Ensemble method: ${this.config.ensembleMethod}.`;
    }
    // 执行AI信号
    async executeAISignal(signal) {
        try {
            // 检查风险限制
            if (!await this.checkRiskLimits()) {
                return {
                    success: false,
                    message: 'Risk limits exceeded',
                    timestamp: new Date()
                };
            }
            // 检查每日损失限制
            const todayLoss = this.calculateTodayLoss();
            if (Math.abs(todayLoss) >= this.config.riskManagement.maxDailyLoss) {
                return {
                    success: false,
                    message: 'Daily loss limit reached',
                    timestamp: new Date()
                };
            }
            // 执行交易
            const order = await this.executeOrder(signal);
            return {
                success: true,
                message: `AI signal executed: ${signal.action}`,
                orderId: order.id,
                amount: signal.quantity,
                price: signal.price,
                timestamp: new Date()
            };
        }
        catch (error) {
            throw new StrategyInterface_1.ExecutionError(`Failed to execute AI signal: ${error.message}`, this.id, this.type);
        }
    }
    // 执行订单
    async executeOrder(signal) {
        // 这里需要根据具体的交易所实现
        // 暂时返回模拟订单
        return {
            id: `ai_order_${Date.now()}`,
            symbol: this.config.symbol,
            side: signal.action,
            amount: signal.quantity,
            price: signal.price,
            status: 'filled'
        };
    }
    // 验证配置
    validateConfig() {
        if (!this.config.symbol) {
            throw new StrategyInterface_1.ConfigurationError('Symbol is required', this.id, this.type);
        }
        if (!this.config.modelIds || this.config.modelIds.length === 0) {
            throw new StrategyInterface_1.ConfigurationError('At least one model ID is required', this.id, this.type);
        }
        if (this.config.minConfidence < 0 || this.config.minConfidence > 1) {
            throw new StrategyInterface_1.ConfigurationError('Min confidence must be between 0 and 1', this.id, this.type);
        }
        if (!['average', 'weighted', 'voting'].includes(this.config.ensembleMethod)) {
            throw new StrategyInterface_1.ConfigurationError('Invalid ensemble method', this.id, this.type);
        }
    }
    // 初始化市场数据
    async initializeMarketData() {
        try {
            // 这里应该从交易所获取历史数据
            // 暂时使用模拟数据
            this.marketFeatures = [];
            this.currentPrice = 100; // 模拟当前价格
        }
        catch (error) {
            throw new StrategyInterface_1.ConfigurationError(`Failed to initialize market data: ${error.message}`, this.id, this.type);
        }
    }
    // 验证模型
    async validateModels() {
        for (const modelId of this.config.modelIds) {
            try {
                const modelInfo = await this.predictionService.getModelInfo(modelId);
                if (!modelInfo.isActive) {
                    throw new StrategyInterface_1.ConfigurationError(`Model ${modelId} is not active`, this.id, this.type);
                }
            }
            catch (error) {
                throw new StrategyInterface_1.ConfigurationError(`Model validation failed for ${modelId}: ${error.message}`, this.id, this.type);
            }
        }
    }
    // 更新市场数据
    async updateMarketData() {
        try {
            // 这里应该获取最新的市场数据
            // 暂时使用模拟数据
            const newFeature = {
                timestamp: new Date(),
                open: this.currentPrice,
                high: this.currentPrice * 1.02,
                low: this.currentPrice * 0.98,
                close: this.currentPrice * (1 + (Math.random() - 0.5) * 0.02),
                volume: Math.random() * 1000000
            };
            this.marketFeatures.push(newFeature);
            this.currentPrice = newFeature.close;
            // 保持数据长度
            if (this.marketFeatures.length > 1000) {
                this.marketFeatures = this.marketFeatures.slice(-1000);
            }
        }
        catch (error) {
            throw new StrategyInterface_1.ExecutionError(`Failed to update market data: ${error.message}`, this.id, this.type);
        }
    }
    // 获取AI策略状态
    getAIStatus() {
        const lastPredictionsObj = {};
        this.lastPredictions.forEach((prediction, modelId) => {
            lastPredictionsObj[modelId] = prediction;
        });
        return {
            modelCount: this.config.modelIds.length,
            lastPredictions: lastPredictionsObj,
            currentPrice: this.currentPrice,
            marketDataPoints: this.marketFeatures.length,
            ensembleMethod: this.config.ensembleMethod,
            minConfidence: this.config.minConfidence
        };
    }
    // 获取预测服务实例
    getPredictionService() {
        return this.predictionService;
    }
}
exports.AIStrategy = AIStrategy;
//# sourceMappingURL=AIStrategy.js.map
import { BaseStrategy } from '../base/BaseStrategy';
import { CEXExchange } from '../../../shared/types';
import { StrategyExecutionResult, StrategySignal } from '../base/StrategyInterface';
export interface TrendFollowingConfig {
    userId: string;
    exchange: CEXExchange;
    symbol: string;
    baseAsset: string;
    quoteAsset: string;
    timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d';
    maType: 'SMA' | 'EMA' | 'WMA';
    fastPeriod: number;
    slowPeriod: number;
    signalPeriod: number;
    positionSize: number;
    stopLossPercent: number;
    takeProfitPercent: number;
    trailingStopPercent: number;
    minTrendStrength: number;
    executionInterval: number;
}
interface TechnicalIndicators {
    fastMA: number;
    slowMA: number;
    signalMA: number;
    rsi: number;
    macd: number;
    macdSignal: number;
    macdHistogram: number;
    atr: number;
    trendStrength: number;
}
interface Position {
    symbol: string;
    side: 'long' | 'short';
    entryPrice: number;
    quantity: number;
    stopLoss: number;
    takeProfit: number;
    trailingStop: number;
    unrealizedPnl: number;
    timestamp: Date;
}
export declare class TrendFollowingStrategy extends BaseStrategy {
    private config;
    private priceHistory;
    private indicators;
    private currentPosition;
    private currentPrice;
    constructor(id: string, name: string);
    initialize(config: TrendFollowingConfig): Promise<void>;
    validate(): Promise<boolean>;
    execute(): Promise<StrategyExecutionResult>;
    protected generateSignal(): Promise<StrategySignal | null>;
    protected executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult>;
    private validateConfig;
    private initializePriceHistory;
    private updatePriceData;
    private calculateIndicators;
    private calculateMA;
    private calculateEMA;
    private calculateWMA;
    private calculateRSI;
    private calculateATR;
    private calculateTrendStrength;
    private calculateSignalConfidence;
    private managePosition;
    private calculatePositionProfit;
    private updateTrailingStop;
    private shouldClosePosition;
    private calculateStopLoss;
    private calculateTakeProfit;
    private calculateTrailingStop;
    getCurrentPosition(): Position | null;
    getIndicators(): TechnicalIndicators | null;
}
export {};
//# sourceMappingURL=TrendFollowingStrategy.d.ts.map
{"version": 3, "file": "RewardInterface.js", "sourceRoot": "", "sources": ["../../../../src/server/rewards/base/RewardInterface.ts"], "names": [], "mappings": ";;;AAAA,OAAO;AACP,IAAY,UAaX;AAbD,WAAY,UAAU;IACpB,+CAAiC,CAAA;IACjC,iDAAmC,CAAA;IACnC,mCAAqB,CAAA;IACrB,yCAA2B,CAAA;IAC3B,yCAA2B,CAAA;IAC3B,qCAAuB,CAAA;IACvB,yDAA2C,CAAA;IAC3C,+CAAiC,CAAA;IACjC,mCAAqB,CAAA;IACrB,2CAA6B,CAAA;IAC7B,2DAA6C,CAAA;IAC7C,yCAA2B,CAAA;AAC7B,CAAC,EAbW,UAAU,0BAAV,UAAU,QAarB;AAED,OAAO;AACP,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,mCAAmB,CAAA;IACnB,qCAAqB,CAAA;IACrB,2CAA2B,CAAA;IAC3B,iCAAiB,CAAA;IACjB,uCAAuB,CAAA;AACzB,CAAC,EANW,YAAY,4BAAZ,YAAY,QAMvB;AAED,OAAO;AACP,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,wBAAW,CAAA;IACX,0BAAa,CAAA;IACb,0BAAa,CAAA;IACb,wBAAW,CAAA;IACX,wBAAW,CAAA;AACb,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AA8LD,QAAQ;AACR,MAAa,WAAY,SAAQ,KAAK;IACpC,YACE,OAAe,EACR,IAAa,EACb,QAAiB;QAExB,KAAK,CAAC,OAAO,CAAC,CAAC;QAHR,SAAI,GAAJ,IAAI,CAAS;QACb,aAAQ,GAAR,QAAQ,CAAS;QAGxB,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;IAC5B,CAAC;CACF;AATD,kCASC;AAED,OAAO;AACP,MAAa,iBAAkB,SAAQ,WAAW;IAChD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;IAClC,CAAC;CACF;AALD,8CAKC;AAED,SAAS;AACT,MAAa,uBAAwB,SAAQ,WAAW;IACtD,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IACxC,CAAC;CACF;AALD,0DAKC;AAED,SAAS;AACT,MAAa,mBAAoB,SAAQ,WAAW;IAClD,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,uBAAuB,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AALD,kDAKC;AAED,QAAQ;AACR,MAAa,aAAc,SAAQ,WAAW;IAC5C,YAAY,OAAe,EAAE,aAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;CAGF;AARD,sCAQC"}
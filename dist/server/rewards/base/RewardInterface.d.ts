export declare enum RewardType {
    TRADING_VOLUME = "trading_volume",
    STRATEGY_PROFIT = "strategy_profit",
    REFERRAL = "referral",
    DAILY_LOGIN = "daily_login",
    FIRST_TRADE = "first_trade",
    MILESTONE = "milestone",
    LIQUIDITY_PROVISION = "liquidity_provision",
    SOCIAL_SHARING = "social_sharing",
    FEEDBACK = "feedback",
    BETA_TESTING = "beta_testing",
    PREMIUM_SUBSCRIPTION = "premium_subscription",
    ACHIEVEMENT = "achievement"
}
export declare enum RewardStatus {
    PENDING = "pending",
    APPROVED = "approved",
    DISTRIBUTED = "distributed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare enum TokenType {
    TAI = "TAI",// TradeAI Token
    USDT = "USDT",
    USDC = "USDC",
    ETH = "ETH",
    BTC = "BTC"
}
export interface RewardRecord {
    id: string;
    userId: string;
    type: RewardType;
    amount: number;
    tokenType: TokenType;
    reason: string;
    status: RewardStatus;
    metadata?: Record<string, any>;
    txHash?: string;
    distributedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface RewardRule {
    id: string;
    type: RewardType;
    name: string;
    description: string;
    isActive: boolean;
    conditions: RewardCondition[];
    rewards: RewardAmount[];
    cooldownPeriod?: number;
    maxClaimsPerUser?: number;
    maxClaimsTotal?: number;
    validFrom?: Date;
    validTo?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface RewardCondition {
    field: string;
    operator: 'eq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'between';
    value: any;
    description?: string;
}
export interface RewardAmount {
    tokenType: TokenType;
    amount: number;
    percentage?: number;
    baseField?: string;
}
export interface UserRewardStats {
    userId: string;
    totalRewards: Record<TokenType, number>;
    totalClaimed: Record<TokenType, number>;
    totalPending: Record<TokenType, number>;
    rewardsByType: Record<RewardType, number>;
    lastRewardDate?: Date;
    streakDays: number;
    level: number;
    nextLevelRequirement: number;
}
export interface RewardCampaign {
    id: string;
    name: string;
    description: string;
    type: 'limited_time' | 'ongoing' | 'milestone';
    isActive: boolean;
    rules: RewardRule[];
    totalBudget?: Record<TokenType, number>;
    distributedAmount?: Record<TokenType, number>;
    participantCount: number;
    startDate: Date;
    endDate?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface ReferralConfig {
    referrerReward: RewardAmount[];
    refereeReward: RewardAmount[];
    minimumTradeVolume?: number;
    validityPeriod?: number;
    maxReferrals?: number;
    tieredRewards?: {
        threshold: number;
        multiplier: number;
    }[];
}
export interface LevelSystem {
    level: number;
    name: string;
    requiredPoints: number;
    benefits: {
        rewardMultiplier: number;
        tradingFeeDiscount: number;
        premiumFeatures: string[];
        withdrawalLimits: Record<TokenType, number>;
    };
    badge?: string;
    color?: string;
}
export interface Achievement {
    id: string;
    name: string;
    description: string;
    category: 'trading' | 'social' | 'milestone' | 'special';
    icon: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    conditions: RewardCondition[];
    rewards: RewardAmount[];
    isHidden: boolean;
    unlockedBy: string[];
    createdAt: Date;
}
export interface DistributionResult {
    success: boolean;
    rewardId: string;
    txHash?: string;
    amount: number;
    tokenType: TokenType;
    message: string;
    timestamp: Date;
}
export interface IRewardSystem {
    createReward(userId: string, type: RewardType, amount: number, tokenType: TokenType, reason: string, metadata?: any): Promise<RewardRecord>;
    getReward(rewardId: string): Promise<RewardRecord | null>;
    getUserRewards(userId: string, status?: RewardStatus, limit?: number, offset?: number): Promise<RewardRecord[]>;
    updateRewardStatus(rewardId: string, status: RewardStatus, txHash?: string): Promise<void>;
    distributeReward(rewardId: string): Promise<DistributionResult>;
    batchDistributeRewards(rewardIds: string[]): Promise<DistributionResult[]>;
    createRewardRule(rule: Omit<RewardRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardRule>;
    updateRewardRule(ruleId: string, updates: Partial<RewardRule>): Promise<RewardRule>;
    deleteRewardRule(ruleId: string): Promise<void>;
    getRewardRules(type?: RewardType, isActive?: boolean): Promise<RewardRule[]>;
    checkAndAwardUser(userId: string, eventType: string, eventData: any): Promise<RewardRecord[]>;
    processUserActivity(userId: string, activity: UserActivity): Promise<RewardRecord[]>;
    getUserRewardStats(userId: string): Promise<UserRewardStats>;
    updateUserLevel(userId: string): Promise<void>;
    processReferral(referrerId: string, refereeId: string): Promise<RewardRecord[]>;
    getReferralStats(userId: string): Promise<any>;
    createCampaign(campaign: Omit<RewardCampaign, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardCampaign>;
    updateCampaign(campaignId: string, updates: Partial<RewardCampaign>): Promise<RewardCampaign>;
    getCampaigns(isActive?: boolean): Promise<RewardCampaign[]>;
    checkAchievements(userId: string): Promise<Achievement[]>;
    unlockAchievement(userId: string, achievementId: string): Promise<RewardRecord[]>;
    getUserAchievements(userId: string): Promise<Achievement[]>;
    getSystemStats(): Promise<any>;
    exportRewards(startDate: Date, endDate: Date): Promise<any>;
    validateRewardBudget(): Promise<boolean>;
}
export interface UserActivity {
    type: 'trade' | 'login' | 'referral' | 'social' | 'subscription' | 'feedback';
    userId: string;
    data: any;
    timestamp: Date;
}
export declare class RewardError extends Error {
    code?: string | undefined;
    rewardId?: string | undefined;
    constructor(message: string, code?: string | undefined, rewardId?: string | undefined);
}
export declare class DistributionError extends RewardError {
    constructor(message: string, rewardId?: string);
}
export declare class InsufficientBudgetError extends RewardError {
    constructor(message: string);
}
export declare class RuleValidationError extends RewardError {
    constructor(message: string);
}
export declare class CooldownError extends RewardError {
    constructor(message: string, remainingTime: number);
    remainingTime: number;
}
//# sourceMappingURL=RewardInterface.d.ts.map
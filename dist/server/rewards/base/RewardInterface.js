"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CooldownError = exports.RuleValidationError = exports.InsufficientBudgetError = exports.DistributionError = exports.RewardError = exports.TokenType = exports.RewardStatus = exports.RewardType = void 0;
// 奖励类型
var RewardType;
(function (RewardType) {
    RewardType["TRADING_VOLUME"] = "trading_volume";
    RewardType["STRATEGY_PROFIT"] = "strategy_profit";
    RewardType["REFERRAL"] = "referral";
    RewardType["DAILY_LOGIN"] = "daily_login";
    RewardType["FIRST_TRADE"] = "first_trade";
    RewardType["MILESTONE"] = "milestone";
    RewardType["LIQUIDITY_PROVISION"] = "liquidity_provision";
    RewardType["SOCIAL_SHARING"] = "social_sharing";
    RewardType["FEEDBACK"] = "feedback";
    RewardType["BETA_TESTING"] = "beta_testing";
    RewardType["PREMIUM_SUBSCRIPTION"] = "premium_subscription";
    RewardType["ACHIEVEMENT"] = "achievement";
})(RewardType || (exports.RewardType = RewardType = {}));
// 奖励状态
var RewardStatus;
(function (RewardStatus) {
    RewardStatus["PENDING"] = "pending";
    RewardStatus["APPROVED"] = "approved";
    RewardStatus["DISTRIBUTED"] = "distributed";
    RewardStatus["FAILED"] = "failed";
    RewardStatus["CANCELLED"] = "cancelled";
})(RewardStatus || (exports.RewardStatus = RewardStatus = {}));
// 代币类型
var TokenType;
(function (TokenType) {
    TokenType["TAI"] = "TAI";
    TokenType["USDT"] = "USDT";
    TokenType["USDC"] = "USDC";
    TokenType["ETH"] = "ETH";
    TokenType["BTC"] = "BTC";
})(TokenType || (exports.TokenType = TokenType = {}));
// 奖励错误类
class RewardError extends Error {
    constructor(message, code, rewardId) {
        super(message);
        this.code = code;
        this.rewardId = rewardId;
        this.name = 'RewardError';
    }
}
exports.RewardError = RewardError;
// 分发错误
class DistributionError extends RewardError {
    constructor(message, rewardId) {
        super(message, 'DISTRIBUTION_ERROR', rewardId);
        this.name = 'DistributionError';
    }
}
exports.DistributionError = DistributionError;
// 余额不足错误
class InsufficientBudgetError extends RewardError {
    constructor(message) {
        super(message, 'INSUFFICIENT_BUDGET');
        this.name = 'InsufficientBudgetError';
    }
}
exports.InsufficientBudgetError = InsufficientBudgetError;
// 规则验证错误
class RuleValidationError extends RewardError {
    constructor(message) {
        super(message, 'RULE_VALIDATION_ERROR');
        this.name = 'RuleValidationError';
    }
}
exports.RuleValidationError = RuleValidationError;
// 冷却期错误
class CooldownError extends RewardError {
    constructor(message, remainingTime) {
        super(message, 'COOLDOWN_ERROR');
        this.name = 'CooldownError';
        this.remainingTime = remainingTime;
    }
}
exports.CooldownError = CooldownError;
//# sourceMappingURL=RewardInterface.js.map
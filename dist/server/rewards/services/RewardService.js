"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RewardService = void 0;
const events_1 = require("events");
const database_1 = require("../../models/database");
const logger_1 = require("../../utils/logger");
const RewardInterface_1 = require("../base/RewardInterface");
class RewardService extends events_1.EventEmitter {
    constructor() {
        super();
        this.rewardRules = new Map();
        this.campaigns = new Map();
        this.achievements = new Map();
        this.initializeDefaultRules();
        this.initializeAchievements();
    }
    static getInstance() {
        if (!RewardService.instance) {
            RewardService.instance = new RewardService();
        }
        return RewardService.instance;
    }
    // 创建奖励记录
    async createReward(userId, type, amount, tokenType, reason, metadata) {
        try {
            const result = await database_1.db.query(`INSERT INTO rewards (user_id, amount, reason, token_symbol, is_distributed, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         RETURNING id, user_id, amount, reason, token_symbol, is_distributed, created_at, updated_at`, [userId, amount, reason, tokenType, false]);
            const record = result.rows[0];
            const rewardRecord = {
                id: record.id,
                userId: record.user_id,
                type,
                amount: parseFloat(record.amount),
                tokenType: record.token_symbol,
                reason: record.reason,
                status: record.is_distributed ? RewardInterface_1.RewardStatus.DISTRIBUTED : RewardInterface_1.RewardStatus.PENDING,
                metadata,
                createdAt: record.created_at,
                updatedAt: record.updated_at
            };
            logger_1.logUtils.logUserAction(userId, 'reward_created', {
                rewardId: rewardRecord.id,
                type,
                amount,
                tokenType,
                reason
            });
            this.emit('rewardCreated', rewardRecord);
            return rewardRecord;
        }
        catch (error) {
            logger_1.logger.error('Error creating reward:', error);
            throw new RewardInterface_1.RewardError(`Failed to create reward: ${error.message}`);
        }
    }
    // 获取奖励记录
    async getReward(rewardId) {
        try {
            const result = await database_1.db.query('SELECT * FROM rewards WHERE id = $1', [rewardId]);
            if (result.rows.length === 0) {
                return null;
            }
            const record = result.rows[0];
            return {
                id: record.id,
                userId: record.user_id,
                type: RewardInterface_1.RewardType.TRADING_VOLUME, // 需要从metadata或其他字段获取
                amount: parseFloat(record.amount),
                tokenType: record.token_symbol,
                reason: record.reason,
                status: record.is_distributed ? RewardInterface_1.RewardStatus.DISTRIBUTED : RewardInterface_1.RewardStatus.PENDING,
                txHash: record.tx_hash,
                distributedAt: record.distributed_at,
                createdAt: record.created_at,
                updatedAt: record.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting reward:', error);
            throw new RewardInterface_1.RewardError(`Failed to get reward: ${error.message}`, 'GET_ERROR', rewardId);
        }
    }
    // 获取用户奖励记录
    async getUserRewards(userId, status, limit = 50, offset = 0) {
        try {
            let query = 'SELECT * FROM rewards WHERE user_id = $1';
            const params = [userId];
            if (status) {
                query += ' AND is_distributed = $2';
                params.push(status === RewardInterface_1.RewardStatus.DISTRIBUTED);
            }
            query += ' ORDER BY created_at DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
            params.push(limit, offset);
            const result = await database_1.db.query(query, params);
            return result.rows.map(record => ({
                id: record.id,
                userId: record.user_id,
                type: RewardInterface_1.RewardType.TRADING_VOLUME, // 需要从metadata获取
                amount: parseFloat(record.amount),
                tokenType: record.token_symbol,
                reason: record.reason,
                status: record.is_distributed ? RewardInterface_1.RewardStatus.DISTRIBUTED : RewardInterface_1.RewardStatus.PENDING,
                txHash: record.tx_hash,
                distributedAt: record.distributed_at,
                createdAt: record.created_at,
                updatedAt: record.updated_at
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting user rewards:', error);
            throw new RewardInterface_1.RewardError(`Failed to get user rewards: ${error.message}`);
        }
    }
    // 更新奖励状态
    async updateRewardStatus(rewardId, status, txHash) {
        try {
            const isDistributed = status === RewardInterface_1.RewardStatus.DISTRIBUTED;
            const distributedAt = isDistributed ? new Date() : null;
            await database_1.db.query(`UPDATE rewards 
         SET is_distributed = $1, tx_hash = $2, updated_at = CURRENT_TIMESTAMP
         WHERE id = $3`, [isDistributed, txHash, rewardId]);
            this.emit('rewardStatusUpdated', { rewardId, status, txHash });
        }
        catch (error) {
            logger_1.logger.error('Error updating reward status:', error);
            throw new RewardInterface_1.RewardError(`Failed to update reward status: ${error.message}`, 'UPDATE_ERROR', rewardId);
        }
    }
    // 分发奖励
    async distributeReward(rewardId) {
        try {
            const reward = await this.getReward(rewardId);
            if (!reward) {
                throw new RewardInterface_1.RewardError('Reward not found', 'NOT_FOUND', rewardId);
            }
            if (reward.status === RewardInterface_1.RewardStatus.DISTRIBUTED) {
                throw new RewardInterface_1.DistributionError('Reward already distributed', rewardId);
            }
            // 检查预算
            const budgetCheck = await this.checkBudget(reward.tokenType, reward.amount);
            if (!budgetCheck) {
                throw new RewardInterface_1.InsufficientBudgetError(`Insufficient budget for ${reward.tokenType}`);
            }
            // 模拟分发到用户钱包
            const txHash = await this.performDistribution(reward);
            // 更新奖励状态
            await this.updateRewardStatus(rewardId, RewardInterface_1.RewardStatus.DISTRIBUTED, txHash);
            // 更新用户总奖励
            await this.updateUserTotalRewards(reward.userId, reward.tokenType, reward.amount);
            const result = {
                success: true,
                rewardId,
                txHash,
                amount: reward.amount,
                tokenType: reward.tokenType,
                message: 'Reward distributed successfully',
                timestamp: new Date()
            };
            logger_1.logUtils.logUserAction(reward.userId, 'reward_distributed', {
                rewardId,
                amount: reward.amount,
                tokenType: reward.tokenType,
                txHash
            });
            this.emit('rewardDistributed', result);
            return result;
        }
        catch (error) {
            logger_1.logger.error('Error distributing reward:', error);
            const result = {
                success: false,
                rewardId,
                amount: 0,
                tokenType: RewardInterface_1.TokenType.TAI,
                message: error.message,
                timestamp: new Date()
            };
            return result;
        }
    }
    // 批量分发奖励
    async batchDistributeRewards(rewardIds) {
        const results = [];
        for (const rewardId of rewardIds) {
            try {
                const result = await this.distributeReward(rewardId);
                results.push(result);
            }
            catch (error) {
                results.push({
                    success: false,
                    rewardId,
                    amount: 0,
                    tokenType: RewardInterface_1.TokenType.TAI,
                    message: error.message,
                    timestamp: new Date()
                });
            }
        }
        return results;
    }
    // 创建奖励规则
    async createRewardRule(rule) {
        const ruleId = this.generateRuleId();
        const newRule = {
            ...rule,
            id: ruleId,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.rewardRules.set(ruleId, newRule);
        this.emit('ruleCreated', newRule);
        return newRule;
    }
    // 更新奖励规则
    async updateRewardRule(ruleId, updates) {
        const rule = this.rewardRules.get(ruleId);
        if (!rule) {
            throw new RewardInterface_1.RuleValidationError(`Rule not found: ${ruleId}`);
        }
        const updatedRule = {
            ...rule,
            ...updates,
            updatedAt: new Date()
        };
        this.rewardRules.set(ruleId, updatedRule);
        this.emit('ruleUpdated', updatedRule);
        return updatedRule;
    }
    // 删除奖励规则
    async deleteRewardRule(ruleId) {
        if (!this.rewardRules.has(ruleId)) {
            throw new RewardInterface_1.RuleValidationError(`Rule not found: ${ruleId}`);
        }
        this.rewardRules.delete(ruleId);
        this.emit('ruleDeleted', { ruleId });
    }
    // 获取奖励规则
    async getRewardRules(type, isActive) {
        let rules = Array.from(this.rewardRules.values());
        if (type) {
            rules = rules.filter(rule => rule.type === type);
        }
        if (isActive !== undefined) {
            rules = rules.filter(rule => rule.isActive === isActive);
        }
        return rules;
    }
    // 检查并奖励用户
    async checkAndAwardUser(userId, eventType, eventData) {
        const rewards = [];
        const applicableRules = await this.getApplicableRules(eventType, eventData);
        for (const rule of applicableRules) {
            try {
                // 检查冷却期
                if (await this.isInCooldown(userId, rule.id)) {
                    continue;
                }
                // 检查条件
                if (this.evaluateConditions(rule.conditions, eventData)) {
                    // 计算奖励金额
                    for (const rewardAmount of rule.rewards) {
                        const amount = this.calculateRewardAmount(rewardAmount, eventData);
                        const reward = await this.createReward(userId, rule.type, amount, rewardAmount.tokenType, `${rule.name}: ${rule.description}`, { ruleId: rule.id, eventType, eventData });
                        rewards.push(reward);
                    }
                    // 记录规则使用
                    await this.recordRuleUsage(userId, rule.id);
                }
            }
            catch (error) {
                logger_1.logger.error(`Error processing rule ${rule.id} for user ${userId}:`, error);
            }
        }
        return rewards;
    }
    // 处理用户活动
    async processUserActivity(userId, activity) {
        return this.checkAndAwardUser(userId, activity.type, activity.data);
    }
    // 获取用户奖励统计
    async getUserRewardStats(userId) {
        try {
            const result = await database_1.db.query('SELECT token_symbol, SUM(amount) as total FROM rewards WHERE user_id = $1 GROUP BY token_symbol', [userId]);
            const totalRewards = {};
            const totalClaimed = {};
            const totalPending = {};
            // 初始化
            Object.values(RewardInterface_1.TokenType).forEach(token => {
                totalRewards[token] = 0;
                totalClaimed[token] = 0;
                totalPending[token] = 0;
            });
            // 处理查询结果
            result.rows.forEach(row => {
                const token = row.token_symbol;
                totalRewards[token] = parseFloat(row.total);
            });
            // 获取已分发和待分发的奖励
            const claimedResult = await database_1.db.query('SELECT token_symbol, SUM(amount) as total FROM rewards WHERE user_id = $1 AND is_distributed = true GROUP BY token_symbol', [userId]);
            claimedResult.rows.forEach(row => {
                const token = row.token_symbol;
                totalClaimed[token] = parseFloat(row.total);
                totalPending[token] = totalRewards[token] - totalClaimed[token];
            });
            return {
                userId,
                totalRewards,
                totalClaimed,
                totalPending,
                rewardsByType: {}, // 需要额外查询
                streakDays: 0, // 需要计算连续登录天数
                level: 1, // 需要根据积分计算等级
                nextLevelRequirement: 1000
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting user reward stats:', error);
            throw new RewardInterface_1.RewardError(`Failed to get user reward stats: ${error.message}`);
        }
    }
    // 更新用户等级
    async updateUserLevel(userId) {
        // 实现用户等级更新逻辑
    }
    // 处理推荐
    async processReferral(referrerId, refereeId) {
        const rewards = [];
        try {
            // 给推荐人奖励
            const referrerReward = await this.createReward(referrerId, RewardInterface_1.RewardType.REFERRAL, 50, // 推荐奖励金额
            RewardInterface_1.TokenType.TAI, `Referral reward for inviting user ${refereeId}`);
            rewards.push(referrerReward);
            // 给被推荐人奖励
            const refereeReward = await this.createReward(refereeId, RewardInterface_1.RewardType.REFERRAL, 25, // 新用户奖励金额
            RewardInterface_1.TokenType.TAI, `Welcome bonus for joining through referral`);
            rewards.push(refereeReward);
            logger_1.logUtils.logUserAction(referrerId, 'referral_processed', {
                refereeId,
                referrerReward: referrerReward.amount,
                refereeReward: refereeReward.amount
            });
            return rewards;
        }
        catch (error) {
            logger_1.logger.error('Error processing referral:', error);
            throw new RewardInterface_1.RewardError(`Failed to process referral: ${error.message}`);
        }
    }
    // 获取推荐统计
    async getReferralStats(userId) {
        // 实现推荐统计逻辑
        return {
            totalReferrals: 0,
            successfulReferrals: 0,
            totalEarned: 0,
            pendingRewards: 0
        };
    }
    // 创建活动
    async createCampaign(campaign) {
        const campaignId = this.generateCampaignId();
        const newCampaign = {
            ...campaign,
            id: campaignId,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.campaigns.set(campaignId, newCampaign);
        this.emit('campaignCreated', newCampaign);
        return newCampaign;
    }
    // 更新活动
    async updateCampaign(campaignId, updates) {
        const campaign = this.campaigns.get(campaignId);
        if (!campaign) {
            throw new RewardInterface_1.RewardError(`Campaign not found: ${campaignId}`);
        }
        const updatedCampaign = {
            ...campaign,
            ...updates,
            updatedAt: new Date()
        };
        this.campaigns.set(campaignId, updatedCampaign);
        this.emit('campaignUpdated', updatedCampaign);
        return updatedCampaign;
    }
    // 获取活动列表
    async getCampaigns(isActive) {
        let campaigns = Array.from(this.campaigns.values());
        if (isActive !== undefined) {
            campaigns = campaigns.filter(campaign => campaign.isActive === isActive);
        }
        return campaigns;
    }
    // 检查成就
    async checkAchievements(userId) {
        const unlockedAchievements = [];
        for (const achievement of this.achievements.values()) {
            if (!achievement.unlockedBy.includes(userId)) {
                // 检查成就条件
                const userData = await this.getUserActivityData(userId);
                if (this.evaluateConditions(achievement.conditions, userData)) {
                    unlockedAchievements.push(achievement);
                    achievement.unlockedBy.push(userId);
                }
            }
        }
        return unlockedAchievements;
    }
    // 解锁成就
    async unlockAchievement(userId, achievementId) {
        const achievement = this.achievements.get(achievementId);
        if (!achievement) {
            throw new RewardInterface_1.RewardError(`Achievement not found: ${achievementId}`);
        }
        const rewards = [];
        for (const rewardAmount of achievement.rewards) {
            const reward = await this.createReward(userId, RewardInterface_1.RewardType.ACHIEVEMENT, rewardAmount.amount, rewardAmount.tokenType, `Achievement unlocked: ${achievement.name}`, { achievementId });
            rewards.push(reward);
        }
        return rewards;
    }
    // 获取用户成就
    async getUserAchievements(userId) {
        return Array.from(this.achievements.values()).filter(achievement => achievement.unlockedBy.includes(userId));
    }
    // 获取系统统计
    async getSystemStats() {
        try {
            const totalRewardsResult = await database_1.db.query('SELECT token_symbol, SUM(amount) as total, COUNT(*) as count FROM rewards GROUP BY token_symbol');
            const distributedRewardsResult = await database_1.db.query('SELECT token_symbol, SUM(amount) as total, COUNT(*) as count FROM rewards WHERE is_distributed = true GROUP BY token_symbol');
            return {
                totalRewards: totalRewardsResult.rows,
                distributedRewards: distributedRewardsResult.rows,
                activeRules: this.rewardRules.size,
                activeCampaigns: Array.from(this.campaigns.values()).filter(c => c.isActive).length,
                totalAchievements: this.achievements.size
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting system stats:', error);
            throw new RewardInterface_1.RewardError(`Failed to get system stats: ${error.message}`);
        }
    }
    // 导出奖励数据
    async exportRewards(startDate, endDate) {
        try {
            const result = await database_1.db.query('SELECT * FROM rewards WHERE created_at BETWEEN $1 AND $2 ORDER BY created_at DESC', [startDate, endDate]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error exporting rewards:', error);
            throw new RewardInterface_1.RewardError(`Failed to export rewards: ${error.message}`);
        }
    }
    // 验证奖励预算
    async validateRewardBudget() {
        // 实现预算验证逻辑
        return true;
    }
    // 私有辅助方法
    async checkBudget(tokenType, amount) {
        // 模拟预算检查
        return true;
    }
    async performDistribution(reward) {
        // 模拟分发到区块链
        return `0x${Math.random().toString(16).substr(2, 64)}`;
    }
    async updateUserTotalRewards(userId, tokenType, amount) {
        await database_1.db.query('UPDATE users SET total_rewards = total_rewards + $1 WHERE id = $2', [amount, userId]);
    }
    generateRuleId() {
        return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateCampaignId() {
        return `campaign_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async getApplicableRules(eventType, eventData) {
        return Array.from(this.rewardRules.values()).filter(rule => rule.isActive);
    }
    async isInCooldown(userId, ruleId) {
        // 检查用户是否在冷却期内
        return false;
    }
    evaluateConditions(conditions, data) {
        // 评估奖励条件
        return true;
    }
    calculateRewardAmount(rewardAmount, eventData) {
        if (rewardAmount.percentage && rewardAmount.baseField) {
            const baseValue = eventData[rewardAmount.baseField] || 0;
            return baseValue * (rewardAmount.percentage / 100);
        }
        return rewardAmount.amount;
    }
    async recordRuleUsage(userId, ruleId) {
        // 记录规则使用情况
    }
    async getUserActivityData(userId) {
        // 获取用户活动数据用于成就检查
        return {};
    }
    initializeDefaultRules() {
        // 初始化默认奖励规则
    }
    initializeAchievements() {
        // 初始化成就系统
    }
}
exports.RewardService = RewardService;
//# sourceMappingURL=RewardService.js.map
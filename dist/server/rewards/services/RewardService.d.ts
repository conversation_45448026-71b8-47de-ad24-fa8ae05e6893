import { EventEmitter } from 'events';
import { IRewardSystem, RewardRecord, RewardRule, RewardType, RewardStatus, TokenType, UserRewardStats, RewardCampaign, Achievement, UserActivity, DistributionResult } from '../base/RewardInterface';
export declare class RewardService extends EventEmitter implements IRewardSystem {
    private static instance;
    private rewardRules;
    private campaigns;
    private achievements;
    private constructor();
    static getInstance(): RewardService;
    createReward(userId: string, type: RewardType, amount: number, tokenType: TokenType, reason: string, metadata?: any): Promise<RewardRecord>;
    getReward(rewardId: string): Promise<RewardRecord | null>;
    getUserRewards(userId: string, status?: RewardStatus, limit?: number, offset?: number): Promise<RewardRecord[]>;
    updateRewardStatus(rewardId: string, status: RewardStatus, txHash?: string): Promise<void>;
    distributeReward(rewardId: string): Promise<DistributionResult>;
    batchDistributeRewards(rewardIds: string[]): Promise<DistributionResult[]>;
    createRewardRule(rule: Omit<RewardRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardRule>;
    updateRewardRule(ruleId: string, updates: Partial<RewardRule>): Promise<RewardRule>;
    deleteRewardRule(ruleId: string): Promise<void>;
    getRewardRules(type?: RewardType, isActive?: boolean): Promise<RewardRule[]>;
    checkAndAwardUser(userId: string, eventType: string, eventData: any): Promise<RewardRecord[]>;
    processUserActivity(userId: string, activity: UserActivity): Promise<RewardRecord[]>;
    getUserRewardStats(userId: string): Promise<UserRewardStats>;
    updateUserLevel(userId: string): Promise<void>;
    processReferral(referrerId: string, refereeId: string): Promise<RewardRecord[]>;
    getReferralStats(userId: string): Promise<any>;
    createCampaign(campaign: Omit<RewardCampaign, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardCampaign>;
    updateCampaign(campaignId: string, updates: Partial<RewardCampaign>): Promise<RewardCampaign>;
    getCampaigns(isActive?: boolean): Promise<RewardCampaign[]>;
    checkAchievements(userId: string): Promise<Achievement[]>;
    unlockAchievement(userId: string, achievementId: string): Promise<RewardRecord[]>;
    getUserAchievements(userId: string): Promise<Achievement[]>;
    getSystemStats(): Promise<any>;
    exportRewards(startDate: Date, endDate: Date): Promise<any>;
    validateRewardBudget(): Promise<boolean>;
    private checkBudget;
    private performDistribution;
    private updateUserTotalRewards;
    private generateRuleId;
    private generateCampaignId;
    private getApplicableRules;
    private isInCooldown;
    private evaluateConditions;
    private calculateRewardAmount;
    private recordRuleUsage;
    private getUserActivityData;
    private initializeDefaultRules;
    private initializeAchievements;
}
//# sourceMappingURL=RewardService.d.ts.map
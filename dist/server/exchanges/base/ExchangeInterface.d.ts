import { CEXExchange, OrderType, OrderStatus } from '../../../shared/types';
export interface ExchangeAccountInfo {
    balances: ExchangeBalance[];
    permissions: string[];
    tradingEnabled: boolean;
    withdrawEnabled: boolean;
}
export interface ExchangeBalance {
    asset: string;
    free: number;
    locked: number;
    total: number;
}
export interface ExchangeOrder {
    id: string;
    clientOrderId?: string;
    symbol: string;
    type: OrderType;
    side: 'buy' | 'sell';
    amount: number;
    price?: number;
    status: OrderStatus;
    filled: number;
    remaining: number;
    cost: number;
    fee: number;
    feeAsset: string;
    timestamp: number;
    lastTradeTimestamp?: number;
}
export interface ExchangeTicker {
    symbol: string;
    bid: number;
    ask: number;
    last: number;
    high: number;
    low: number;
    volume: number;
    quoteVolume: number;
    change: number;
    percentage: number;
    timestamp: number;
}
export interface ExchangeKline {
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}
export interface ExchangeOrderBook {
    symbol: string;
    bids: [number, number][];
    asks: [number, number][];
    timestamp: number;
}
export interface ExchangeTrade {
    id: string;
    orderId: string;
    symbol: string;
    side: 'buy' | 'sell';
    amount: number;
    price: number;
    cost: number;
    fee: number;
    feeAsset: string;
    timestamp: number;
}
export interface CreateOrderParams {
    symbol: string;
    type: OrderType;
    side: 'buy' | 'sell';
    amount: number;
    price?: number;
    stopPrice?: number;
    timeInForce?: 'GTC' | 'IOC' | 'FOK';
    clientOrderId?: string;
}
export interface ExchangeConfig {
    apiKey: string;
    secretKey: string;
    passphrase?: string;
    sandbox?: boolean;
    rateLimit?: number;
    timeout?: number;
}
export interface IExchange {
    readonly name: CEXExchange;
    readonly isConnected: boolean;
    initialize(config: ExchangeConfig): Promise<void>;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    testConnection(): Promise<boolean>;
    getAccountInfo(): Promise<ExchangeAccountInfo>;
    getBalances(): Promise<ExchangeBalance[]>;
    getBalance(asset: string): Promise<ExchangeBalance>;
    getTicker(symbol: string): Promise<ExchangeTicker>;
    getTickers(symbols?: string[]): Promise<ExchangeTicker[]>;
    getOrderBook(symbol: string, limit?: number): Promise<ExchangeOrderBook>;
    getKlines(symbol: string, interval: string, limit?: number, startTime?: number, endTime?: number): Promise<ExchangeKline[]>;
    createOrder(params: CreateOrderParams): Promise<ExchangeOrder>;
    cancelOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
    cancelAllOrders(symbol?: string): Promise<ExchangeOrder[]>;
    getOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
    getOpenOrders(symbol?: string): Promise<ExchangeOrder[]>;
    getOrderHistory(symbol?: string, limit?: number): Promise<ExchangeOrder[]>;
    getTrades(symbol?: string, limit?: number): Promise<ExchangeTrade[]>;
    getMyTrades(symbol: string, limit?: number): Promise<ExchangeTrade[]>;
    getSymbols(): Promise<string[]>;
    getSymbolInfo(symbol: string): Promise<any>;
    formatSymbol(base: string, quote: string): string;
    parseSymbol(symbol: string): {
        base: string;
        quote: string;
    };
    getTradingFees(symbol?: string): Promise<{
        maker: number;
        taker: number;
    }>;
    subscribeToTicker?(symbol: string, callback: (ticker: ExchangeTicker) => void): void;
    subscribeToOrderBook?(symbol: string, callback: (orderBook: ExchangeOrderBook) => void): void;
    subscribeToTrades?(symbol: string, callback: (trade: ExchangeTrade) => void): void;
    subscribeToOrders?(callback: (order: ExchangeOrder) => void): void;
    unsubscribeFromTicker?(symbol: string): void;
    unsubscribeFromOrderBook?(symbol: string): void;
    unsubscribeFromTrades?(symbol: string): void;
    unsubscribeFromOrders?(): void;
}
export declare class ExchangeError extends Error {
    exchange: CEXExchange;
    code?: string | undefined;
    statusCode?: number | undefined;
    constructor(message: string, exchange: CEXExchange, code?: string | undefined, statusCode?: number | undefined);
}
export declare class NetworkError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange);
}
export declare class AuthenticationError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange);
}
export declare class PermissionError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange);
}
export declare class RateLimitError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange);
}
export declare class InsufficientBalanceError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange);
}
export declare class OrderError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange, code?: string);
}
export declare class MarketError extends ExchangeError {
    constructor(message: string, exchange: CEXExchange);
}
//# sourceMappingURL=ExchangeInterface.d.ts.map
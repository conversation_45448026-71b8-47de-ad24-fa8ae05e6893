"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketError = exports.OrderError = exports.InsufficientBalanceError = exports.RateLimitError = exports.PermissionError = exports.AuthenticationError = exports.NetworkError = exports.ExchangeError = void 0;
// 交易所错误类
class ExchangeError extends Error {
    constructor(message, exchange, code, statusCode) {
        super(message);
        this.exchange = exchange;
        this.code = code;
        this.statusCode = statusCode;
        this.name = 'ExchangeError';
    }
}
exports.ExchangeError = ExchangeError;
// 网络错误
class NetworkError extends ExchangeError {
    constructor(message, exchange) {
        super(message, exchange, 'NETWORK_ERROR');
        this.name = 'NetworkError';
    }
}
exports.NetworkError = NetworkError;
// 认证错误
class AuthenticationError extends ExchangeError {
    constructor(message, exchange) {
        super(message, exchange, 'AUTH_ERROR', 401);
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
// 权限错误
class PermissionError extends ExchangeError {
    constructor(message, exchange) {
        super(message, exchange, 'PERMISSION_ERROR', 403);
        this.name = 'PermissionError';
    }
}
exports.PermissionError = PermissionError;
// 速率限制错误
class RateLimitError extends ExchangeError {
    constructor(message, exchange) {
        super(message, exchange, 'RATE_LIMIT_ERROR', 429);
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
// 余额不足错误
class InsufficientBalanceError extends ExchangeError {
    constructor(message, exchange) {
        super(message, exchange, 'INSUFFICIENT_BALANCE');
        this.name = 'InsufficientBalanceError';
    }
}
exports.InsufficientBalanceError = InsufficientBalanceError;
// 订单错误
class OrderError extends ExchangeError {
    constructor(message, exchange, code) {
        super(message, exchange, code || 'ORDER_ERROR');
        this.name = 'OrderError';
    }
}
exports.OrderError = OrderError;
// 市场错误
class MarketError extends ExchangeError {
    constructor(message, exchange) {
        super(message, exchange, 'MARKET_ERROR');
        this.name = 'MarketError';
    }
}
exports.MarketError = MarketError;
//# sourceMappingURL=ExchangeInterface.js.map
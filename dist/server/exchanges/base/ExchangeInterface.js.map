{"version": 3, "file": "ExchangeInterface.js", "sourceRoot": "", "sources": ["../../../../src/server/exchanges/base/ExchangeInterface.ts"], "names": [], "mappings": ";;;AAkKA,SAAS;AACT,MAAa,aAAc,SAAQ,KAAK;IACtC,YACE,OAAe,EACR,QAAqB,EACrB,IAAa,EACb,UAAmB;QAE1B,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,aAAQ,GAAR,QAAQ,CAAa;QACrB,SAAI,GAAJ,IAAI,CAAS;QACb,eAAU,GAAV,UAAU,CAAS;QAG1B,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AAVD,sCAUC;AAED,OAAO;AACP,MAAa,YAAa,SAAQ,aAAa;IAC7C,YAAY,OAAe,EAAE,QAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC7B,CAAC;CACF;AALD,oCAKC;AAED,OAAO;AACP,MAAa,mBAAoB,SAAQ,aAAa;IACpD,YAAY,OAAe,EAAE,QAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AALD,kDAKC;AAED,OAAO;AACP,MAAa,eAAgB,SAAQ,aAAa;IAChD,YAAY,OAAe,EAAE,QAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AALD,0CAKC;AAED,SAAS;AACT,MAAa,cAAe,SAAQ,aAAa;IAC/C,YAAY,OAAe,EAAE,QAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,wCAKC;AAED,SAAS;AACT,MAAa,wBAAyB,SAAQ,aAAa;IACzD,YAAY,OAAe,EAAE,QAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACzC,CAAC;CACF;AALD,4DAKC;AAED,OAAO;AACP,MAAa,UAAW,SAAQ,aAAa;IAC3C,YAAY,OAAe,EAAE,QAAqB,EAAE,IAAa;QAC/D,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,IAAI,aAAa,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;IAC3B,CAAC;CACF;AALD,gCAKC;AAED,OAAO;AACP,MAAa,WAAY,SAAQ,aAAa;IAC5C,YAAY,OAAe,EAAE,QAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;IAC5B,CAAC;CACF;AALD,kCAKC"}
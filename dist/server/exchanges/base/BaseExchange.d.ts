import { AxiosInstance } from 'axios';
import { EventEmitter } from 'events';
import { CEXExchange } from '../../../shared/types';
import { IExchange, ExchangeConfig, ExchangeAccountInfo, ExchangeBalance, ExchangeOrder, ExchangeTicker, ExchangeOrderBook, ExchangeKline, ExchangeTrade, CreateOrderParams } from './ExchangeInterface';
export declare abstract class BaseExchange extends EventEmitter implements IExchange {
    readonly name: CEXExchange;
    protected config: ExchangeConfig;
    protected httpClient: AxiosInstance;
    protected _isConnected: boolean;
    protected rateLimiter: Map<string, number>;
    protected lastRequestTime: number;
    constructor(name: CEXExchange);
    get isConnected(): boolean;
    protected abstract getBaseUrl(): string;
    protected abstract signRequest(method: string, path: string, params?: any, body?: any): any;
    protected abstract getHeaders(signature?: any): Record<string, string>;
    initialize(config: ExchangeConfig): Promise<void>;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    testConnection(): Promise<boolean>;
    protected enforceRateLimit(): void;
    protected handleHttpError(error: any): void;
    protected makeAuthenticatedRequest(method: string, path: string, params?: any, body?: any): Promise<any>;
    protected makePublicRequest(method: string, path: string, params?: any): Promise<any>;
    protected generateHmacSignature(message: string, secret: string, algorithm?: string): string;
    protected buildQueryString(params: Record<string, any>): string;
    protected formatNumber(num: number, precision?: number): number;
    protected parseTimestamp(timestamp: any): number;
    abstract getAccountInfo(): Promise<ExchangeAccountInfo>;
    abstract getBalances(): Promise<ExchangeBalance[]>;
    abstract getBalance(asset: string): Promise<ExchangeBalance>;
    abstract getTicker(symbol: string): Promise<ExchangeTicker>;
    abstract getTickers(symbols?: string[]): Promise<ExchangeTicker[]>;
    abstract getOrderBook(symbol: string, limit?: number): Promise<ExchangeOrderBook>;
    abstract getKlines(symbol: string, interval: string, limit?: number, startTime?: number, endTime?: number): Promise<ExchangeKline[]>;
    abstract createOrder(params: CreateOrderParams): Promise<ExchangeOrder>;
    abstract cancelOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
    abstract cancelAllOrders(symbol?: string): Promise<ExchangeOrder[]>;
    abstract getOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
    abstract getOpenOrders(symbol?: string): Promise<ExchangeOrder[]>;
    abstract getOrderHistory(symbol?: string, limit?: number): Promise<ExchangeOrder[]>;
    abstract getTrades(symbol?: string, limit?: number): Promise<ExchangeTrade[]>;
    abstract getMyTrades(symbol: string, limit?: number): Promise<ExchangeTrade[]>;
    abstract getSymbols(): Promise<string[]>;
    abstract getSymbolInfo(symbol: string): Promise<any>;
    abstract formatSymbol(base: string, quote: string): string;
    abstract parseSymbol(symbol: string): {
        base: string;
        quote: string;
    };
    abstract getTradingFees(symbol?: string): Promise<{
        maker: number;
        taker: number;
    }>;
}
//# sourceMappingURL=BaseExchange.d.ts.map
{"version": 3, "file": "BaseExchange.js", "sourceRoot": "", "sources": ["../../../../src/server/exchanges/base/BaseExchange.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAiE;AACjE,oDAA4B;AAC5B,mCAAsC;AACtC,+CAA4C;AAE5C,2DAe6B;AAE7B,MAAsB,YAAa,SAAQ,qBAAY;IAQrD,YAAY,IAAiB;QAC3B,KAAK,EAAE,CAAC;QALA,iBAAY,GAAY,KAAK,CAAC;QAC9B,gBAAW,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC7C,oBAAe,GAAW,CAAC,CAAC;QAIpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAOD,SAAS;IACT,KAAK,CAAC,UAAU,CAAC,MAAsB;QACrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;YAC7B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK;YAChC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,YAAY,EAAE,iBAAiB;aAChC;SACF,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACtC,CAAC,MAAM,EAAE,EAAE;YACT,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CACjC,CAAC;QAEF,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACvC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EACtB,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,uBAAuB,CAAC,CAAC;IACnD,CAAC;IAED,SAAS;IACT,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACvB,eAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,uCAAmB,CAAC,yBAAyB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO;IACP,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1B,eAAM,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC;IACzD,CAAC;IAED,OAAO;IACP,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO;IACG,gBAAgB;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC,YAAY;QAE9D,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,WAAW,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,WAAW,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,0BAA0B;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC;gBAClC,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACpC,CAAC;IAED,WAAW;IACD,eAAe,CAAC,KAAU;QAClC,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YACrC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;YAE1F,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,GAAG;oBACN,MAAM,IAAI,uCAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpD,KAAK,GAAG;oBACN,MAAM,IAAI,kCAAc,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/C;oBACE,MAAM,IAAI,iCAAa,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,gCAAY,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,iCAAa,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,SAAS;IACC,KAAK,CAAC,wBAAwB,CACtC,MAAc,EACd,IAAY,EACZ,MAAY,EACZ,IAAU;QAEV,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAE3C,MAAM,MAAM,GAAuB;YACjC,MAAM,EAAE,MAAM,CAAC,WAAW,EAAS;YACnC,GAAG,EAAE,IAAI;YACT,OAAO;YACP,MAAM;YACN,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,sBAAsB,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,SAAS;IACC,KAAK,CAAC,iBAAiB,CAC/B,MAAc,EACd,IAAY,EACZ,MAAY;QAEZ,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,MAAM,CAAC,WAAW,EAAS;gBACnC,GAAG,EAAE,IAAI;gBACT,MAAM;aACP,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,WAAW;IACD,qBAAqB,CAC7B,OAAe,EACf,MAAc,EACd,YAAoB,QAAQ;QAE5B,OAAO,gBAAM;aACV,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC;aAC7B,MAAM,CAAC,OAAO,CAAC;aACf,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED,UAAU;IACA,gBAAgB,CAAC,MAA2B;QACpD,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;aACvB,IAAI,EAAE;aACN,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;aACvD,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAED,QAAQ;IACE,YAAY,CAAC,GAAW,EAAE,YAAoB,CAAC;QACvD,OAAO,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,QAAQ;IACE,cAAc,CAAC,SAAc;QACrC,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;QACvC,CAAC;QACD,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,iBAAiB;YACjB,OAAO,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;CAuBF;AA1OD,oCA0OC"}
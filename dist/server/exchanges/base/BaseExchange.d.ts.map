{"version": 3, "file": "BaseExchange.d.ts", "sourceRoot": "", "sources": ["../../../../src/server/exchanges/base/BaseExchange.ts"], "names": [], "mappings": "AAAA,OAAc,EAAE,aAAa,EAAsB,MAAM,OAAO,CAAC;AAEjE,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEtC,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EACL,SAAS,EACT,cAAc,EAKd,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,iBAAiB,EAClB,MAAM,qBAAqB,CAAC;AAE7B,8BAAsB,YAAa,SAAQ,YAAa,YAAW,SAAS;IAC1E,SAAgB,IAAI,EAAE,WAAW,CAAC;IAClC,SAAS,CAAC,MAAM,EAAE,cAAc,CAAC;IACjC,SAAS,CAAC,UAAU,EAAE,aAAa,CAAC;IACpC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAS;IACxC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAa;IACvD,SAAS,CAAC,eAAe,EAAE,MAAM,CAAK;gBAE1B,IAAI,EAAE,WAAW;IAK7B,IAAI,WAAW,IAAI,OAAO,CAEzB;IAGD,SAAS,CAAC,QAAQ,CAAC,UAAU,IAAI,MAAM;IACvC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG;IAC3F,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAGhE,UAAU,CAAC,MAAM,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC;IAkCjD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAkBxB,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAO3B,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAWxC,SAAS,CAAC,gBAAgB,IAAI,IAAI;IAiBlC,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI;cAqB3B,wBAAwB,CACtC,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,MAAM,EACZ,MAAM,CAAC,EAAE,GAAG,EACZ,IAAI,CAAC,EAAE,GAAG,GACT,OAAO,CAAC,GAAG,CAAC;cAsBC,iBAAiB,CAC/B,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,MAAM,EACZ,MAAM,CAAC,EAAE,GAAG,GACX,OAAO,CAAC,GAAG,CAAC;IAef,SAAS,CAAC,qBAAqB,CAC7B,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,MAAM,EACd,SAAS,GAAE,MAAiB,GAC3B,MAAM;IAQT,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM;IAQ/D,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,GAAE,MAAU,GAAG,MAAM;IAKlE,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,GAAG,GAAG,MAAM;IAYhD,QAAQ,CAAC,cAAc,IAAI,OAAO,CAAC,mBAAmB,CAAC;IACvD,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;IAClD,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;IAC5D,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAC3D,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IAClE,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC;IACjF,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IACpI,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC;IACvE,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAC7E,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IACnE,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAC1E,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IACjE,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IACnF,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAC7E,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAC9E,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IACxC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IACpD,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM;IAC1D,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE;IACrE,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;CACpF"}
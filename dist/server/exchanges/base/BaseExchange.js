"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseExchange = void 0;
const axios_1 = __importDefault(require("axios"));
const crypto_1 = __importDefault(require("crypto"));
const events_1 = require("events");
const logger_1 = require("../../utils/logger");
const ExchangeInterface_1 = require("./ExchangeInterface");
class BaseExchange extends events_1.EventEmitter {
    constructor(name) {
        super();
        this._isConnected = false;
        this.rateLimiter = new Map();
        this.lastRequestTime = 0;
        this.name = name;
    }
    get isConnected() {
        return this._isConnected;
    }
    // 初始化交易所
    async initialize(config) {
        this.config = config;
        this.httpClient = axios_1.default.create({
            baseURL: this.getBaseUrl(),
            timeout: config.timeout || 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'TradeAI-Bot/1.0'
            }
        });
        // 添加请求拦截器
        this.httpClient.interceptors.request.use((config) => {
            this.enforceRateLimit();
            return config;
        }, (error) => Promise.reject(error));
        // 添加响应拦截器
        this.httpClient.interceptors.response.use((response) => response, (error) => {
            this.handleHttpError(error);
            return Promise.reject(error);
        });
        logger_1.logger.info(`${this.name} exchange initialized`);
    }
    // 连接到交易所
    async connect() {
        try {
            const isValid = await this.testConnection();
            if (isValid) {
                this._isConnected = true;
                this.emit('connected');
                logger_1.logger.info(`Connected to ${this.name} exchange`);
            }
            else {
                throw new ExchangeInterface_1.AuthenticationError('Invalid API credentials', this.name);
            }
        }
        catch (error) {
            this._isConnected = false;
            this.emit('error', error);
            throw error;
        }
    }
    // 断开连接
    async disconnect() {
        this._isConnected = false;
        this.emit('disconnected');
        logger_1.logger.info(`Disconnected from ${this.name} exchange`);
    }
    // 测试连接
    async testConnection() {
        try {
            await this.getAccountInfo();
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Connection test failed for ${this.name}:`, error);
            return false;
        }
    }
    // 速率限制
    enforceRateLimit() {
        const now = Date.now();
        const minInterval = this.config.rateLimit || 100; // 默认100ms间隔
        if (now - this.lastRequestTime < minInterval) {
            const delay = minInterval - (now - this.lastRequestTime);
            // 简单的同步延迟，实际应用中可能需要更复杂的实现
            const start = Date.now();
            while (Date.now() - start < delay) {
                // 忙等待
            }
        }
        this.lastRequestTime = Date.now();
    }
    // 处理HTTP错误
    handleHttpError(error) {
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.msg || error.response.data?.message || error.message;
            switch (status) {
                case 401:
                    throw new ExchangeInterface_1.AuthenticationError(message, this.name);
                case 429:
                    throw new ExchangeInterface_1.RateLimitError(message, this.name);
                default:
                    throw new ExchangeInterface_1.ExchangeError(message, this.name, 'HTTP_ERROR', status);
            }
        }
        else if (error.request) {
            throw new ExchangeInterface_1.NetworkError('Network error', this.name);
        }
        else {
            throw new ExchangeInterface_1.ExchangeError(error.message, this.name);
        }
    }
    // 发送认证请求
    async makeAuthenticatedRequest(method, path, params, body) {
        const signature = this.signRequest(method, path, params, body);
        const headers = this.getHeaders(signature);
        const config = {
            method: method.toLowerCase(),
            url: path,
            headers,
            params,
            data: body
        };
        try {
            const response = await this.httpClient.request(config);
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`${this.name} API request failed:`, error);
            throw error;
        }
    }
    // 发送公开请求
    async makePublicRequest(method, path, params) {
        try {
            const response = await this.httpClient.request({
                method: method.toLowerCase(),
                url: path,
                params
            });
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`${this.name} public API request failed:`, error);
            throw error;
        }
    }
    // 生成HMAC签名
    generateHmacSignature(message, secret, algorithm = 'sha256') {
        return crypto_1.default
            .createHmac(algorithm, secret)
            .update(message)
            .digest('hex');
    }
    // 生成查询字符串
    buildQueryString(params) {
        return Object.keys(params)
            .sort()
            .map(key => `${key}=${encodeURIComponent(params[key])}`)
            .join('&');
    }
    // 格式化数字
    formatNumber(num, precision = 8) {
        return parseFloat(num.toFixed(precision));
    }
    // 解析时间戳
    parseTimestamp(timestamp) {
        if (typeof timestamp === 'string') {
            return new Date(timestamp).getTime();
        }
        if (typeof timestamp === 'number') {
            // 如果是秒级时间戳，转换为毫秒
            return timestamp < 1e12 ? timestamp * 1000 : timestamp;
        }
        return Date.now();
    }
}
exports.BaseExchange = BaseExchange;
//# sourceMappingURL=BaseExchange.js.map
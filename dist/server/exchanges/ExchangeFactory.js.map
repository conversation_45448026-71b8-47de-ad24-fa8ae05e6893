{"version": 3, "file": "ExchangeFactory.js", "sourceRoot": "", "sources": ["../../../src/server/exchanges/ExchangeFactory.ts"], "names": [], "mappings": ";;;AAAA,8CAAiD;AAEjD,+DAA4D;AAC5D,iBAAiB;AACjB,mDAAmD;AACnD,mDAAmD;AACnD,4DAA4D;AAC5D,yDAAyD;AACzD,sDAAsD;AACtD,+DAA+D;AAE/D,MAAa,eAAe;IAG1B;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,QAAqB,EAAE,MAAsB;QACvE,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAE3C,YAAY;QACZ,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;YAC1C,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACzB,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,QAAQ;QACR,IAAI,gBAA2B,CAAC;QAEhC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mBAAW,CAAC,OAAO;gBACtB,gBAAgB,GAAG,IAAI,iCAAe,EAAE,CAAC;gBACzC,MAAM;YAER,wBAAwB;YACxB,0CAA0C;YAC1C,WAAW;YAEX,wBAAwB;YACxB,0CAA0C;YAC1C,WAAW;YAEX,2BAA2B;YAC3B,6CAA6C;YAC7C,WAAW;YAEX,0BAA0B;YAC1B,4CAA4C;YAC5C,WAAW;YAEX,yBAAyB;YACzB,2CAA2C;YAC3C,WAAW;YAEX,4BAA4B;YAC5B,8CAA8C;YAC9C,WAAW;YAEX;gBACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,SAAS;QACT,MAAM,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAEjC,OAAO;QACP,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;QAE1C,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,WAAW,CAAC,QAAqB,EAAE,MAAc;QACtD,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,MAAM,EAAE,CAAC;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,QAAqB,EAAE,MAAc;QAC/D,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,MAAM,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEzC,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,kBAAkB;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa;QACxB,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAC5E,QAAQ,CAAC,UAAU,EAAE,CACtB,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,mBAAW,CAAC,OAAO;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,sBAAsB;YACtB,qBAAqB;YACrB,oBAAoB;YACpB,sBAAsB;SACvB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,WAAW,CAAC,QAAqB;QACtC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAqB;QAC3C,MAAM,OAAO,GAAiD;YAC5D,CAAC,mBAAW,CAAC,OAAO,CAAC,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,CAAC,mBAAW,CAAC,GAAG,CAAC,EAAE;gBACjB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,CAAC,mBAAW,CAAC,GAAG,CAAC,EAAE;gBACjB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,CAAC,mBAAW,CAAC,MAAM,CAAC,EAAE;gBACpB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,CAAC,mBAAW,CAAC,KAAK,CAAC,EAAE;gBACnB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,CAAC,mBAAW,CAAC,IAAI,CAAC,EAAE;gBAClB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,CAAC,mBAAW,CAAC,OAAO,CAAC,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;SACF,CAAC;QAEF,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,cAAc,CAAC,QAAqB,EAAE,MAAsB;QAIjE,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,OAAO;QACP,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,UAAU;QACV,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mBAAW,CAAC,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBAChD,CAAC;gBACD,MAAM;YAER,KAAK,mBAAW,CAAC,GAAG;gBAClB,WAAW;gBACX,MAAM;YAER,gBAAgB;QAClB,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,QAAqB,EAAE,MAAsB;QAKvE,IAAI,CAAC;YACH,OAAO;YACP,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,wBAAwB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;iBAChE,CAAC;YACJ,CAAC;YAED,aAAa;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACjE,MAAM,WAAW,GAAG,MAAM,YAAY,CAAC,cAAc,EAAE,CAAC;YAExD,YAAY;YACZ,MAAM,YAAY,CAAC,UAAU,EAAE,CAAC;YAEhC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,uBAAuB;gBAChC,WAAW;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC;QACJ,CAAC;IACH,CAAC;;AAzQH,0CA0QC;AAzQgB,yBAAS,GAA2B,IAAI,GAAG,EAAE,CAAC"}
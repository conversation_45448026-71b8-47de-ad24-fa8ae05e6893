"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExchangeFactory = void 0;
const types_1 = require("../../shared/types");
const BinanceExchange_1 = require("./binance/BinanceExchange");
// 其他交易所的导入将在后续添加
// import { OKXExchange } from './okx/OKXExchange';
// import { HTXExchange } from './htx/HTXExchange';
// import { BitgetExchange } from './bitget/BitgetExchange';
// import { BybitExchange } from './bybit/BybitExchange';
// import { MEXCExchange } from './mexc/MEXCExchange';
// import { BitmartExchange } from './bitmart/BitmartExchange';
class ExchangeFactory {
    /**
     * 创建交易所实例
     * @param exchange 交易所类型
     * @param config 配置信息
     * @returns 交易所实例
     */
    static async createExchange(exchange, config) {
        const key = `${exchange}_${config.apiKey}`;
        // 检查是否已存在实例
        if (this.instances.has(key)) {
            const instance = this.instances.get(key);
            if (instance.isConnected) {
                return instance;
            }
        }
        // 创建新实例
        let exchangeInstance;
        switch (exchange) {
            case types_1.CEXExchange.BINANCE:
                exchangeInstance = new BinanceExchange_1.BinanceExchange();
                break;
            // case CEXExchange.OKX:
            //   exchangeInstance = new OKXExchange();
            //   break;
            // case CEXExchange.HTX:
            //   exchangeInstance = new HTXExchange();
            //   break;
            // case CEXExchange.BITGET:
            //   exchangeInstance = new BitgetExchange();
            //   break;
            // case CEXExchange.BYBIT:
            //   exchangeInstance = new BybitExchange();
            //   break;
            // case CEXExchange.MEXC:
            //   exchangeInstance = new MEXCExchange();
            //   break;
            // case CEXExchange.BITMART:
            //   exchangeInstance = new BitmartExchange();
            //   break;
            default:
                throw new Error(`Unsupported exchange: ${exchange}`);
        }
        // 初始化并连接
        await exchangeInstance.initialize(config);
        await exchangeInstance.connect();
        // 缓存实例
        this.instances.set(key, exchangeInstance);
        return exchangeInstance;
    }
    /**
     * 获取已存在的交易所实例
     * @param exchange 交易所类型
     * @param apiKey API密钥
     * @returns 交易所实例或null
     */
    static getExchange(exchange, apiKey) {
        const key = `${exchange}_${apiKey}`;
        return this.instances.get(key) || null;
    }
    /**
     * 移除交易所实例
     * @param exchange 交易所类型
     * @param apiKey API密钥
     */
    static async removeExchange(exchange, apiKey) {
        const key = `${exchange}_${apiKey}`;
        const instance = this.instances.get(key);
        if (instance) {
            await instance.disconnect();
            this.instances.delete(key);
        }
    }
    /**
     * 获取所有活跃的交易所实例
     * @returns 活跃的交易所实例数组
     */
    static getActiveExchanges() {
        return Array.from(this.instances.values()).filter(exchange => exchange.isConnected);
    }
    /**
     * 断开所有交易所连接
     */
    static async disconnectAll() {
        const disconnectPromises = Array.from(this.instances.values()).map(exchange => exchange.disconnect());
        await Promise.all(disconnectPromises);
        this.instances.clear();
    }
    /**
     * 获取支持的交易所列表
     * @returns 支持的交易所数组
     */
    static getSupportedExchanges() {
        return [
            types_1.CEXExchange.BINANCE,
            // CEXExchange.OKX,
            // CEXExchange.HTX,
            // CEXExchange.BITGET,
            // CEXExchange.BYBIT,
            // CEXExchange.MEXC,
            // CEXExchange.BITMART
        ];
    }
    /**
     * 检查交易所是否支持
     * @param exchange 交易所类型
     * @returns 是否支持
     */
    static isSupported(exchange) {
        return this.getSupportedExchanges().includes(exchange);
    }
    /**
     * 获取交易所的默认配置
     * @param exchange 交易所类型
     * @returns 默认配置
     */
    static getDefaultConfig(exchange) {
        const configs = {
            [types_1.CEXExchange.BINANCE]: {
                rateLimit: 100,
                timeout: 30000,
                sandbox: false
            },
            [types_1.CEXExchange.OKX]: {
                rateLimit: 100,
                timeout: 30000,
                sandbox: false
            },
            [types_1.CEXExchange.HTX]: {
                rateLimit: 200,
                timeout: 30000,
                sandbox: false
            },
            [types_1.CEXExchange.BITGET]: {
                rateLimit: 100,
                timeout: 30000,
                sandbox: false
            },
            [types_1.CEXExchange.BYBIT]: {
                rateLimit: 120,
                timeout: 30000,
                sandbox: false
            },
            [types_1.CEXExchange.MEXC]: {
                rateLimit: 100,
                timeout: 30000,
                sandbox: false
            },
            [types_1.CEXExchange.BITMART]: {
                rateLimit: 200,
                timeout: 30000,
                sandbox: false
            }
        };
        return configs[exchange] || {};
    }
    /**
     * 验证交易所配置
     * @param exchange 交易所类型
     * @param config 配置信息
     * @returns 验证结果
     */
    static validateConfig(exchange, config) {
        const errors = [];
        // 基础验证
        if (!config.apiKey) {
            errors.push('API Key is required');
        }
        if (!config.secretKey) {
            errors.push('Secret Key is required');
        }
        // 特定交易所验证
        switch (exchange) {
            case types_1.CEXExchange.OKX:
                if (!config.passphrase) {
                    errors.push('Passphrase is required for OKX');
                }
                break;
            case types_1.CEXExchange.HTX:
                // HTX 特定验证
                break;
            // 其他交易所的特定验证...
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    /**
     * 测试交易所连接
     * @param exchange 交易所类型
     * @param config 配置信息
     * @returns 测试结果
     */
    static async testConnection(exchange, config) {
        try {
            // 验证配置
            const validation = this.validateConfig(exchange, config);
            if (!validation.valid) {
                return {
                    success: false,
                    message: `Configuration error: ${validation.errors.join(', ')}`
                };
            }
            // 创建临时实例进行测试
            const tempInstance = await this.createExchange(exchange, config);
            const accountInfo = await tempInstance.getAccountInfo();
            // 测试完成后断开连接
            await tempInstance.disconnect();
            return {
                success: true,
                message: 'Connection successful',
                accountInfo
            };
        }
        catch (error) {
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
}
exports.ExchangeFactory = ExchangeFactory;
ExchangeFactory.instances = new Map();
//# sourceMappingURL=ExchangeFactory.js.map
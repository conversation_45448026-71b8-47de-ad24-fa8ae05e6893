import { BaseExchange } from '../base/BaseExchange';
import { ExchangeAccountInfo, ExchangeBalance, ExchangeOrder, ExchangeTicker, ExchangeOrderBook, ExchangeKline, ExchangeTrade, CreateOrderParams } from '../base/ExchangeInterface';
export declare class BinanceExchange extends BaseExchange {
    constructor();
    protected getBaseUrl(): string;
    protected signRequest(method: string, path: string, params?: any, body?: any): string;
    protected getHeaders(signature?: string): Record<string, string>;
    getAccountInfo(): Promise<ExchangeAccountInfo>;
    getBalances(): Promise<ExchangeBalance[]>;
    getBalance(asset: string): Promise<ExchangeBalance>;
    getTicker(symbol: string): Promise<ExchangeTicker>;
    getTickers(symbols?: string[]): Promise<ExchangeTicker[]>;
    getOrderBook(symbol: string, limit?: number): Promise<ExchangeOrderBook>;
    getKlines(symbol: string, interval: string, limit?: number, startTime?: number, endTime?: number): Promise<ExchangeKline[]>;
    createOrder(params: CreateOrderParams): Promise<ExchangeOrder>;
    cancelOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
    cancelAllOrders(symbol?: string): Promise<ExchangeOrder[]>;
    getOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
    getOpenOrders(symbol?: string): Promise<ExchangeOrder[]>;
    getOrderHistory(symbol?: string, limit?: number): Promise<ExchangeOrder[]>;
    getTrades(symbol?: string, limit?: number): Promise<ExchangeTrade[]>;
    getMyTrades(symbol: string, limit?: number): Promise<ExchangeTrade[]>;
    getSymbols(): Promise<string[]>;
    getSymbolInfo(symbol: string): Promise<any>;
    formatSymbol(base: string, quote: string): string;
    parseSymbol(symbol: string): {
        base: string;
        quote: string;
    };
    getTradingFees(symbol?: string): Promise<{
        maker: number;
        taker: number;
    }>;
    private mapOrderType;
    private parseOrderStatus;
    private parseOrder;
    private parseTrade;
    private parseMyTrade;
    private handleOrderError;
}
//# sourceMappingURL=BinanceExchange.d.ts.map
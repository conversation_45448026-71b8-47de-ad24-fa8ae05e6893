{"version": 3, "file": "BinanceExchange.d.ts", "sourceRoot": "", "sources": ["../../../../src/server/exchanges/binance/BinanceExchange.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAEpD,OAAO,EACL,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,iBAAiB,EAIlB,MAAM,2BAA2B,CAAC;AAEnC,qBAAa,eAAgB,SAAQ,YAAY;;IAK/C,SAAS,CAAC,UAAU,IAAI,MAAM;IAM9B,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,MAAM;IAOrF,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAa1D,cAAc,IAAI,OAAO,CAAC,mBAAmB,CAAC;IAiB9C,WAAW,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;IAMzC,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;IAiBnD,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAmBlD,UAAU,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IAsBzD,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,GAAE,MAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAY7E,SAAS,CACb,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,KAAK,GAAE,MAAY,EACnB,SAAS,CAAC,EAAE,MAAM,EAClB,OAAO,CAAC,EAAE,MAAM,GACf,OAAO,CAAC,aAAa,EAAE,CAAC;IAkBrB,WAAW,CAAC,MAAM,EAAE,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC;IAmC9D,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAWpE,eAAe,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAc1D,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAWjE,aAAa,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IASxD,eAAe,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,GAAE,MAAY,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAe/E,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,GAAE,MAAY,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAWzE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,GAAE,MAAY,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAW1E,UAAU,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAQ/B,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAMjD,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM;IAKjD,WAAW,CAAC,MAAM,EAAE,MAAM,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE;IAkBtD,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;IAYhF,OAAO,CAAC,YAAY;IAWpB,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,UAAU;IAoBlB,OAAO,CAAC,UAAU;IAelB,OAAO,CAAC,YAAY;IAepB,OAAO,CAAC,gBAAgB;CASzB"}
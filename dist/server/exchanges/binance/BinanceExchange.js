"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BinanceExchange = void 0;
const BaseExchange_1 = require("../base/BaseExchange");
const types_1 = require("../../../shared/types");
const ExchangeInterface_1 = require("../base/ExchangeInterface");
class BinanceExchange extends BaseExchange_1.BaseExchange {
    constructor() {
        super(types_1.CEXExchange.BINANCE);
    }
    getBaseUrl() {
        return this.config.sandbox
            ? 'https://testnet.binance.vision'
            : 'https://api.binance.com';
    }
    signRequest(method, path, params, body) {
        const timestamp = Date.now();
        const queryString = params ? this.buildQueryString({ ...params, timestamp }) : `timestamp=${timestamp}`;
        return this.generateHmacSignature(queryString, this.config.secretKey);
    }
    getHeaders(signature) {
        const headers = {
            'X-MBX-APIKEY': this.config.apiKey
        };
        if (signature) {
            headers['signature'] = signature;
        }
        return headers;
    }
    // 获取账户信息
    async getAccountInfo() {
        const data = await this.makeAuthenticatedRequest('GET', '/api/v3/account');
        return {
            balances: data.balances.map((balance) => ({
                asset: balance.asset,
                free: parseFloat(balance.free),
                locked: parseFloat(balance.locked),
                total: parseFloat(balance.free) + parseFloat(balance.locked)
            })),
            permissions: data.permissions || [],
            tradingEnabled: data.canTrade || false,
            withdrawEnabled: data.canWithdraw || false
        };
    }
    // 获取所有余额
    async getBalances() {
        const accountInfo = await this.getAccountInfo();
        return accountInfo.balances.filter(balance => balance.total > 0);
    }
    // 获取单个资产余额
    async getBalance(asset) {
        const balances = await this.getBalances();
        const balance = balances.find(b => b.asset === asset.toUpperCase());
        if (!balance) {
            return {
                asset: asset.toUpperCase(),
                free: 0,
                locked: 0,
                total: 0
            };
        }
        return balance;
    }
    // 获取单个交易对行情
    async getTicker(symbol) {
        const data = await this.makePublicRequest('GET', '/api/v3/ticker/24hr', { symbol });
        return {
            symbol: data.symbol,
            bid: parseFloat(data.bidPrice),
            ask: parseFloat(data.askPrice),
            last: parseFloat(data.lastPrice),
            high: parseFloat(data.highPrice),
            low: parseFloat(data.lowPrice),
            volume: parseFloat(data.volume),
            quoteVolume: parseFloat(data.quoteVolume),
            change: parseFloat(data.priceChange),
            percentage: parseFloat(data.priceChangePercent),
            timestamp: parseInt(data.closeTime)
        };
    }
    // 获取多个交易对行情
    async getTickers(symbols) {
        const params = symbols ? { symbols: JSON.stringify(symbols) } : {};
        const data = await this.makePublicRequest('GET', '/api/v3/ticker/24hr', params);
        const tickers = Array.isArray(data) ? data : [data];
        return tickers.map((ticker) => ({
            symbol: ticker.symbol,
            bid: parseFloat(ticker.bidPrice),
            ask: parseFloat(ticker.askPrice),
            last: parseFloat(ticker.lastPrice),
            high: parseFloat(ticker.highPrice),
            low: parseFloat(ticker.lowPrice),
            volume: parseFloat(ticker.volume),
            quoteVolume: parseFloat(ticker.quoteVolume),
            change: parseFloat(ticker.priceChange),
            percentage: parseFloat(ticker.priceChangePercent),
            timestamp: parseInt(ticker.closeTime)
        }));
    }
    // 获取订单簿
    async getOrderBook(symbol, limit = 100) {
        const data = await this.makePublicRequest('GET', '/api/v3/depth', { symbol, limit });
        return {
            symbol,
            bids: data.bids.map((bid) => [parseFloat(bid[0]), parseFloat(bid[1])]),
            asks: data.asks.map((ask) => [parseFloat(ask[0]), parseFloat(ask[1])]),
            timestamp: data.lastUpdateId
        };
    }
    // 获取K线数据
    async getKlines(symbol, interval, limit = 500, startTime, endTime) {
        const params = { symbol, interval, limit };
        if (startTime)
            params.startTime = startTime;
        if (endTime)
            params.endTime = endTime;
        const data = await this.makePublicRequest('GET', '/api/v3/klines', params);
        return data.map((kline) => ({
            timestamp: kline[0],
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4]),
            volume: parseFloat(kline[5])
        }));
    }
    // 创建订单
    async createOrder(params) {
        const orderParams = {
            symbol: params.symbol,
            side: params.side.toUpperCase(),
            type: this.mapOrderType(params.type),
            quantity: params.amount,
            timestamp: Date.now()
        };
        if (params.price) {
            orderParams.price = params.price;
        }
        if (params.stopPrice) {
            orderParams.stopPrice = params.stopPrice;
        }
        if (params.timeInForce) {
            orderParams.timeInForce = params.timeInForce;
        }
        if (params.clientOrderId) {
            orderParams.newClientOrderId = params.clientOrderId;
        }
        try {
            const data = await this.makeAuthenticatedRequest('POST', '/api/v3/order', orderParams);
            return this.parseOrder(data);
        }
        catch (error) {
            this.handleOrderError(error);
            throw error;
        }
    }
    // 取消订单
    async cancelOrder(orderId, symbol) {
        const data = await this.makeAuthenticatedRequest('DELETE', '/api/v3/order', {
            symbol,
            orderId,
            timestamp: Date.now()
        });
        return this.parseOrder(data);
    }
    // 取消所有订单
    async cancelAllOrders(symbol) {
        if (!symbol) {
            throw new ExchangeInterface_1.ExchangeError('Symbol is required for Binance cancelAllOrders', this.name);
        }
        const data = await this.makeAuthenticatedRequest('DELETE', '/api/v3/openOrders', {
            symbol,
            timestamp: Date.now()
        });
        return data.map((order) => this.parseOrder(order));
    }
    // 获取订单信息
    async getOrder(orderId, symbol) {
        const data = await this.makeAuthenticatedRequest('GET', '/api/v3/order', {
            symbol,
            orderId,
            timestamp: Date.now()
        });
        return this.parseOrder(data);
    }
    // 获取开放订单
    async getOpenOrders(symbol) {
        const params = { timestamp: Date.now() };
        if (symbol)
            params.symbol = symbol;
        const data = await this.makeAuthenticatedRequest('GET', '/api/v3/openOrders', params);
        return data.map((order) => this.parseOrder(order));
    }
    // 获取订单历史
    async getOrderHistory(symbol, limit = 500) {
        if (!symbol) {
            throw new ExchangeInterface_1.ExchangeError('Symbol is required for Binance order history', this.name);
        }
        const data = await this.makeAuthenticatedRequest('GET', '/api/v3/allOrders', {
            symbol,
            limit,
            timestamp: Date.now()
        });
        return data.map((order) => this.parseOrder(order));
    }
    // 获取交易记录
    async getTrades(symbol, limit = 500) {
        if (!symbol) {
            const data = await this.makePublicRequest('GET', '/api/v3/trades', { limit });
            return data.map((trade) => this.parseTrade(trade));
        }
        const data = await this.makePublicRequest('GET', '/api/v3/trades', { symbol, limit });
        return data.map((trade) => this.parseTrade(trade));
    }
    // 获取我的交易记录
    async getMyTrades(symbol, limit = 500) {
        const data = await this.makeAuthenticatedRequest('GET', '/api/v3/myTrades', {
            symbol,
            limit,
            timestamp: Date.now()
        });
        return data.map((trade) => this.parseMyTrade(trade));
    }
    // 获取交易对列表
    async getSymbols() {
        const data = await this.makePublicRequest('GET', '/api/v3/exchangeInfo');
        return data.symbols
            .filter((symbol) => symbol.status === 'TRADING')
            .map((symbol) => symbol.symbol);
    }
    // 获取交易对信息
    async getSymbolInfo(symbol) {
        const data = await this.makePublicRequest('GET', '/api/v3/exchangeInfo');
        return data.symbols.find((s) => s.symbol === symbol);
    }
    // 格式化交易对
    formatSymbol(base, quote) {
        return `${base.toUpperCase()}${quote.toUpperCase()}`;
    }
    // 解析交易对
    parseSymbol(symbol) {
        // 币安的交易对格式比较复杂，需要通过交易所信息来解析
        // 这里提供一个简化版本
        const commonQuotes = ['USDT', 'BUSD', 'BTC', 'ETH', 'BNB'];
        for (const quote of commonQuotes) {
            if (symbol.endsWith(quote)) {
                return {
                    base: symbol.slice(0, -quote.length),
                    quote
                };
            }
        }
        throw new ExchangeInterface_1.ExchangeError(`Cannot parse symbol: ${symbol}`, this.name);
    }
    // 获取交易费率
    async getTradingFees(symbol) {
        const data = await this.makeAuthenticatedRequest('GET', '/api/v3/account', {
            timestamp: Date.now()
        });
        return {
            maker: data.makerCommission / 10000, // 币安返回的是万分比
            taker: data.takerCommission / 10000
        };
    }
    // 辅助方法
    mapOrderType(type) {
        const typeMap = {
            [types_1.OrderType.MARKET]: 'MARKET',
            [types_1.OrderType.LIMIT]: 'LIMIT',
            [types_1.OrderType.STOP_LOSS]: 'STOP_LOSS',
            [types_1.OrderType.TAKE_PROFIT]: 'TAKE_PROFIT'
        };
        return typeMap[type] || 'LIMIT';
    }
    parseOrderStatus(status) {
        const statusMap = {
            'NEW': types_1.OrderStatus.PENDING,
            'PARTIALLY_FILLED': types_1.OrderStatus.PARTIALLY_FILLED,
            'FILLED': types_1.OrderStatus.FILLED,
            'CANCELED': types_1.OrderStatus.CANCELLED,
            'REJECTED': types_1.OrderStatus.FAILED,
            'EXPIRED': types_1.OrderStatus.CANCELLED
        };
        return statusMap[status] || types_1.OrderStatus.PENDING;
    }
    parseOrder(data) {
        return {
            id: data.orderId.toString(),
            clientOrderId: data.clientOrderId,
            symbol: data.symbol,
            type: data.type.toLowerCase(),
            side: data.side.toLowerCase(),
            amount: parseFloat(data.origQty),
            price: parseFloat(data.price) || undefined,
            status: this.parseOrderStatus(data.status),
            filled: parseFloat(data.executedQty),
            remaining: parseFloat(data.origQty) - parseFloat(data.executedQty),
            cost: parseFloat(data.cummulativeQuoteQty),
            fee: 0, // 需要从交易记录中获取
            feeAsset: '',
            timestamp: data.time,
            lastTradeTimestamp: data.updateTime
        };
    }
    parseTrade(data) {
        return {
            id: data.id.toString(),
            orderId: '',
            symbol: data.symbol || '',
            side: data.isBuyerMaker ? 'sell' : 'buy',
            amount: parseFloat(data.qty),
            price: parseFloat(data.price),
            cost: parseFloat(data.quoteQty),
            fee: 0,
            feeAsset: '',
            timestamp: data.time
        };
    }
    parseMyTrade(data) {
        return {
            id: data.id.toString(),
            orderId: data.orderId.toString(),
            symbol: data.symbol,
            side: data.isBuyer ? 'buy' : 'sell',
            amount: parseFloat(data.qty),
            price: parseFloat(data.price),
            cost: parseFloat(data.quoteQty),
            fee: parseFloat(data.commission),
            feeAsset: data.commissionAsset,
            timestamp: data.time
        };
    }
    handleOrderError(error) {
        const message = error.response?.data?.msg || error.message;
        if (message.includes('insufficient balance')) {
            throw new ExchangeInterface_1.InsufficientBalanceError(message, this.name);
        }
        throw new ExchangeInterface_1.OrderError(message, this.name);
    }
}
exports.BinanceExchange = BinanceExchange;
//# sourceMappingURL=BinanceExchange.js.map
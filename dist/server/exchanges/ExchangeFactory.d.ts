import { CEXExchange } from '../../shared/types';
import { IExchange, ExchangeConfig } from './base/ExchangeInterface';
export declare class ExchangeFactory {
    private static instances;
    /**
     * 创建交易所实例
     * @param exchange 交易所类型
     * @param config 配置信息
     * @returns 交易所实例
     */
    static createExchange(exchange: CEXExchange, config: ExchangeConfig): Promise<IExchange>;
    /**
     * 获取已存在的交易所实例
     * @param exchange 交易所类型
     * @param apiKey API密钥
     * @returns 交易所实例或null
     */
    static getExchange(exchange: CEXExchange, apiKey: string): IExchange | null;
    /**
     * 移除交易所实例
     * @param exchange 交易所类型
     * @param apiKey API密钥
     */
    static removeExchange(exchange: CEXExchange, apiKey: string): Promise<void>;
    /**
     * 获取所有活跃的交易所实例
     * @returns 活跃的交易所实例数组
     */
    static getActiveExchanges(): IExchange[];
    /**
     * 断开所有交易所连接
     */
    static disconnectAll(): Promise<void>;
    /**
     * 获取支持的交易所列表
     * @returns 支持的交易所数组
     */
    static getSupportedExchanges(): CEXExchange[];
    /**
     * 检查交易所是否支持
     * @param exchange 交易所类型
     * @returns 是否支持
     */
    static isSupported(exchange: CEXExchange): boolean;
    /**
     * 获取交易所的默认配置
     * @param exchange 交易所类型
     * @returns 默认配置
     */
    static getDefaultConfig(exchange: CEXExchange): Partial<ExchangeConfig>;
    /**
     * 验证交易所配置
     * @param exchange 交易所类型
     * @param config 配置信息
     * @returns 验证结果
     */
    static validateConfig(exchange: CEXExchange, config: ExchangeConfig): {
        valid: boolean;
        errors: string[];
    };
    /**
     * 测试交易所连接
     * @param exchange 交易所类型
     * @param config 配置信息
     * @returns 测试结果
     */
    static testConnection(exchange: CEXExchange, config: ExchangeConfig): Promise<{
        success: boolean;
        message: string;
        accountInfo?: any;
    }>;
}
//# sourceMappingURL=ExchangeFactory.d.ts.map
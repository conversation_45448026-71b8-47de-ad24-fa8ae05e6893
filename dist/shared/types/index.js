"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WSMessageType = exports.UserRole = exports.OrderStatus = exports.OrderType = exports.StrategyStatus = exports.StrategyType = exports.DEXProtocol = exports.BlockchainNetwork = exports.CEXExchange = exports.ExchangeType = void 0;
// 交易所类型
var ExchangeType;
(function (ExchangeType) {
    ExchangeType["CEX"] = "cex";
    ExchangeType["DEX"] = "dex";
})(ExchangeType || (exports.ExchangeType = ExchangeType = {}));
// CEX 交易所
var CEXExchange;
(function (CEXExchange) {
    CEXExchange["BINANCE"] = "binance";
    CEXExchange["OKX"] = "okx";
    CEXExchange["HTX"] = "htx";
    CEXExchange["BITGET"] = "bitget";
    CEXExchange["BYBIT"] = "bybit";
    CEXExchange["MEXC"] = "mexc";
    CEXExchange["BITMART"] = "bitmart";
})(CEXExchange || (exports.CEXExchange = CEXExchange = {}));
// 区块链网络
var BlockchainNetwork;
(function (BlockchainNetwork) {
    BlockchainNetwork["ETH"] = "ethereum";
    BlockchainNetwork["BSC"] = "bsc";
    BlockchainNetwork["BASE"] = "base";
    BlockchainNetwork["SOLANA"] = "solana";
})(BlockchainNetwork || (exports.BlockchainNetwork = BlockchainNetwork = {}));
// DEX 协议
var DEXProtocol;
(function (DEXProtocol) {
    DEXProtocol["UNISWAP_V2"] = "uniswap_v2";
    DEXProtocol["UNISWAP_V3"] = "uniswap_v3";
    DEXProtocol["PANCAKESWAP"] = "pancakeswap";
    DEXProtocol["RAYDIUM"] = "raydium";
    DEXProtocol["JUPITER"] = "jupiter";
})(DEXProtocol || (exports.DEXProtocol = DEXProtocol = {}));
// 策略类型
var StrategyType;
(function (StrategyType) {
    // CEX 策略
    StrategyType["GRID"] = "grid";
    StrategyType["POSITION_BUILDING"] = "position_building";
    StrategyType["LIQUIDITY_MAINTENANCE"] = "liquidity_maintenance";
    StrategyType["VOLUME_WASHING"] = "volume_washing";
    StrategyType["TREND_FOLLOWING"] = "trend_following";
    StrategyType["HEDGING"] = "hedging";
    StrategyType["AI_STRATEGY"] = "ai_strategy";
    // DEX 策略
    StrategyType["DEX_VOLUME_WASHING"] = "dex_volume_washing";
    StrategyType["DEX_TREND_FOLLOWING"] = "dex_trend_following";
    StrategyType["ACCOUNT_TRACKING"] = "account_tracking";
    StrategyType["DEX_POSITION_BUILDING"] = "dex_position_building";
    StrategyType["DEX_AI_STRATEGY"] = "dex_ai_strategy";
})(StrategyType || (exports.StrategyType = StrategyType = {}));
// 策略状态
var StrategyStatus;
(function (StrategyStatus) {
    StrategyStatus["INACTIVE"] = "inactive";
    StrategyStatus["ACTIVE"] = "active";
    StrategyStatus["PAUSED"] = "paused";
    StrategyStatus["ERROR"] = "error";
    StrategyStatus["COMPLETED"] = "completed";
})(StrategyStatus || (exports.StrategyStatus = StrategyStatus = {}));
// 订单类型
var OrderType;
(function (OrderType) {
    OrderType["MARKET"] = "market";
    OrderType["LIMIT"] = "limit";
    OrderType["STOP_LOSS"] = "stop_loss";
    OrderType["TAKE_PROFIT"] = "take_profit";
})(OrderType || (exports.OrderType = OrderType = {}));
// 订单状态
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "pending";
    OrderStatus["FILLED"] = "filled";
    OrderStatus["PARTIALLY_FILLED"] = "partially_filled";
    OrderStatus["CANCELLED"] = "cancelled";
    OrderStatus["FAILED"] = "failed";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
// 用户角色
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "user";
    UserRole["PREMIUM"] = "premium";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
// WebSocket 消息类型
var WSMessageType;
(function (WSMessageType) {
    WSMessageType["STRATEGY_UPDATE"] = "strategy_update";
    WSMessageType["ORDER_UPDATE"] = "order_update";
    WSMessageType["BALANCE_UPDATE"] = "balance_update";
    WSMessageType["ERROR"] = "error";
    WSMessageType["HEARTBEAT"] = "heartbeat";
})(WSMessageType || (exports.WSMessageType = WSMessageType = {}));
//# sourceMappingURL=index.js.map
export declare enum ExchangeType {
    CEX = "cex",
    DEX = "dex"
}
export declare enum CEXExchange {
    BINANCE = "binance",
    OKX = "okx",
    HTX = "htx",
    BITGET = "bitget",
    BYBIT = "bybit",
    MEXC = "mexc",
    BITMART = "bitmart"
}
export declare enum BlockchainNetwork {
    ETH = "ethereum",
    BSC = "bsc",
    BASE = "base",
    SOLANA = "solana"
}
export declare enum DEXProtocol {
    UNISWAP_V2 = "uniswap_v2",
    UNISWAP_V3 = "uniswap_v3",
    PANCAKESWAP = "pancakeswap",
    RAYDIUM = "raydium",
    JUPITER = "jupiter"
}
export declare enum StrategyType {
    GRID = "grid",
    POSITION_BUILDING = "position_building",
    LIQUIDITY_MAINTENANCE = "liquidity_maintenance",
    VOLUME_WASHING = "volume_washing",
    TREND_FOLLOWING = "trend_following",
    HEDGING = "hedging",
    AI_STRATEGY = "ai_strategy",
    DEX_VOLUME_WASHING = "dex_volume_washing",
    DEX_TREND_FOLLOWING = "dex_trend_following",
    ACCOUNT_TRACKING = "account_tracking",
    DEX_POSITION_BUILDING = "dex_position_building",
    DEX_AI_STRATEGY = "dex_ai_strategy"
}
export declare enum StrategyStatus {
    INACTIVE = "inactive",
    ACTIVE = "active",
    PAUSED = "paused",
    ERROR = "error",
    COMPLETED = "completed"
}
export declare enum OrderType {
    MARKET = "market",
    LIMIT = "limit",
    STOP_LOSS = "stop_loss",
    TAKE_PROFIT = "take_profit"
}
export declare enum OrderStatus {
    PENDING = "pending",
    FILLED = "filled",
    PARTIALLY_FILLED = "partially_filled",
    CANCELLED = "cancelled",
    FAILED = "failed"
}
export declare enum UserRole {
    USER = "user",
    PREMIUM = "premium",
    ADMIN = "admin"
}
export interface BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface User extends BaseEntity {
    email: string;
    passwordHash: string;
    role: UserRole;
    isActive: boolean;
    subscriptionExpiry?: Date;
    totalRewards: number;
}
export interface APIKey extends BaseEntity {
    userId: string;
    exchange: CEXExchange;
    apiKey: string;
    secretKey: string;
    passphrase?: string;
    isActive: boolean;
    encryptedData: string;
}
export interface WalletAddress extends BaseEntity {
    userId: string;
    network: BlockchainNetwork;
    address: string;
    privateKey: string;
    isActive: boolean;
    balance?: number;
}
export interface StrategyConfig extends BaseEntity {
    userId: string;
    name: string;
    type: StrategyType;
    exchangeType: ExchangeType;
    exchange?: CEXExchange;
    network?: BlockchainNetwork;
    dexProtocol?: DEXProtocol;
    status: StrategyStatus;
    config: Record<string, any>;
    isActive: boolean;
}
export interface Order extends BaseEntity {
    userId: string;
    strategyId: string;
    exchange: CEXExchange | DEXProtocol;
    symbol: string;
    type: OrderType;
    side: 'buy' | 'sell';
    amount: number;
    price?: number;
    status: OrderStatus;
    executedAmount: number;
    executedPrice?: number;
    fees: number;
    txHash?: string;
}
export interface Trade extends BaseEntity {
    userId: string;
    strategyId: string;
    orderId: string;
    exchange: CEXExchange | DEXProtocol;
    symbol: string;
    side: 'buy' | 'sell';
    amount: number;
    price: number;
    fees: number;
    profit?: number;
    txHash?: string;
}
export interface Reward extends BaseEntity {
    userId: string;
    amount: number;
    reason: string;
    tokenSymbol: string;
    txHash?: string;
    isDistributed: boolean;
}
export interface GridStrategyConfig {
    symbol: string;
    baseAmount: number;
    gridCount: number;
    gridSpacing: number;
    upperPrice: number;
    lowerPrice: number;
    isInfinite: boolean;
    stopLoss?: number;
    takeProfit?: number;
}
export interface LiquidityMaintenanceConfig {
    symbol: string;
    spreadPercentage: number;
    amount: number;
    maxSlippage: number;
    rebalanceThreshold: number;
}
export interface TrendFollowingConfig {
    symbol: string;
    timeframe: string;
    trendDirection: 'up' | 'down' | 'sideways';
    duration: number;
    randomness: number;
    maxAmount: number;
}
export interface MultiAddressConfig {
    addresses: string[];
    csvFilePath?: string;
    password: string;
    balanceThreshold: number;
}
export interface AccountTrackingConfig {
    targetAddresses: string[];
    followRatio: number;
    maxAmount: number;
    delay: number;
}
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
export declare enum WSMessageType {
    STRATEGY_UPDATE = "strategy_update",
    ORDER_UPDATE = "order_update",
    BALANCE_UPDATE = "balance_update",
    ERROR = "error",
    HEARTBEAT = "heartbeat"
}
export interface WSMessage {
    type: WSMessageType;
    data: any;
    timestamp: number;
}
//# sourceMappingURL=index.d.ts.map
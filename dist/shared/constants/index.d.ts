export declare const EXCHANGE_CONFIG: {
    binance: {
        name: string;
        baseUrl: string;
        testnetUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
    okx: {
        name: string;
        baseUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
    htx: {
        name: string;
        baseUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
    bitget: {
        name: string;
        baseUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
    bybit: {
        name: string;
        baseUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
    mexc: {
        name: string;
        baseUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
    bitmart: {
        name: string;
        baseUrl: string;
        rateLimits: {
            orders: number;
            requests: number;
        };
    };
};
export declare const BLOCKCHAIN_CONFIG: {
    ethereum: {
        name: string;
        chainId: number;
        nativeCurrency: string;
        blockTime: number;
        confirmations: number;
        gasLimit: number;
        dexes: string[];
    };
    bsc: {
        name: string;
        chainId: number;
        nativeCurrency: string;
        blockTime: number;
        confirmations: number;
        gasLimit: number;
        dexes: string[];
    };
    base: {
        name: string;
        chainId: number;
        nativeCurrency: string;
        blockTime: number;
        confirmations: number;
        gasLimit: number;
        dexes: string[];
    };
    solana: {
        name: string;
        chainId: number;
        nativeCurrency: string;
        blockTime: number;
        confirmations: number;
        dexes: string[];
    };
};
export declare const DEX_CONFIG: {
    uniswap_v2: {
        name: string;
        factory: string;
        router: string;
        fee: number;
    };
    uniswap_v3: {
        name: string;
        factory: string;
        router: string;
        fees: number[];
    };
    pancakeswap: {
        name: string;
        factory: string;
        router: string;
        fee: number;
    };
    raydium: {
        name: string;
        programId: string;
        fee: number;
    };
    jupiter: {
        name: string;
        programId: string;
        fee: number;
    };
};
export declare const STRATEGY_DEFAULTS: {
    grid: {
        gridCount: number;
        gridSpacing: number;
        maxInvestment: number;
        stopLoss: number;
        takeProfit: number;
    };
    positionBuilding: {
        orderSize: number;
        interval: number;
        maxSlippage: number;
        maxAmount: number;
    };
    liquidityMaintenance: {
        spreadPercentage: number;
        rebalanceThreshold: number;
        maxSlippage: number;
    };
    volumeWashing: {
        minAmount: number;
        maxAmount: number;
        interval: number;
        randomness: number;
    };
    trendFollowing: {
        timeframe: string;
        duration: number;
        randomness: number;
        maxAmount: number;
    };
    hedging: {
        hedgeRatio: number;
        rebalanceThreshold: number;
        maxSlippage: number;
    };
};
export declare const FEE_CONFIG: {
    cex: {
        maker: number;
        taker: number;
    };
    dex: {
        ethereum: number;
        bsc: number;
        base: number;
        solana: number;
    };
};
export declare const RISK_CONFIG: {
    maxPositionSize: number;
    maxDailyLoss: number;
    maxDrawdown: number;
    stopLossPercentage: number;
    takeProfitPercentage: number;
    maxOpenOrders: number;
    maxStrategiesPerUser: number;
};
export declare const REWARD_CONFIG: {
    tokenSymbol: string;
    dailyRewardPool: number;
    minimumStakeTime: number;
    rewardCalculationInterval: number;
    bonusMultipliers: {
        premium: number;
        highVolume: number;
        longTerm: number;
    };
};
export declare const SUBSCRIPTION_CONFIG: {
    premium: {
        price: number;
        features: string[];
        limits: {
            strategies: number;
            apiCalls: number;
            walletAddresses: number;
        };
    };
    free: {
        price: number;
        features: string[];
        limits: {
            strategies: number;
            apiCalls: number;
            walletAddresses: number;
        };
    };
};
export declare const SYSTEM_CONFIG: {
    maxConcurrentStrategies: number;
    orderExecutionTimeout: number;
    websocketHeartbeatInterval: number;
    databaseConnectionPool: number;
    redisConnectionPool: number;
    logRetentionDays: number;
    backupInterval: number;
    maintenanceWindow: {
        start: string;
        end: string;
        timezone: string;
    };
};
export declare const ERROR_CODES: {
    UNAUTHORIZED: string;
    INVALID_TOKEN: string;
    EXPIRED_TOKEN: string;
    USER_NOT_FOUND: string;
    USER_ALREADY_EXISTS: string;
    INVALID_CREDENTIALS: string;
    STRATEGY_NOT_FOUND: string;
    STRATEGY_LIMIT_EXCEEDED: string;
    INVALID_STRATEGY_CONFIG: string;
    INSUFFICIENT_BALANCE: string;
    ORDER_FAILED: string;
    EXCHANGE_ERROR: string;
    NETWORK_ERROR: string;
    INTERNAL_ERROR: string;
    DATABASE_ERROR: string;
    RATE_LIMIT_EXCEEDED: string;
};
export declare const SUCCESS_MESSAGES: {
    USER_CREATED: string;
    LOGIN_SUCCESS: string;
    STRATEGY_CREATED: string;
    STRATEGY_UPDATED: string;
    STRATEGY_DELETED: string;
    ORDER_PLACED: string;
    SETTINGS_UPDATED: string;
};
//# sourceMappingURL=index.d.ts.map
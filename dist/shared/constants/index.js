"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUCCESS_MESSAGES = exports.ERROR_CODES = exports.SYSTEM_CONFIG = exports.SUBSCRIPTION_CONFIG = exports.REWARD_CONFIG = exports.RISK_CONFIG = exports.FEE_CONFIG = exports.STRATEGY_DEFAULTS = exports.DEX_CONFIG = exports.BLOCKCHAIN_CONFIG = exports.EXCHANGE_CONFIG = void 0;
// 交易所配置
exports.EXCHANGE_CONFIG = {
    binance: {
        name: 'Binance',
        baseUrl: 'https://api.binance.com',
        testnetUrl: 'https://testnet.binance.vision',
        rateLimits: {
            orders: 10, // per second
            requests: 1200 // per minute
        }
    },
    okx: {
        name: 'OKX',
        baseUrl: 'https://www.okx.com',
        rateLimits: {
            orders: 60, // per 2 seconds
            requests: 20 // per 2 seconds
        }
    },
    htx: {
        name: 'HTX (<PERSON><PERSON><PERSON>)',
        baseUrl: 'https://api.huobi.pro',
        rateLimits: {
            orders: 100, // per 10 seconds
            requests: 10 // per second
        }
    },
    bitget: {
        name: 'Bitget',
        baseUrl: 'https://api.bitget.com',
        rateLimits: {
            orders: 20, // per second
            requests: 10 // per second
        }
    },
    bybit: {
        name: 'Bybit',
        baseUrl: 'https://api.bybit.com',
        rateLimits: {
            orders: 10, // per second
            requests: 120 // per minute
        }
    },
    mexc: {
        name: 'MEXC',
        baseUrl: 'https://api.mexc.com',
        rateLimits: {
            orders: 10, // per second
            requests: 20 // per second
        }
    },
    bitmart: {
        name: 'BitMart',
        baseUrl: 'https://api-cloud.bitmart.com',
        rateLimits: {
            orders: 5, // per second
            requests: 600 // per minute
        }
    }
};
// 区块链网络配置
exports.BLOCKCHAIN_CONFIG = {
    ethereum: {
        name: 'Ethereum',
        chainId: 1,
        nativeCurrency: 'ETH',
        blockTime: 12, // seconds
        confirmations: 12,
        gasLimit: 21000,
        dexes: ['uniswap_v2', 'uniswap_v3']
    },
    bsc: {
        name: 'Binance Smart Chain',
        chainId: 56,
        nativeCurrency: 'BNB',
        blockTime: 3,
        confirmations: 3,
        gasLimit: 21000,
        dexes: ['pancakeswap', 'uniswap_v2']
    },
    base: {
        name: 'Base',
        chainId: 8453,
        nativeCurrency: 'ETH',
        blockTime: 2,
        confirmations: 3,
        gasLimit: 21000,
        dexes: ['uniswap_v3']
    },
    solana: {
        name: 'Solana',
        chainId: 101,
        nativeCurrency: 'SOL',
        blockTime: 0.4,
        confirmations: 1,
        dexes: ['raydium', 'jupiter']
    }
};
// DEX 协议配置
exports.DEX_CONFIG = {
    uniswap_v2: {
        name: 'Uniswap V2',
        factory: '******************************************',
        router: '******************************************',
        fee: 0.003 // 0.3%
    },
    uniswap_v3: {
        name: 'Uniswap V3',
        factory: '******************************************',
        router: '******************************************',
        fees: [0.0005, 0.003, 0.01] // 0.05%, 0.3%, 1%
    },
    pancakeswap: {
        name: 'PancakeSwap',
        factory: '******************************************',
        router: '******************************************',
        fee: 0.0025 // 0.25%
    },
    raydium: {
        name: 'Raydium',
        programId: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
        fee: 0.0025 // 0.25%
    },
    jupiter: {
        name: 'Jupiter',
        programId: 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4',
        fee: 0.001 // 0.1%
    }
};
// 策略默认配置
exports.STRATEGY_DEFAULTS = {
    grid: {
        gridCount: 10,
        gridSpacing: 1, // 1%
        maxInvestment: 1000,
        stopLoss: 10, // 10%
        takeProfit: 20 // 20%
    },
    positionBuilding: {
        orderSize: 10, // USDT
        interval: 60, // seconds
        maxSlippage: 0.5, // 0.5%
        maxAmount: 1000
    },
    liquidityMaintenance: {
        spreadPercentage: 2, // 2%
        rebalanceThreshold: 5, // 5%
        maxSlippage: 1 // 1%
    },
    volumeWashing: {
        minAmount: 10,
        maxAmount: 100,
        interval: 30, // seconds
        randomness: 20 // 20%
    },
    trendFollowing: {
        timeframe: '1h',
        duration: 24, // hours
        randomness: 15, // 15%
        maxAmount: 500
    },
    hedging: {
        hedgeRatio: 0.8, // 80%
        rebalanceThreshold: 5, // 5%
        maxSlippage: 1 // 1%
    }
};
// 费率配置
exports.FEE_CONFIG = {
    cex: {
        maker: 0.001, // 0.1%
        taker: 0.001 // 0.1%
    },
    dex: {
        ethereum: 0.003, // 0.3%
        bsc: 0.0025, // 0.25%
        base: 0.003, // 0.3%
        solana: 0.0025 // 0.25%
    }
};
// 风险管理配置
exports.RISK_CONFIG = {
    maxPositionSize: 0.1, // 10% of portfolio
    maxDailyLoss: 0.05, // 5% of portfolio
    maxDrawdown: 0.2, // 20% of portfolio
    stopLossPercentage: 0.1, // 10%
    takeProfitPercentage: 0.2, // 20%
    maxOpenOrders: 50,
    maxStrategiesPerUser: 10
};
// 奖励系统配置
exports.REWARD_CONFIG = {
    tokenSymbol: 'TAI', // TradeAI Token
    dailyRewardPool: 1000,
    minimumStakeTime: 24, // hours
    rewardCalculationInterval: 3600, // seconds (1 hour)
    bonusMultipliers: {
        premium: 1.5,
        highVolume: 1.2,
        longTerm: 1.3
    }
};
// 订阅配置
exports.SUBSCRIPTION_CONFIG = {
    premium: {
        price: 500, // USD per month
        features: [
            'unlimited_strategies',
            'advanced_ai',
            'priority_support',
            'custom_indicators',
            'api_access'
        ],
        limits: {
            strategies: -1, // unlimited
            apiCalls: -1, // unlimited
            walletAddresses: -1 // unlimited
        }
    },
    free: {
        price: 0,
        features: [
            'basic_strategies',
            'limited_ai',
            'community_support'
        ],
        limits: {
            strategies: 3,
            apiCalls: 1000, // per day
            walletAddresses: 5
        }
    }
};
// 系统配置
exports.SYSTEM_CONFIG = {
    maxConcurrentStrategies: 100,
    orderExecutionTimeout: 30000, // 30 seconds
    websocketHeartbeatInterval: 30000, // 30 seconds
    databaseConnectionPool: 10,
    redisConnectionPool: 5,
    logRetentionDays: 30,
    backupInterval: 86400, // 24 hours
    maintenanceWindow: {
        start: '02:00',
        end: '04:00',
        timezone: 'UTC'
    }
};
// 错误代码
exports.ERROR_CODES = {
    // 认证错误
    UNAUTHORIZED: 'UNAUTHORIZED',
    INVALID_TOKEN: 'INVALID_TOKEN',
    EXPIRED_TOKEN: 'EXPIRED_TOKEN',
    // 用户错误
    USER_NOT_FOUND: 'USER_NOT_FOUND',
    USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    // 策略错误
    STRATEGY_NOT_FOUND: 'STRATEGY_NOT_FOUND',
    STRATEGY_LIMIT_EXCEEDED: 'STRATEGY_LIMIT_EXCEEDED',
    INVALID_STRATEGY_CONFIG: 'INVALID_STRATEGY_CONFIG',
    // 交易错误
    INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
    ORDER_FAILED: 'ORDER_FAILED',
    EXCHANGE_ERROR: 'EXCHANGE_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    // 系统错误
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    DATABASE_ERROR: 'DATABASE_ERROR',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED'
};
// 成功消息
exports.SUCCESS_MESSAGES = {
    USER_CREATED: 'User created successfully',
    LOGIN_SUCCESS: 'Login successful',
    STRATEGY_CREATED: 'Strategy created successfully',
    STRATEGY_UPDATED: 'Strategy updated successfully',
    STRATEGY_DELETED: 'Strategy deleted successfully',
    ORDER_PLACED: 'Order placed successfully',
    SETTINGS_UPDATED: 'Settings updated successfully'
};
//# sourceMappingURL=index.js.map
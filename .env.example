# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tradeai_bot
DB_USER=postgres
DB_PASSWORD=your_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Security
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_32_character_encryption_key

# CEX API Keys (Example - Users will configure their own)
BINANCE_API_KEY=
BINANCE_SECRET_KEY=
OKX_API_KEY=
OKX_SECRET_KEY=
OKX_PASSPHRASE=

# Blockchain RPC URLs
ETH_RPC_URL=https://mainnet.infura.io/v3/your_project_id
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BASE_RPC_URL=https://mainnet.base.org
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# AI Configuration
OPENAI_API_KEY=your_openai_api_key

# Subscription System
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Features
ENABLE_PREMIUM_FEATURES=false
PREMIUM_SUBSCRIPTION_PRICE=500

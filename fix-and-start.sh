#!/bin/bash

# TradeAI Bot 修复并启动脚本
# 解决所有前端依赖问题

set -e

echo "🔧 TradeAI Bot 修复并启动"
echo "=========================="

# 清理端口
echo "🧹 清理端口占用..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 启动后端
echo "🔧 启动后端服务器..."
node test-server.js &
BACKEND_PID=$!
echo "✅ 后端已启动 (PID: $BACKEND_PID)"

# 等待后端启动
sleep 3

# 验证后端
if curl -s http://localhost:3001/health > /dev/null; then
    echo "✅ 后端健康检查通过"
else
    echo "❌ 后端启动失败"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# 修复前端
echo "🎨 修复前端依赖问题..."
cd client

# 备份原始文件
echo "📝 备份原始文件..."
cp src/App.tsx src/App-original.tsx 2>/dev/null || true

# 使用简化版本
echo "🔄 使用简化版本..."
cp src/App-simple.tsx src/App.tsx

# 检查组件是否存在
if [ ! -f "src/components/SimpleDashboard.tsx" ]; then
    echo "❌ SimpleDashboard.tsx 不存在"
    cd ..
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 前端组件准备完成"

# 启动前端开发服务器
echo "🚀 启动前端服务器..."
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 前端已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 8

# 检查前端状态
echo "🔍 检查前端状态..."
if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ 前端启动成功"
else
    echo "⚠️  前端可能还在启动中..."
fi

# 显示访问信息
echo ""
echo "🎉 启动完成！"
echo "==============="
echo "📱 前端: http://localhost:5173"
echo "🔗 后端: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo "🧪 API测试: http://localhost:3001/api/test"
echo ""
echo "📋 快速测试:"
echo "  curl http://localhost:3001/health"
echo "  curl http://localhost:3001/api"
echo ""
echo "💡 提示:"
echo "  - 前端可能需要几秒钟完全加载"
echo "  - 如果页面空白，请等待或刷新页面"
echo "  - 在前端界面点击'检查后端'测试连接"
echo ""
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    # 恢复原始文件
    echo "🔄 恢复原始文件..."
    cd client
    cp src/App-original.tsx src/App.tsx 2>/dev/null || true
    cd ..
    
    echo "✅ 所有服务已停止，文件已恢复"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待进程
wait $BACKEND_PID $FRONTEND_PID

# TradeAI Bot 参数配置指南

## 📋 配置页面概览

TradeAI Bot 提供了完整的参数配置系统，包含以下四个主要配置模块：

### 🏢 交易所配置
配置各大交易所的API连接信息

### 🤖 策略配置  
设置和管理各种交易策略参数

### ⚠️ 风险管理
配置风险控制和资金管理参数

### 🔔 通知配置
设置邮件和Webhook通知

## 🏢 交易所配置详解

### 支持的交易所
- **Binance** - 全球最大的加密货币交易所
- **OKX** - 专业的数字资产交易平台
- **HTX** - 全球化的数字资产交易平台

### 配置参数
- **API Key**: 交易所提供的API密钥
- **Secret Key**: 交易所提供的密钥
- **Passphrase**: 部分交易所需要的额外密码（如OKX）
- **沙盒模式**: 开启后使用测试环境，不会进行真实交易
- **启用状态**: 控制是否使用该交易所进行交易

### 安全建议
1. 🔒 **API权限**: 只开启必要的交易权限，不要开启提现权限
2. 🌐 **IP白名单**: 在交易所设置IP白名单限制
3. 🧪 **先测试**: 建议先在沙盒环境测试
4. 🔑 **密钥安全**: 定期更换API密钥

## 🤖 策略配置详解

### 网格策略 (Grid Strategy)
适合震荡行情的自动化交易策略

**参数说明:**
- **网格大小**: 网格的数量，建议10-50个
- **价格范围**: 网格覆盖的价格范围百分比

**适用场景:**
- 横盘震荡市场
- 流动性好的主流币种
- 波动率适中的市场

### 趋势跟踪策略 (Trend Following)
跟随市场趋势的交易策略

**参数说明:**
- **周期**: 技术指标的计算周期
- **阈值**: 触发交易的信号强度

**适用场景:**
- 明显的上升或下降趋势
- 突破性行情
- 中长期投资

### AI策略 (AI Strategy)
基于机器学习的智能交易策略

**参数说明:**
- **模型类型**: LSTM、Transformer、Random Forest
- **置信度**: AI预测的最低置信度要求

**适用场景:**
- 复杂的市场环境
- 需要多因子分析
- 高频交易场景

## ⚠️ 风险管理配置

### 核心参数
- **最大仓位大小**: 单笔交易的最大金额
- **止损百分比**: 自动止损的亏损比例
- **止盈百分比**: 自动止盈的盈利比例
- **每日最大亏损**: 每日亏损上限，达到后停止交易
- **最大开仓数量**: 同时持有的最大仓位数

### 风险等级建议

#### 🟢 保守型 (低风险)
- 最大仓位: 总资金的5-10%
- 止损: 2-3%
- 止盈: 5-8%
- 每日亏损限制: 总资金的1%

#### 🟡 平衡型 (中等风险)
- 最大仓位: 总资金的10-20%
- 止损: 3-5%
- 止盈: 8-15%
- 每日亏损限制: 总资金的2%

#### 🔴 激进型 (高风险)
- 最大仓位: 总资金的20-30%
- 止损: 5-8%
- 止盈: 15-25%
- 每日亏损限制: 总资金的5%

## 🔔 通知配置

### 邮件通知
- 配置邮箱地址接收交易通知
- 支持交易执行、盈亏、异常等通知

### Webhook通知
- 配置Webhook URL接收实时通知
- 支持集成到Slack、Discord、钉钉等平台
- JSON格式的结构化数据推送

### 通知事件类型
- 📈 **交易执行**: 买入/卖出订单执行
- 💰 **盈亏提醒**: 达到止盈/止损条件
- ⚠️ **风险警告**: 接近风险限制
- 🔧 **系统状态**: 连接异常、策略启停

## 💾 配置管理

### 保存配置
- 点击"保存配置"按钮保存所有设置
- 配置会实时生效，无需重启系统
- 支持配置版本管理和回滚

### 导入导出
- 支持配置文件的导入和导出
- 便于在不同环境间迁移配置
- JSON格式，便于版本控制

### 配置验证
- 自动验证配置参数的有效性
- 提供配置建议和风险提示
- 支持配置模板快速设置

## 🧪 测试功能

### 连接测试
- 测试交易所API连接状态
- 验证API权限和配置正确性
- 显示连接延迟和状态信息

### 策略回测
- 基于历史数据测试策略效果
- 评估策略的盈利能力和风险
- 优化策略参数设置

### 模拟交易
- 在真实市场环境下进行模拟交易
- 验证策略在实际市场中的表现
- 无风险测试新的配置组合

## 📊 监控和分析

### 实时监控
- 实时查看策略运行状态
- 监控仓位、盈亏、风险指标
- 异常情况自动告警

### 性能分析
- 策略收益率统计
- 风险指标分析
- 交易成功率统计

### 日志记录
- 详细的操作日志记录
- 交易执行历史追踪
- 配置变更历史记录

## 🔧 最佳实践

### 1. 渐进式配置
- 从小资金开始测试
- 逐步增加仓位和复杂度
- 充分验证后再大规模使用

### 2. 多样化策略
- 不要把所有资金投入单一策略
- 组合使用不同类型的策略
- 根据市场环境调整策略权重

### 3. 定期优化
- 定期回顾和分析策略表现
- 根据市场变化调整参数
- 及时停止表现不佳的策略

### 4. 风险控制
- 严格遵守风险管理规则
- 设置合理的止损和止盈
- 保持充足的资金储备

## 🆘 常见问题

### Q: API连接失败怎么办？
A: 检查API密钥是否正确，确认IP白名单设置，验证网络连接状态。

### Q: 策略不执行交易怎么办？
A: 确认策略已启用，检查风险参数设置，验证市场条件是否满足策略要求。

### Q: 如何备份配置？
A: 使用导出功能定期备份配置文件，建议保存在安全的位置。

### Q: 可以同时运行多个策略吗？
A: 可以，但要注意总体风险控制，避免策略间的冲突。

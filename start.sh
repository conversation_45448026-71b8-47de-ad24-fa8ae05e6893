#!/bin/bash

# TradeAI Bot 统一启动脚本
# 清理、优化、简化版本

set -e

echo "🚀 TradeAI Bot 启动"
echo "=================="

# 清理端口
echo "🧹 清理端口..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 检查依赖
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi

if [ ! -d "client/node_modules" ]; then
    echo "安装前端依赖..."
    cd client && npm install && cd ..
fi

# 创建必要的前端组件
echo "🔧 准备前端组件..."
./fix-main-app.sh 2>/dev/null || echo "前端组件已存在"

# 使用简化的服务器和前端
echo "🔄 使用优化版本..."

# 备份并使用简化服务器
if [ -f "src/server/index.ts" ]; then
    cp src/server/index.ts src/server/index-backup.ts 2>/dev/null || true
fi
cp src/server/index-minimal.ts src/server/index.ts

# 备份并使用简化前端
cd client/src
if [ -f "App.tsx" ]; then
    cp App.tsx App-backup.tsx 2>/dev/null || true
fi
cp App-simple.tsx App.tsx
cd ../..

# 启动后端
echo "🔧 启动后端..."
npx ts-node src/server/index.ts &
BACKEND_PID=$!

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 5

# 验证后端
for i in {1..10}; do
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ 后端启动成功"
        break
    else
        echo "⏳ 等待中... ($i/10)"
        sleep 2
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 后端启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
done

# 启动前端
echo "🎨 启动前端..."
cd client
npm run dev &
FRONTEND_PID=$!
cd ..

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 8

# 显示信息
echo ""
echo "🎉 启动完成！"
echo "============="
echo ""
echo "📱 前端: http://localhost:5173"
echo "🔗 后端: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo ""
echo "💡 特性:"
echo "  ✅ 解决了 CSP 问题"
echo "  ✅ 最小化依赖"
echo "  ✅ 清理了冗余代码"
echo "  ✅ 统一的启动流程"
echo ""
echo "🛑 按 Ctrl+C 停止服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 停止服务..."
    
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    # 恢复文件
    echo "🔄 恢复原始文件..."
    if [ -f "src/server/index-backup.ts" ]; then
        cp src/server/index-backup.ts src/server/index.ts
    fi
    
    if [ -f "client/src/App-backup.tsx" ]; then
        cp client/src/App-backup.tsx client/src/App.tsx
    fi
    
    echo "✅ 清理完成"
    exit 0
}

trap cleanup SIGINT SIGTERM
wait $BACKEND_PID $FRONTEND_PID

# Test Environment Variables
NODE_ENV=test
PORT=3001

# Test Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tradeai_bot_test
DB_USER=postgres
DB_PASSWORD=test_password

# Test Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Test Security
JWT_SECRET=test-jwt-secret-key-for-testing-only-do-not-use-in-production
ENCRYPTION_KEY=test-encryption-key-32-characters

# Test Features
ENABLE_PREMIUM_FEATURES=true
PREMIUM_SUBSCRIPTION_PRICE=500

# Test Logging
LOG_LEVEL=error

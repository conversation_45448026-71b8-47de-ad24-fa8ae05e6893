# 🚀 TradeAI Bot 快速启动指南

## 一键启动 (推荐)

### macOS/Linux 用户

```bash
# 1. 赋予执行权限
chmod +x scripts/start-local.sh

# 2. 运行启动脚本
./scripts/start-local.sh
```

### Windows 用户

```cmd
# 双击运行或在命令提示符中执行
scripts\start-local.bat
```

## 手动启动步骤

### 1. 环境检查

确保已安装：
- Node.js 18+ 
- npm
- Docker (可选，用于数据库)

```bash
# 检查版本
node --version  # 应该 >= 18.0.0
npm --version
docker --version  # 可选
```

### 2. 项目设置

```bash
# 安装依赖
npm install
cd client && npm install && cd ..

# 创建环境变量文件
cp .env.example .env
```

### 3. 启动数据库 (Docker方式)

```bash
# 启动 PostgreSQL 和 Redis
docker-compose up -d postgres redis

# 运行数据库迁移
npm run migrate
```

### 4. 启动应用

```bash
# 开发模式启动 (前后端同时启动)
npm run dev
```

## 访问地址

启动成功后，访问以下地址：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:3001  
- **健康检查**: http://localhost:3001/health
- **API文档**: http://localhost:3001/api-docs

## 默认测试账户

可以注册新账户或使用以下测试数据：

```
邮箱: <EMAIL>
用户名: testuser  
密码: password123
```

## 常见问题

### 端口被占用

```bash
# 查看端口占用
lsof -i :3000
lsof -i :3001

# 杀死进程
kill -9 <PID>
```

### 数据库连接失败

```bash
# 重启数据库
docker-compose restart postgres redis

# 查看日志
docker-compose logs postgres
```

### 依赖安装失败

```bash
# 清理重装
rm -rf node_modules package-lock.json
npm install

# 前端依赖
cd client
rm -rf node_modules package-lock.json  
npm install
cd ..
```

## 停止服务

```bash
# 停止开发服务器
Ctrl + C

# 停止数据库
docker-compose down
```

## 下一步

1. 浏览前端界面熟悉功能
2. 查看 `docs/` 目录下的详细文档
3. 测试API接口
4. 配置交易所API密钥 (可选)

## 需要帮助？

如果遇到问题，请：

1. 检查控制台错误信息
2. 查看 `logs/` 目录下的日志文件
3. 参考 `docs/TROUBLESHOOTING.md`
4. 或联系技术支持

---

🎉 **祝您使用愉快！**

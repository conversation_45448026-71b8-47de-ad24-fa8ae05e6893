# 多阶段构建
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 复制 package.json 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 开发阶段
FROM base AS development
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]

# 构建阶段
FROM base AS build
RUN npm ci
COPY . .
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tradeai -u 1001

# 设置工作目录
WORKDIR /app

# 复制构建产物和依赖
COPY --from=build --chown=tradeai:nodejs /app/dist ./dist
COPY --from=build --chown=tradeai:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=tradeai:nodejs /app/package*.json ./

# 创建日志目录
RUN mkdir -p logs && chown tradeai:nodejs logs

# 切换到非 root 用户
USER tradeai

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["node", "dist/index.js"]

# Multi-stage build for TradeA<PERSON> Bot

# Stage 1: Build the client
FROM node:18-alpine AS client-builder

WORKDIR /app/client

# Copy client package files
COPY client/package*.json ./
RUN npm ci

# Copy client source code
COPY client/ ./

# Build the client
RUN npm run build

# Stage 2: Build the server
FROM node:18-alpine AS server-builder

WORKDIR /app

# Copy server package files
COPY package*.json ./
RUN npm ci

# Copy server source code
COPY src/ ./src/
COPY tsconfig.json ./

# Build the server
RUN npm run build

# Stage 3: Production image
FROM node:18-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tradeai -u 1001

# Copy package files
COPY package*.json ./

# Install production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built server from server-builder stage
COPY --from=server-builder /app/dist ./dist

# Copy built client from client-builder stage
COPY --from=client-builder /app/client/dist ./client/dist

# Copy other necessary files
COPY .env.example .env
COPY database/ ./database/

# Create necessary directories
RUN mkdir -p logs uploads temp models

# Change ownership to nodejs user
RUN chown -R tradeai:nodejs /app

# Switch to non-root user
USER tradeai

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/server.js"]

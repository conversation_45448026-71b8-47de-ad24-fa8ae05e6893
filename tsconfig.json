{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@server/*": ["src/server/*"], "@shared/*": ["src/shared/*"], "@cli/*": ["src/cli/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "client", "**/*.test.ts", "**/*.spec.ts"]}
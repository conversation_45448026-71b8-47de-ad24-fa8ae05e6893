// 简化的测试服务器 - 用于快速验证启动
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// 简单的日志函数
const log = (msg) => console.log(`[${new Date().toISOString()}] ${msg}`);

// 中间件
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API 基础路由
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'TradeAI Bot API - Test Server',
    version: '1.0.0-test',
    endpoints: [
      'GET /health',
      'GET /api',
      'GET /api/test'
    ]
  });
});

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'Test endpoint working!',
    timestamp: new Date().toISOString()
  });
});

// 404 处理
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.path
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// 启动服务器
app.listen(PORT, () => {
  log(`🚀 Test server running on port ${PORT}`);
  log(`🔗 Health check: http://localhost:${PORT}/health`);
  log(`🔗 API test: http://localhost:${PORT}/api/test`);
  log('✅ Server started successfully!');
});

// 优雅关闭
process.on('SIGINT', () => {
  log('Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Shutting down gracefully...');
  process.exit(0);
});

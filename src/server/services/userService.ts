import { db } from '../models/database';
import { EncryptionUtils } from '../utils/encryption';
import { logger, logUtils } from '../utils/logger';
import { User, UserRole, APIResponse } from '../../shared/types';

export class UserService {
  // 获取用户信息
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const result = await db.query(
        'SELECT id, email, role, is_active, subscription_expiry, total_rewards, created_at, updated_at FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      return {
        id: user.id,
        email: user.email,
        passwordHash: '', // 不返回密码哈希
        role: user.role,
        isActive: user.is_active,
        subscriptionExpiry: user.subscription_expiry,
        totalRewards: parseFloat(user.total_rewards),
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      throw new Error('Failed to get user');
    }
  }

  // 获取用户列表（管理员功能）
  static async getUsers(page: number = 1, limit: number = 20, search?: string): Promise<{
    users: User[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;
      let whereClause = '';
      let queryParams: any[] = [limit, offset];

      if (search) {
        whereClause = 'WHERE email ILIKE $3';
        queryParams.push(`%${search}%`);
      }

      // 获取用户列表
      const usersResult = await db.query(
        `SELECT id, email, role, is_active, subscription_expiry, total_rewards, created_at, updated_at 
         FROM users ${whereClause} 
         ORDER BY created_at DESC 
         LIMIT $1 OFFSET $2`,
        queryParams
      );

      // 获取总数
      const countResult = await db.query(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        search ? [`%${search}%`] : []
      );

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      const users = usersResult.rows.map(user => ({
        id: user.id,
        email: user.email,
        passwordHash: '',
        role: user.role,
        isActive: user.is_active,
        subscriptionExpiry: user.subscription_expiry,
        totalRewards: parseFloat(user.total_rewards),
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }));

      return {
        users,
        total,
        page,
        totalPages
      };
    } catch (error) {
      logger.error('Error getting users:', error);
      throw new Error('Failed to get users');
    }
  }

  // 更新用户信息
  static async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    try {
      const allowedFields = ['email', 'role', 'isActive'];
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      for (const [key, value] of Object.entries(updates)) {
        if (allowedFields.includes(key) && value !== undefined) {
          if (key === 'isActive') {
            updateFields.push(`is_active = $${paramIndex}`);
          } else {
            updateFields.push(`${key} = $${paramIndex}`);
          }
          updateValues.push(value);
          paramIndex++;
        }
      }

      if (updateFields.length === 0) {
        throw new Error('No valid fields to update');
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(userId);

      const result = await db.query(
        `UPDATE users SET ${updateFields.join(', ')} 
         WHERE id = $${paramIndex} 
         RETURNING id, email, role, is_active, subscription_expiry, total_rewards, created_at, updated_at`,
        updateValues
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = result.rows[0];
      logUtils.logUserAction(userId, 'profile_updated', updates);

      return {
        id: user.id,
        email: user.email,
        passwordHash: '',
        role: user.role,
        isActive: user.is_active,
        subscriptionExpiry: user.subscription_expiry,
        totalRewards: parseFloat(user.total_rewards),
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  // 更改密码
  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      // 获取当前密码哈希
      const result = await db.query(
        'SELECT password_hash FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      const currentPasswordHash = result.rows[0].password_hash;

      // 验证当前密码
      const isCurrentPasswordValid = await EncryptionUtils.verifyPassword(currentPassword, currentPasswordHash);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // 哈希新密码
      const newPasswordHash = await EncryptionUtils.hashPassword(newPassword);

      // 更新密码
      await db.query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [newPasswordHash, userId]
      );

      logUtils.logUserAction(userId, 'password_changed');
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }

  // 删除用户（软删除）
  static async deleteUser(userId: string): Promise<void> {
    try {
      const result = await db.query(
        'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [userId]
      );

      if (result.rowCount === 0) {
        throw new Error('User not found');
      }

      logUtils.logUserAction(userId, 'account_deleted');
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  // 升级用户为高级用户
  static async upgradeToPremium(userId: string, subscriptionExpiry: Date): Promise<void> {
    try {
      const result = await db.query(
        'UPDATE users SET role = $1, subscription_expiry = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
        [UserRole.PREMIUM, subscriptionExpiry, userId]
      );

      if (result.rowCount === 0) {
        throw new Error('User not found');
      }

      logUtils.logUserAction(userId, 'upgraded_to_premium', { subscriptionExpiry });
    } catch (error) {
      logger.error('Error upgrading user to premium:', error);
      throw error;
    }
  }

  // 降级高级用户
  static async downgradePremium(userId: string): Promise<void> {
    try {
      const result = await db.query(
        'UPDATE users SET role = $1, subscription_expiry = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [UserRole.USER, userId]
      );

      if (result.rowCount === 0) {
        throw new Error('User not found');
      }

      logUtils.logUserAction(userId, 'downgraded_from_premium');
    } catch (error) {
      logger.error('Error downgrading user from premium:', error);
      throw error;
    }
  }

  // 获取用户统计信息
  static async getUserStats(userId: string): Promise<{
    totalStrategies: number;
    activeStrategies: number;
    totalTrades: number;
    totalProfit: number;
    totalRewards: number;
  }> {
    try {
      // 获取策略统计
      const strategiesResult = await db.query(
        'SELECT COUNT(*) as total, COUNT(CASE WHEN status = \'active\' THEN 1 END) as active FROM strategy_configs WHERE user_id = $1',
        [userId]
      );

      // 获取交易统计
      const tradesResult = await db.query(
        'SELECT COUNT(*) as total, COALESCE(SUM(profit), 0) as total_profit FROM trades WHERE user_id = $1',
        [userId]
      );

      // 获取奖励统计
      const rewardsResult = await db.query(
        'SELECT COALESCE(SUM(amount), 0) as total_rewards FROM rewards WHERE user_id = $1 AND is_distributed = true',
        [userId]
      );

      return {
        totalStrategies: parseInt(strategiesResult.rows[0].total),
        activeStrategies: parseInt(strategiesResult.rows[0].active),
        totalTrades: parseInt(tradesResult.rows[0].total),
        totalProfit: parseFloat(tradesResult.rows[0].total_profit),
        totalRewards: parseFloat(rewardsResult.rows[0].total_rewards)
      };
    } catch (error) {
      logger.error('Error getting user stats:', error);
      throw new Error('Failed to get user statistics');
    }
  }

  // 检查用户权限
  static async checkUserPermissions(userId: string, feature: string): Promise<boolean> {
    try {
      const user = await this.getUserById(userId);
      if (!user || !user.isActive) {
        return false;
      }

      // 管理员拥有所有权限
      if (user.role === UserRole.ADMIN) {
        return true;
      }

      // 高级功能需要高级订阅
      const premiumFeatures = [
        'unlimited_strategies',
        'advanced_ai',
        'custom_indicators',
        'api_access',
        'priority_support'
      ];

      if (premiumFeatures.includes(feature)) {
        if (user.role !== UserRole.PREMIUM) {
          return false;
        }

        // 检查订阅是否过期
        if (user.subscriptionExpiry && new Date() > user.subscriptionExpiry) {
          return false;
        }
      }

      return true;
    } catch (error) {
      logger.error('Error checking user permissions:', error);
      return false;
    }
  }

  // 更新用户奖励
  static async updateUserRewards(userId: string, amount: number): Promise<void> {
    try {
      await db.query(
        'UPDATE users SET total_rewards = total_rewards + $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [amount, userId]
      );

      logUtils.logUserAction(userId, 'rewards_updated', { amount });
    } catch (error) {
      logger.error('Error updating user rewards:', error);
      throw new Error('Failed to update user rewards');
    }
  }
}

import { ethers } from 'ethers';
import { Keypair } from '@solana/web3.js';
import { db } from '../models/database';
import { EncryptionUtils } from '../utils/encryption';
import { logger, logUtils } from '../utils/logger';
import { BlockchainNetwork } from '../../shared/types';

export interface WalletAddress {
  id: string;
  userId: string;
  network: BlockchainNetwork;
  address: string;
  privateKey: string;
  isActive: boolean;
  balance: number;
  createdAt: Date;
  updatedAt: Date;
}

export class WalletService {
  /**
   * 生成新钱包
   * @param network 区块链网络
   * @returns 钱包信息
   */
  static generateWallet(network: BlockchainNetwork): {
    address: string;
    privateKey: string;
  } {
    switch (network) {
      case BlockchainNetwork.ETH:
      case BlockchainNetwork.BSC:
      case BlockchainNetwork.BASE:
        // EVM 兼容网络
        const wallet = ethers.Wallet.createRandom();
        return {
          address: wallet.address,
          privateKey: wallet.privateKey
        };
      
      case BlockchainNetwork.SOLANA:
        // Solana 网络
        const keypair = Keypair.generate();
        return {
          address: keypair.publicKey.toString(),
          privateKey: Buffer.from(keypair.secretKey).toString('hex')
        };
      
      default:
        throw new Error(`Unsupported network: ${network}`);
    }
  }

  /**
   * 从私钥恢复钱包
   * @param privateKey 私钥
   * @param network 区块链网络
   * @returns 钱包地址
   */
  static recoverWallet(privateKey: string, network: BlockchainNetwork): string {
    try {
      switch (network) {
        case BlockchainNetwork.ETH:
        case BlockchainNetwork.BSC:
        case BlockchainNetwork.BASE:
          const wallet = new ethers.Wallet(privateKey);
          return wallet.address;
        
        case BlockchainNetwork.SOLANA:
          const secretKey = Buffer.from(privateKey, 'hex');
          const keypair = Keypair.fromSecretKey(secretKey);
          return keypair.publicKey.toString();
        
        default:
          throw new Error(`Unsupported network: ${network}`);
      }
    } catch (error) {
      throw new Error(`Invalid private key for ${network}: ${error.message}`);
    }
  }

  /**
   * 添加钱包地址
   * @param userId 用户ID
   * @param network 区块链网络
   * @param privateKey 私钥（可选，如果不提供则生成新钱包）
   * @returns 钱包地址信息
   */
  static async addWalletAddress(
    userId: string,
    network: BlockchainNetwork,
    privateKey?: string
  ): Promise<WalletAddress> {
    try {
      let address: string;
      let walletPrivateKey: string;

      if (privateKey) {
        // 从私钥恢复钱包
        address = this.recoverWallet(privateKey, network);
        walletPrivateKey = privateKey;
      } else {
        // 生成新钱包
        const wallet = this.generateWallet(network);
        address = wallet.address;
        walletPrivateKey = wallet.privateKey;
      }

      // 检查地址是否已存在
      const existingWallet = await db.query(
        'SELECT id FROM wallet_addresses WHERE user_id = $1 AND network = $2 AND address = $3',
        [userId, network, address]
      );

      if (existingWallet.rows.length > 0) {
        throw new Error('Wallet address already exists');
      }

      // 加密私钥
      const encryptedPrivateKey = EncryptionUtils.encryptSensitiveData(walletPrivateKey);

      // 存储到数据库
      const result = await db.query(
        `INSERT INTO wallet_addresses (user_id, network, address, private_key, is_active, balance) 
         VALUES ($1, $2, $3, $4, $5, $6) 
         RETURNING id, user_id, network, address, is_active, balance, created_at, updated_at`,
        [userId, network, address, encryptedPrivateKey, true, 0]
      );

      const walletRecord = result.rows[0];

      logUtils.logUserAction(userId, 'wallet_added', { network, address });

      return {
        id: walletRecord.id,
        userId: walletRecord.user_id,
        network: walletRecord.network,
        address: walletRecord.address,
        privateKey: '***masked***',
        isActive: walletRecord.is_active,
        balance: parseFloat(walletRecord.balance),
        createdAt: walletRecord.created_at,
        updatedAt: walletRecord.updated_at
      };
    } catch (error) {
      logger.error('Error adding wallet address:', error);
      throw error;
    }
  }

  /**
   * 获取用户的钱包地址列表
   * @param userId 用户ID
   * @param network 区块链网络（可选）
   * @returns 钱包地址列表
   */
  static async getUserWalletAddresses(
    userId: string,
    network?: BlockchainNetwork
  ): Promise<WalletAddress[]> {
    try {
      let query = 'SELECT id, user_id, network, address, is_active, balance, created_at, updated_at FROM wallet_addresses WHERE user_id = $1';
      const params: any[] = [userId];

      if (network) {
        query += ' AND network = $2';
        params.push(network);
      }

      query += ' ORDER BY created_at DESC';

      const result = await db.query(query, params);

      return result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        network: row.network,
        address: row.address,
        privateKey: '***masked***',
        isActive: row.is_active,
        balance: parseFloat(row.balance),
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      logger.error('Error getting user wallet addresses:', error);
      throw new Error('Failed to get wallet addresses');
    }
  }

  /**
   * 获取解密的私钥（用于交易）
   * @param userId 用户ID
   * @param walletId 钱包ID
   * @returns 解密的私钥
   */
  static async getDecryptedPrivateKey(userId: string, walletId: string): Promise<{
    address: string;
    privateKey: string;
    network: BlockchainNetwork;
  } | null> {
    try {
      const result = await db.query(
        'SELECT address, private_key, network FROM wallet_addresses WHERE id = $1 AND user_id = $2 AND is_active = true',
        [walletId, userId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const wallet = result.rows[0];
      const decryptedPrivateKey = EncryptionUtils.decryptSensitiveData(wallet.private_key);

      return {
        address: wallet.address,
        privateKey: decryptedPrivateKey,
        network: wallet.network
      };
    } catch (error) {
      logger.error('Error getting decrypted private key:', error);
      throw new Error('Failed to decrypt private key');
    }
  }

  /**
   * 更新钱包余额
   * @param walletId 钱包ID
   * @param balance 新余额
   */
  static async updateWalletBalance(walletId: string, balance: number): Promise<void> {
    try {
      await db.query(
        'UPDATE wallet_addresses SET balance = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [balance, walletId]
      );
    } catch (error) {
      logger.error('Error updating wallet balance:', error);
      throw new Error('Failed to update wallet balance');
    }
  }

  /**
   * 删除钱包地址
   * @param userId 用户ID
   * @param walletId 钱包ID
   */
  static async deleteWalletAddress(userId: string, walletId: string): Promise<void> {
    try {
      const result = await db.query(
        'DELETE FROM wallet_addresses WHERE id = $1 AND user_id = $2 RETURNING address, network',
        [walletId, userId]
      );

      if (result.rowCount === 0) {
        throw new Error('Wallet address not found');
      }

      const { address, network } = result.rows[0];
      logUtils.logUserAction(userId, 'wallet_deleted', { walletId, address, network });
    } catch (error) {
      logger.error('Error deleting wallet address:', error);
      throw error;
    }
  }

  /**
   * 激活/停用钱包地址
   * @param userId 用户ID
   * @param walletId 钱包ID
   * @param isActive 是否激活
   */
  static async toggleWalletAddress(
    userId: string,
    walletId: string,
    isActive: boolean
  ): Promise<WalletAddress> {
    try {
      const result = await db.query(
        `UPDATE wallet_addresses SET is_active = $1, updated_at = CURRENT_TIMESTAMP 
         WHERE id = $2 AND user_id = $3 
         RETURNING id, user_id, network, address, is_active, balance, created_at, updated_at`,
        [isActive, walletId, userId]
      );

      if (result.rows.length === 0) {
        throw new Error('Wallet address not found');
      }

      const wallet = result.rows[0];

      logUtils.logUserAction(userId, 'wallet_toggled', {
        walletId,
        address: wallet.address,
        network: wallet.network,
        isActive
      });

      return {
        id: wallet.id,
        userId: wallet.user_id,
        network: wallet.network,
        address: wallet.address,
        privateKey: '***masked***',
        isActive: wallet.is_active,
        balance: parseFloat(wallet.balance),
        createdAt: wallet.created_at,
        updatedAt: wallet.updated_at
      };
    } catch (error) {
      logger.error('Error toggling wallet address:', error);
      throw error;
    }
  }

  /**
   * 从CSV文件导入钱包地址
   * @param userId 用户ID
   * @param csvContent CSV文件内容
   * @param password 解密密码
   * @returns 导入结果
   */
  static async importWalletsFromCSV(
    userId: string,
    csvContent: string,
    password: string
  ): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    try {
      // 解密CSV内容
      const decryptedContent = EncryptionUtils.decryptCSVContent(csvContent, password);
      
      // 解析CSV
      const lines = decryptedContent.split('\n').filter(line => line.trim());
      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
      
      // 验证CSV格式
      if (!headers.includes('network') || !headers.includes('private_key')) {
        throw new Error('CSV must contain "network" and "private_key" columns');
      }

      const networkIndex = headers.indexOf('network');
      const privateKeyIndex = headers.indexOf('private_key');
      const addressIndex = headers.indexOf('address');

      let success = 0;
      let failed = 0;
      const errors: string[] = [];

      // 处理每一行
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim());
          const network = values[networkIndex] as BlockchainNetwork;
          const privateKey = values[privateKeyIndex];
          
          // 验证网络
          if (!Object.values(BlockchainNetwork).includes(network)) {
            throw new Error(`Invalid network: ${network}`);
          }

          // 验证私钥并获取地址
          const address = this.recoverWallet(privateKey, network);
          
          // 如果CSV中有地址列，验证地址是否匹配
          if (addressIndex >= 0 && values[addressIndex]) {
            const csvAddress = values[addressIndex];
            if (address.toLowerCase() !== csvAddress.toLowerCase()) {
              throw new Error(`Address mismatch for private key on line ${i + 1}`);
            }
          }

          // 添加钱包
          await this.addWalletAddress(userId, network, privateKey);
          success++;
        } catch (error) {
          failed++;
          errors.push(`Line ${i + 1}: ${error.message}`);
        }
      }

      logUtils.logUserAction(userId, 'wallets_imported_from_csv', {
        success,
        failed,
        total: lines.length - 1
      });

      return { success, failed, errors };
    } catch (error) {
      logger.error('Error importing wallets from CSV:', error);
      throw error;
    }
  }

  /**
   * 导出钱包地址到CSV
   * @param userId 用户ID
   * @param password 加密密码
   * @param network 区块链网络（可选）
   * @returns 加密的CSV内容
   */
  static async exportWalletsToCSV(
    userId: string,
    password: string,
    network?: BlockchainNetwork
  ): Promise<string> {
    try {
      // 获取钱包地址
      const wallets = await this.getUserWalletAddresses(userId, network);
      
      if (wallets.length === 0) {
        throw new Error('No wallet addresses found');
      }

      // 构建CSV内容
      let csvContent = 'network,address,private_key,balance,created_at\n';
      
      for (const wallet of wallets) {
        // 获取解密的私钥
        const decryptedWallet = await this.getDecryptedPrivateKey(userId, wallet.id);
        if (decryptedWallet) {
          csvContent += `${wallet.network},${wallet.address},${decryptedWallet.privateKey},${wallet.balance},${wallet.createdAt.toISOString()}\n`;
        }
      }

      // 加密CSV内容
      const encryptedContent = EncryptionUtils.encryptCSVContent(csvContent, password);

      logUtils.logUserAction(userId, 'wallets_exported_to_csv', {
        count: wallets.length,
        network
      });

      return encryptedContent;
    } catch (error) {
      logger.error('Error exporting wallets to CSV:', error);
      throw error;
    }
  }

  /**
   * 获取用户钱包统计信息
   * @param userId 用户ID
   * @returns 统计信息
   */
  static async getUserWalletStats(userId: string): Promise<{
    totalWallets: number;
    activeWallets: number;
    networkStats: Record<BlockchainNetwork, number>;
    totalBalance: Record<BlockchainNetwork, number>;
  }> {
    try {
      const result = await db.query(
        `SELECT network, COUNT(*) as total, 
                COUNT(CASE WHEN is_active = true THEN 1 END) as active,
                SUM(balance) as total_balance
         FROM wallet_addresses 
         WHERE user_id = $1 
         GROUP BY network`,
        [userId]
      );

      const networkStats: Record<BlockchainNetwork, number> = {} as any;
      const totalBalance: Record<BlockchainNetwork, number> = {} as any;
      let totalWallets = 0;
      let activeWallets = 0;

      // 初始化统计
      Object.values(BlockchainNetwork).forEach(network => {
        networkStats[network] = 0;
        totalBalance[network] = 0;
      });

      // 处理查询结果
      result.rows.forEach(row => {
        const network = row.network as BlockchainNetwork;
        networkStats[network] = parseInt(row.total);
        totalBalance[network] = parseFloat(row.total_balance) || 0;
        totalWallets += parseInt(row.total);
        activeWallets += parseInt(row.active);
      });

      return {
        totalWallets,
        activeWallets,
        networkStats,
        totalBalance
      };
    } catch (error) {
      logger.error('Error getting user wallet stats:', error);
      throw new Error('Failed to get wallet statistics');
    }
  }
}

import { EventEmitter } from 'events';
import { db } from '../../models/database';
import { logger, logUtils } from '../../utils/logger';
import {
  IRewardSystem,
  RewardRecord,
  RewardRule,
  RewardType,
  RewardStatus,
  TokenType,
  UserRewardStats,
  RewardCampaign,
  Achievement,
  UserActivity,
  DistributionResult,
  RewardError,
  DistributionError,
  InsufficientBudgetError,
  RuleValidationError,
  CooldownError
} from '../base/RewardInterface';

export class RewardService extends EventEmitter implements IRewardSystem {
  private static instance: RewardService;
  private rewardRules: Map<string, RewardRule> = new Map();
  private campaigns: Map<string, RewardCampaign> = new Map();
  private achievements: Map<string, Achievement> = new Map();

  private constructor() {
    super();
    this.initializeDefaultRules();
    this.initializeAchievements();
  }

  static getInstance(): RewardService {
    if (!RewardService.instance) {
      RewardService.instance = new RewardService();
    }
    return RewardService.instance;
  }

  // 创建奖励记录
  async createReward(
    userId: string,
    type: RewardType,
    amount: number,
    tokenType: TokenType,
    reason: string,
    metadata?: any
  ): Promise<RewardRecord> {
    try {
      const result = await db.query(
        `INSERT INTO rewards (user_id, amount, reason, token_symbol, is_distributed, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         RETURNING id, user_id, amount, reason, token_symbol, is_distributed, created_at, updated_at`,
        [userId, amount, reason, tokenType, false]
      );

      const record = result.rows[0];
      const rewardRecord: RewardRecord = {
        id: record.id,
        userId: record.user_id,
        type,
        amount: parseFloat(record.amount),
        tokenType: record.token_symbol as TokenType,
        reason: record.reason,
        status: record.is_distributed ? RewardStatus.DISTRIBUTED : RewardStatus.PENDING,
        metadata,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      };

      logUtils.logUserAction(userId, 'reward_created', {
        rewardId: rewardRecord.id,
        type,
        amount,
        tokenType,
        reason
      });

      this.emit('rewardCreated', rewardRecord);
      return rewardRecord;
    } catch (error) {
      logger.error('Error creating reward:', error);
      throw new RewardError(`Failed to create reward: ${error.message}`);
    }
  }

  // 获取奖励记录
  async getReward(rewardId: string): Promise<RewardRecord | null> {
    try {
      const result = await db.query(
        'SELECT * FROM rewards WHERE id = $1',
        [rewardId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const record = result.rows[0];
      return {
        id: record.id,
        userId: record.user_id,
        type: RewardType.TRADING_VOLUME, // 需要从metadata或其他字段获取
        amount: parseFloat(record.amount),
        tokenType: record.token_symbol as TokenType,
        reason: record.reason,
        status: record.is_distributed ? RewardStatus.DISTRIBUTED : RewardStatus.PENDING,
        txHash: record.tx_hash,
        distributedAt: record.distributed_at,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      };
    } catch (error) {
      logger.error('Error getting reward:', error);
      throw new RewardError(`Failed to get reward: ${error.message}`, 'GET_ERROR', rewardId);
    }
  }

  // 获取用户奖励记录
  async getUserRewards(
    userId: string,
    status?: RewardStatus,
    limit: number = 50,
    offset: number = 0
  ): Promise<RewardRecord[]> {
    try {
      let query = 'SELECT * FROM rewards WHERE user_id = $1';
      const params: any[] = [userId];

      if (status) {
        query += ' AND is_distributed = $2';
        params.push(status === RewardStatus.DISTRIBUTED);
      }

      query += ' ORDER BY created_at DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
      params.push(limit, offset);

      const result = await db.query(query, params);

      return result.rows.map(record => ({
        id: record.id,
        userId: record.user_id,
        type: RewardType.TRADING_VOLUME, // 需要从metadata获取
        amount: parseFloat(record.amount),
        tokenType: record.token_symbol as TokenType,
        reason: record.reason,
        status: record.is_distributed ? RewardStatus.DISTRIBUTED : RewardStatus.PENDING,
        txHash: record.tx_hash,
        distributedAt: record.distributed_at,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }));
    } catch (error) {
      logger.error('Error getting user rewards:', error);
      throw new RewardError(`Failed to get user rewards: ${error.message}`);
    }
  }

  // 更新奖励状态
  async updateRewardStatus(rewardId: string, status: RewardStatus, txHash?: string): Promise<void> {
    try {
      const isDistributed = status === RewardStatus.DISTRIBUTED;
      const distributedAt = isDistributed ? new Date() : null;

      await db.query(
        `UPDATE rewards 
         SET is_distributed = $1, tx_hash = $2, updated_at = CURRENT_TIMESTAMP
         WHERE id = $3`,
        [isDistributed, txHash, rewardId]
      );

      this.emit('rewardStatusUpdated', { rewardId, status, txHash });
    } catch (error) {
      logger.error('Error updating reward status:', error);
      throw new RewardError(`Failed to update reward status: ${error.message}`, 'UPDATE_ERROR', rewardId);
    }
  }

  // 分发奖励
  async distributeReward(rewardId: string): Promise<DistributionResult> {
    try {
      const reward = await this.getReward(rewardId);
      if (!reward) {
        throw new RewardError('Reward not found', 'NOT_FOUND', rewardId);
      }

      if (reward.status === RewardStatus.DISTRIBUTED) {
        throw new DistributionError('Reward already distributed', rewardId);
      }

      // 检查预算
      const budgetCheck = await this.checkBudget(reward.tokenType, reward.amount);
      if (!budgetCheck) {
        throw new InsufficientBudgetError(`Insufficient budget for ${reward.tokenType}`);
      }

      // 模拟分发到用户钱包
      const txHash = await this.performDistribution(reward);

      // 更新奖励状态
      await this.updateRewardStatus(rewardId, RewardStatus.DISTRIBUTED, txHash);

      // 更新用户总奖励
      await this.updateUserTotalRewards(reward.userId, reward.tokenType, reward.amount);

      const result: DistributionResult = {
        success: true,
        rewardId,
        txHash,
        amount: reward.amount,
        tokenType: reward.tokenType,
        message: 'Reward distributed successfully',
        timestamp: new Date()
      };

      logUtils.logUserAction(reward.userId, 'reward_distributed', {
        rewardId,
        amount: reward.amount,
        tokenType: reward.tokenType,
        txHash
      });

      this.emit('rewardDistributed', result);
      return result;
    } catch (error) {
      logger.error('Error distributing reward:', error);
      
      const result: DistributionResult = {
        success: false,
        rewardId,
        amount: 0,
        tokenType: TokenType.TAI,
        message: error.message,
        timestamp: new Date()
      };

      return result;
    }
  }

  // 批量分发奖励
  async batchDistributeRewards(rewardIds: string[]): Promise<DistributionResult[]> {
    const results: DistributionResult[] = [];

    for (const rewardId of rewardIds) {
      try {
        const result = await this.distributeReward(rewardId);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          rewardId,
          amount: 0,
          tokenType: TokenType.TAI,
          message: error.message,
          timestamp: new Date()
        });
      }
    }

    return results;
  }

  // 创建奖励规则
  async createRewardRule(rule: Omit<RewardRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardRule> {
    const ruleId = this.generateRuleId();
    const newRule: RewardRule = {
      ...rule,
      id: ruleId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.rewardRules.set(ruleId, newRule);
    this.emit('ruleCreated', newRule);
    
    return newRule;
  }

  // 更新奖励规则
  async updateRewardRule(ruleId: string, updates: Partial<RewardRule>): Promise<RewardRule> {
    const rule = this.rewardRules.get(ruleId);
    if (!rule) {
      throw new RuleValidationError(`Rule not found: ${ruleId}`);
    }

    const updatedRule = {
      ...rule,
      ...updates,
      updatedAt: new Date()
    };

    this.rewardRules.set(ruleId, updatedRule);
    this.emit('ruleUpdated', updatedRule);
    
    return updatedRule;
  }

  // 删除奖励规则
  async deleteRewardRule(ruleId: string): Promise<void> {
    if (!this.rewardRules.has(ruleId)) {
      throw new RuleValidationError(`Rule not found: ${ruleId}`);
    }

    this.rewardRules.delete(ruleId);
    this.emit('ruleDeleted', { ruleId });
  }

  // 获取奖励规则
  async getRewardRules(type?: RewardType, isActive?: boolean): Promise<RewardRule[]> {
    let rules = Array.from(this.rewardRules.values());

    if (type) {
      rules = rules.filter(rule => rule.type === type);
    }

    if (isActive !== undefined) {
      rules = rules.filter(rule => rule.isActive === isActive);
    }

    return rules;
  }

  // 检查并奖励用户
  async checkAndAwardUser(userId: string, eventType: string, eventData: any): Promise<RewardRecord[]> {
    const rewards: RewardRecord[] = [];
    const applicableRules = await this.getApplicableRules(eventType, eventData);

    for (const rule of applicableRules) {
      try {
        // 检查冷却期
        if (await this.isInCooldown(userId, rule.id)) {
          continue;
        }

        // 检查条件
        if (this.evaluateConditions(rule.conditions, eventData)) {
          // 计算奖励金额
          for (const rewardAmount of rule.rewards) {
            const amount = this.calculateRewardAmount(rewardAmount, eventData);
            
            const reward = await this.createReward(
              userId,
              rule.type,
              amount,
              rewardAmount.tokenType,
              `${rule.name}: ${rule.description}`,
              { ruleId: rule.id, eventType, eventData }
            );

            rewards.push(reward);
          }

          // 记录规则使用
          await this.recordRuleUsage(userId, rule.id);
        }
      } catch (error) {
        logger.error(`Error processing rule ${rule.id} for user ${userId}:`, error);
      }
    }

    return rewards;
  }

  // 处理用户活动
  async processUserActivity(userId: string, activity: UserActivity): Promise<RewardRecord[]> {
    return this.checkAndAwardUser(userId, activity.type, activity.data);
  }

  // 获取用户奖励统计
  async getUserRewardStats(userId: string): Promise<UserRewardStats> {
    try {
      const result = await db.query(
        'SELECT token_symbol, SUM(amount) as total FROM rewards WHERE user_id = $1 GROUP BY token_symbol',
        [userId]
      );

      const totalRewards: Record<TokenType, number> = {} as any;
      const totalClaimed: Record<TokenType, number> = {} as any;
      const totalPending: Record<TokenType, number> = {} as any;

      // 初始化
      Object.values(TokenType).forEach(token => {
        totalRewards[token] = 0;
        totalClaimed[token] = 0;
        totalPending[token] = 0;
      });

      // 处理查询结果
      result.rows.forEach(row => {
        const token = row.token_symbol as TokenType;
        totalRewards[token] = parseFloat(row.total);
      });

      // 获取已分发和待分发的奖励
      const claimedResult = await db.query(
        'SELECT token_symbol, SUM(amount) as total FROM rewards WHERE user_id = $1 AND is_distributed = true GROUP BY token_symbol',
        [userId]
      );

      claimedResult.rows.forEach(row => {
        const token = row.token_symbol as TokenType;
        totalClaimed[token] = parseFloat(row.total);
        totalPending[token] = totalRewards[token] - totalClaimed[token];
      });

      return {
        userId,
        totalRewards,
        totalClaimed,
        totalPending,
        rewardsByType: {} as any, // 需要额外查询
        streakDays: 0, // 需要计算连续登录天数
        level: 1, // 需要根据积分计算等级
        nextLevelRequirement: 1000
      };
    } catch (error) {
      logger.error('Error getting user reward stats:', error);
      throw new RewardError(`Failed to get user reward stats: ${error.message}`);
    }
  }

  // 更新用户等级
  async updateUserLevel(userId: string): Promise<void> {
    // 实现用户等级更新逻辑
  }

  // 处理推荐
  async processReferral(referrerId: string, refereeId: string): Promise<RewardRecord[]> {
    const rewards: RewardRecord[] = [];

    try {
      // 给推荐人奖励
      const referrerReward = await this.createReward(
        referrerId,
        RewardType.REFERRAL,
        50, // 推荐奖励金额
        TokenType.TAI,
        `Referral reward for inviting user ${refereeId}`
      );
      rewards.push(referrerReward);

      // 给被推荐人奖励
      const refereeReward = await this.createReward(
        refereeId,
        RewardType.REFERRAL,
        25, // 新用户奖励金额
        TokenType.TAI,
        `Welcome bonus for joining through referral`
      );
      rewards.push(refereeReward);

      logUtils.logUserAction(referrerId, 'referral_processed', {
        refereeId,
        referrerReward: referrerReward.amount,
        refereeReward: refereeReward.amount
      });

      return rewards;
    } catch (error) {
      logger.error('Error processing referral:', error);
      throw new RewardError(`Failed to process referral: ${error.message}`);
    }
  }

  // 获取推荐统计
  async getReferralStats(userId: string): Promise<any> {
    // 实现推荐统计逻辑
    return {
      totalReferrals: 0,
      successfulReferrals: 0,
      totalEarned: 0,
      pendingRewards: 0
    };
  }

  // 创建活动
  async createCampaign(campaign: Omit<RewardCampaign, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardCampaign> {
    const campaignId = this.generateCampaignId();
    const newCampaign: RewardCampaign = {
      ...campaign,
      id: campaignId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.campaigns.set(campaignId, newCampaign);
    this.emit('campaignCreated', newCampaign);
    
    return newCampaign;
  }

  // 更新活动
  async updateCampaign(campaignId: string, updates: Partial<RewardCampaign>): Promise<RewardCampaign> {
    const campaign = this.campaigns.get(campaignId);
    if (!campaign) {
      throw new RewardError(`Campaign not found: ${campaignId}`);
    }

    const updatedCampaign = {
      ...campaign,
      ...updates,
      updatedAt: new Date()
    };

    this.campaigns.set(campaignId, updatedCampaign);
    this.emit('campaignUpdated', updatedCampaign);
    
    return updatedCampaign;
  }

  // 获取活动列表
  async getCampaigns(isActive?: boolean): Promise<RewardCampaign[]> {
    let campaigns = Array.from(this.campaigns.values());

    if (isActive !== undefined) {
      campaigns = campaigns.filter(campaign => campaign.isActive === isActive);
    }

    return campaigns;
  }

  // 检查成就
  async checkAchievements(userId: string): Promise<Achievement[]> {
    const unlockedAchievements: Achievement[] = [];
    
    for (const achievement of this.achievements.values()) {
      if (!achievement.unlockedBy.includes(userId)) {
        // 检查成就条件
        const userData = await this.getUserActivityData(userId);
        if (this.evaluateConditions(achievement.conditions, userData)) {
          unlockedAchievements.push(achievement);
          achievement.unlockedBy.push(userId);
        }
      }
    }

    return unlockedAchievements;
  }

  // 解锁成就
  async unlockAchievement(userId: string, achievementId: string): Promise<RewardRecord[]> {
    const achievement = this.achievements.get(achievementId);
    if (!achievement) {
      throw new RewardError(`Achievement not found: ${achievementId}`);
    }

    const rewards: RewardRecord[] = [];
    
    for (const rewardAmount of achievement.rewards) {
      const reward = await this.createReward(
        userId,
        RewardType.ACHIEVEMENT,
        rewardAmount.amount,
        rewardAmount.tokenType,
        `Achievement unlocked: ${achievement.name}`,
        { achievementId }
      );
      rewards.push(reward);
    }

    return rewards;
  }

  // 获取用户成就
  async getUserAchievements(userId: string): Promise<Achievement[]> {
    return Array.from(this.achievements.values()).filter(
      achievement => achievement.unlockedBy.includes(userId)
    );
  }

  // 获取系统统计
  async getSystemStats(): Promise<any> {
    try {
      const totalRewardsResult = await db.query(
        'SELECT token_symbol, SUM(amount) as total, COUNT(*) as count FROM rewards GROUP BY token_symbol'
      );

      const distributedRewardsResult = await db.query(
        'SELECT token_symbol, SUM(amount) as total, COUNT(*) as count FROM rewards WHERE is_distributed = true GROUP BY token_symbol'
      );

      return {
        totalRewards: totalRewardsResult.rows,
        distributedRewards: distributedRewardsResult.rows,
        activeRules: this.rewardRules.size,
        activeCampaigns: Array.from(this.campaigns.values()).filter(c => c.isActive).length,
        totalAchievements: this.achievements.size
      };
    } catch (error) {
      logger.error('Error getting system stats:', error);
      throw new RewardError(`Failed to get system stats: ${error.message}`);
    }
  }

  // 导出奖励数据
  async exportRewards(startDate: Date, endDate: Date): Promise<any> {
    try {
      const result = await db.query(
        'SELECT * FROM rewards WHERE created_at BETWEEN $1 AND $2 ORDER BY created_at DESC',
        [startDate, endDate]
      );

      return result.rows;
    } catch (error) {
      logger.error('Error exporting rewards:', error);
      throw new RewardError(`Failed to export rewards: ${error.message}`);
    }
  }

  // 验证奖励预算
  async validateRewardBudget(): Promise<boolean> {
    // 实现预算验证逻辑
    return true;
  }

  // 私有辅助方法
  private async checkBudget(tokenType: TokenType, amount: number): Promise<boolean> {
    // 模拟预算检查
    return true;
  }

  private async performDistribution(reward: RewardRecord): Promise<string> {
    // 模拟分发到区块链
    return `0x${Math.random().toString(16).substr(2, 64)}`;
  }

  private async updateUserTotalRewards(userId: string, tokenType: TokenType, amount: number): Promise<void> {
    await db.query(
      'UPDATE users SET total_rewards = total_rewards + $1 WHERE id = $2',
      [amount, userId]
    );
  }

  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCampaignId(): string {
    return `campaign_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getApplicableRules(eventType: string, eventData: any): Promise<RewardRule[]> {
    return Array.from(this.rewardRules.values()).filter(rule => rule.isActive);
  }

  private async isInCooldown(userId: string, ruleId: string): Promise<boolean> {
    // 检查用户是否在冷却期内
    return false;
  }

  private evaluateConditions(conditions: any[], data: any): boolean {
    // 评估奖励条件
    return true;
  }

  private calculateRewardAmount(rewardAmount: any, eventData: any): number {
    if (rewardAmount.percentage && rewardAmount.baseField) {
      const baseValue = eventData[rewardAmount.baseField] || 0;
      return baseValue * (rewardAmount.percentage / 100);
    }
    return rewardAmount.amount;
  }

  private async recordRuleUsage(userId: string, ruleId: string): Promise<void> {
    // 记录规则使用情况
  }

  private async getUserActivityData(userId: string): Promise<any> {
    // 获取用户活动数据用于成就检查
    return {};
  }

  private initializeDefaultRules(): void {
    // 初始化默认奖励规则
  }

  private initializeAchievements(): void {
    // 初始化成就系统
  }
}

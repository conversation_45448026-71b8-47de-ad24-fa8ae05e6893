// 奖励类型
export enum RewardType {
  TRADING_VOLUME = 'trading_volume',
  STRATEGY_PROFIT = 'strategy_profit',
  REFERRAL = 'referral',
  DAILY_LOGIN = 'daily_login',
  FIRST_TRADE = 'first_trade',
  MILESTONE = 'milestone',
  LIQUIDITY_PROVISION = 'liquidity_provision',
  SOCIAL_SHARING = 'social_sharing',
  FEEDBACK = 'feedback',
  BETA_TESTING = 'beta_testing',
  PREMIUM_SUBSCRIPTION = 'premium_subscription',
  ACHIEVEMENT = 'achievement'
}

// 奖励状态
export enum RewardStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  DISTRIBUTED = 'distributed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 代币类型
export enum TokenType {
  TAI = 'TAI', // TradeAI Token
  USDT = 'USDT',
  USDC = 'USDC',
  ETH = 'ETH',
  BTC = 'BTC'
}

// 奖励记录
export interface RewardRecord {
  id: string;
  userId: string;
  type: RewardType;
  amount: number;
  tokenType: TokenType;
  reason: string;
  status: RewardStatus;
  metadata?: Record<string, any>;
  txHash?: string;
  distributedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 奖励规则
export interface RewardRule {
  id: string;
  type: RewardType;
  name: string;
  description: string;
  isActive: boolean;
  conditions: RewardCondition[];
  rewards: RewardAmount[];
  cooldownPeriod?: number; // 冷却期（秒）
  maxClaimsPerUser?: number; // 每用户最大领取次数
  maxClaimsTotal?: number; // 总最大领取次数
  validFrom?: Date;
  validTo?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 奖励条件
export interface RewardCondition {
  field: string; // 条件字段
  operator: 'eq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'between';
  value: any; // 条件值
  description?: string;
}

// 奖励金额
export interface RewardAmount {
  tokenType: TokenType;
  amount: number;
  percentage?: number; // 基于某个值的百分比
  baseField?: string; // 百分比计算的基础字段
}

// 用户奖励统计
export interface UserRewardStats {
  userId: string;
  totalRewards: Record<TokenType, number>;
  totalClaimed: Record<TokenType, number>;
  totalPending: Record<TokenType, number>;
  rewardsByType: Record<RewardType, number>;
  lastRewardDate?: Date;
  streakDays: number;
  level: number;
  nextLevelRequirement: number;
}

// 奖励活动
export interface RewardCampaign {
  id: string;
  name: string;
  description: string;
  type: 'limited_time' | 'ongoing' | 'milestone';
  isActive: boolean;
  rules: RewardRule[];
  totalBudget?: Record<TokenType, number>;
  distributedAmount?: Record<TokenType, number>;
  participantCount: number;
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 推荐奖励配置
export interface ReferralConfig {
  referrerReward: RewardAmount[];
  refereeReward: RewardAmount[];
  minimumTradeVolume?: number;
  validityPeriod?: number; // 推荐有效期（天）
  maxReferrals?: number; // 最大推荐人数
  tieredRewards?: {
    threshold: number;
    multiplier: number;
  }[];
}

// 等级系统
export interface LevelSystem {
  level: number;
  name: string;
  requiredPoints: number;
  benefits: {
    rewardMultiplier: number;
    tradingFeeDiscount: number;
    premiumFeatures: string[];
    withdrawalLimits: Record<TokenType, number>;
  };
  badge?: string;
  color?: string;
}

// 成就系统
export interface Achievement {
  id: string;
  name: string;
  description: string;
  category: 'trading' | 'social' | 'milestone' | 'special';
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  conditions: RewardCondition[];
  rewards: RewardAmount[];
  isHidden: boolean; // 隐藏成就
  unlockedBy: string[]; // 解锁此成就的用户ID列表
  createdAt: Date;
}

// 奖励分发结果
export interface DistributionResult {
  success: boolean;
  rewardId: string;
  txHash?: string;
  amount: number;
  tokenType: TokenType;
  message: string;
  timestamp: Date;
}

// 奖励系统接口
export interface IRewardSystem {
  // 奖励记录管理
  createReward(userId: string, type: RewardType, amount: number, tokenType: TokenType, reason: string, metadata?: any): Promise<RewardRecord>;
  getReward(rewardId: string): Promise<RewardRecord | null>;
  getUserRewards(userId: string, status?: RewardStatus, limit?: number, offset?: number): Promise<RewardRecord[]>;
  updateRewardStatus(rewardId: string, status: RewardStatus, txHash?: string): Promise<void>;
  
  // 奖励分发
  distributeReward(rewardId: string): Promise<DistributionResult>;
  batchDistributeRewards(rewardIds: string[]): Promise<DistributionResult[]>;
  
  // 奖励规则管理
  createRewardRule(rule: Omit<RewardRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardRule>;
  updateRewardRule(ruleId: string, updates: Partial<RewardRule>): Promise<RewardRule>;
  deleteRewardRule(ruleId: string): Promise<void>;
  getRewardRules(type?: RewardType, isActive?: boolean): Promise<RewardRule[]>;
  
  // 自动奖励检查
  checkAndAwardUser(userId: string, eventType: string, eventData: any): Promise<RewardRecord[]>;
  processUserActivity(userId: string, activity: UserActivity): Promise<RewardRecord[]>;
  
  // 用户统计
  getUserRewardStats(userId: string): Promise<UserRewardStats>;
  updateUserLevel(userId: string): Promise<void>;
  
  // 推荐系统
  processReferral(referrerId: string, refereeId: string): Promise<RewardRecord[]>;
  getReferralStats(userId: string): Promise<any>;
  
  // 活动管理
  createCampaign(campaign: Omit<RewardCampaign, 'id' | 'createdAt' | 'updatedAt'>): Promise<RewardCampaign>;
  updateCampaign(campaignId: string, updates: Partial<RewardCampaign>): Promise<RewardCampaign>;
  getCampaigns(isActive?: boolean): Promise<RewardCampaign[]>;
  
  // 成就系统
  checkAchievements(userId: string): Promise<Achievement[]>;
  unlockAchievement(userId: string, achievementId: string): Promise<RewardRecord[]>;
  getUserAchievements(userId: string): Promise<Achievement[]>;
  
  // 系统管理
  getSystemStats(): Promise<any>;
  exportRewards(startDate: Date, endDate: Date): Promise<any>;
  validateRewardBudget(): Promise<boolean>;
}

// 用户活动数据
export interface UserActivity {
  type: 'trade' | 'login' | 'referral' | 'social' | 'subscription' | 'feedback';
  userId: string;
  data: any;
  timestamp: Date;
}

// 奖励错误类
export class RewardError extends Error {
  constructor(
    message: string,
    public code?: string,
    public rewardId?: string
  ) {
    super(message);
    this.name = 'RewardError';
  }
}

// 分发错误
export class DistributionError extends RewardError {
  constructor(message: string, rewardId?: string) {
    super(message, 'DISTRIBUTION_ERROR', rewardId);
    this.name = 'DistributionError';
  }
}

// 余额不足错误
export class InsufficientBudgetError extends RewardError {
  constructor(message: string) {
    super(message, 'INSUFFICIENT_BUDGET');
    this.name = 'InsufficientBudgetError';
  }
}

// 规则验证错误
export class RuleValidationError extends RewardError {
  constructor(message: string) {
    super(message, 'RULE_VALIDATION_ERROR');
    this.name = 'RuleValidationError';
  }
}

// 冷却期错误
export class CooldownError extends RewardError {
  constructor(message: string, remainingTime: number) {
    super(message, 'COOLDOWN_ERROR');
    this.name = 'CooldownError';
    this.remainingTime = remainingTime;
  }
  
  public remainingTime: number;
}

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import path from 'path';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { logger } from './utils/logger';
// import { db } from './models/database'; // 暂时注释掉，避免数据库连接错误

// 加载环境变量
dotenv.config();

// 创建 Express 应用
const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// 中间件配置
// 开发环境下禁用 CSP，生产环境启用
if (process.env.NODE_ENV === 'production') {
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-eval'"], // 允许 eval，用于开发
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }));
} else {
  // 开发环境下使用更宽松的安全策略
  app.use(helmet({
    contentSecurityPolicy: false, // 开发环境禁用 CSP
  }));
}

app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/dist')));
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API 路由 - 临时实现，避免导入不存在的文件
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'TradeAI Bot API',
    version: '1.0.0',
    endpoints: [
      '/api/health',
      '/api/auth',
      '/api/exchanges',
      '/api/strategies',
      '/api/wallets',
      '/api/rewards'
    ]
  });
});

app.use('/api/auth', (req, res) => {
  res.json({
    success: true,
    message: 'Auth API - Coming soon',
    endpoints: ['POST /login', 'POST /register', 'GET /profile']
  });
});

app.use('/api/exchanges', (req, res) => {
  res.json({
    success: true,
    message: 'Exchanges API - Coming soon',
    supported: ['binance', 'okx', 'htx', 'pancakeswap', 'uniswap']
  });
});

app.use('/api/strategies', (req, res) => {
  res.json({
    success: true,
    message: 'Strategies API - Coming soon',
    types: ['grid', 'trend_following', 'ai', 'dex_grid']
  });
});

app.use('/api/wallets', (req, res) => {
  res.json({
    success: true,
    message: 'Wallets API - Coming soon',
    networks: ['ethereum', 'bsc', 'base', 'solana']
  });
});

app.use('/api/rewards', (req, res) => {
  res.json({
    success: true,
    message: 'Rewards API - Coming soon',
    features: ['trading_rewards', 'referral', 'achievements']
  });
});

app.use('/api/orders', (req, res) => {
  res.json({ success: true, message: 'Orders API - Coming soon' });
});

app.use('/api/trades', (req, res) => {
  res.json({ success: true, message: 'Trades API - Coming soon' });
});

app.use('/api/subscriptions', (req, res) => {
  res.json({ success: true, message: 'Subscriptions API - Coming soon' });
});

logger.info('API routes loaded successfully');

// WebSocket 连接处理
wss.on('connection', (ws, req) => {
  logger.info('New WebSocket connection established');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      handleWebSocketMessage(ws, data);
    } catch (error) {
      logger.error('Invalid WebSocket message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format'
      }));
    }
  });

  ws.on('close', () => {
    logger.info('WebSocket connection closed');
  });

  ws.on('error', (error) => {
    logger.error('WebSocket error:', error);
  });

  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Connected to TradeAI Bot WebSocket'
  }));
});

// WebSocket 消息处理
function handleWebSocketMessage(ws: any, data: any) {
  switch (data.type) {
    case 'ping':
      ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
      break;
    
    case 'subscribe':
      // 处理订阅请求
      handleSubscription(ws, data);
      break;
    
    case 'unsubscribe':
      // 处理取消订阅请求
      handleUnsubscription(ws, data);
      break;
    
    default:
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Unknown message type'
      }));
  }
}

// 处理订阅
function handleSubscription(ws: any, data: any) {
  const { channel, userId } = data;
  
  if (!channel || !userId) {
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Channel and userId are required for subscription'
    }));
    return;
  }

  // 这里可以添加订阅逻辑
  ws.userId = userId;
  ws.subscribedChannels = ws.subscribedChannels || new Set();
  ws.subscribedChannels.add(channel);
  
  ws.send(JSON.stringify({
    type: 'subscribed',
    channel,
    message: `Subscribed to ${channel}`
  }));
  
  logger.info(`User ${userId} subscribed to channel ${channel}`);
}

// 处理取消订阅
function handleUnsubscription(ws: any, data: any) {
  const { channel } = data;

  if (!channel) {
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Channel is required for unsubscription'
    }));
    return;
  }

  if (ws.subscribedChannels) {
    ws.subscribedChannels.delete(channel);
  }

  ws.send(JSON.stringify({
    type: 'unsubscribed',
    channel,
    message: `Unsubscribed from ${channel}`
  }));

  logger.info(`User unsubscribed from channel ${channel}`);
}

// 404 处理
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.path
  });
});

// 生产环境下的前端路由处理
if (process.env.NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/dist/index.html'));
  });
}

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', err);

  res.status(err.status || 500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

const PORT = process.env.PORT || 3001;

// 启动服务器
const startServer = async () => {
  try {
    logger.info('Starting TradeAI Bot server...');

    // 注释掉数据库连接，避免启动时崩溃
    // logger.info('Connecting to database...');
    // await db.query('SELECT 1');
    // logger.info('Database connected successfully');

    server.listen(PORT, () => {
      logger.info(`🚀 TradeAI Bot server is running on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
      logger.info(`🔗 API Base: http://localhost:${PORT}/api`);

      if (process.env.NODE_ENV !== 'production') {
        logger.info(`🌐 Frontend: http://localhost:3000`);
      }

      logger.info('✅ Server started successfully!');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 启动服务器
startServer();
  
  if (ws.subscribedChannels) {
    ws.subscribedChannels.delete(channel);
  }
  
  ws.send(JSON.stringify({
    type: 'unsubscribed',
    channel,
    message: `Unsubscribed from ${channel}`
  }));
}

// 广播消息到所有订阅的客户端
export function broadcastToChannel(channel: string, message: any) {
  wss.clients.forEach((client: any) => {
    if (client.readyState === 1 && // WebSocket.OPEN
        client.subscribedChannels && 
        client.subscribedChannels.has(channel)) {
      client.send(JSON.stringify({
        type: 'broadcast',
        channel,
        data: message,
        timestamp: Date.now()
      }));
    }
  });
}

// 发送消息给特定用户
export function sendToUser(userId: string, message: any) {
  wss.clients.forEach((client: any) => {
    if (client.readyState === 1 && client.userId === userId) {
      client.send(JSON.stringify({
        type: 'direct',
        data: message,
        timestamp: Date.now()
      }));
    }
  });
}

// 错误处理中间件
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', error);
  
  res.status(error.status || 500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: error.stack })
  });
});

// 404 处理
app.use('*', (req, res) => {
  if (req.originalUrl.startsWith('/api/')) {
    res.status(404).json({
      success: false,
      message: 'API endpoint not found'
    });
  } else if (process.env.NODE_ENV === 'production') {
    // 在生产环境中，为前端路由返回 index.html
    res.sendFile(path.join(__dirname, '../client/dist/index.html'));
  } else {
    res.status(404).json({
      success: false,
      message: 'Page not found'
    });
  }
});

// 启动服务器
const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  logger.info(`TradeAI Bot server is running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV}`);
  logger.info(`WebSocket server is ready`);
});

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  server.close(() => {
    logger.info('HTTP server closed');
  });
  
  await db.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  
  server.close(() => {
    logger.info('HTTP server closed');
  });
  
  await db.close();
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export { app, server, wss };

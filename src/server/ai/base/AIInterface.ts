// AI模型类型
export enum AIModelType {
  LSTM = 'lstm',
  GRU = 'gru',
  TRANSFORMER = 'transformer',
  CNN = 'cnn',
  RANDOM_FOREST = 'random_forest',
  XG_BOOST = 'xgboost',
  SVM = 'svm',
  LINEAR_REGRESSION = 'linear_regression'
}

// 预测类型
export enum PredictionType {
  PRICE = 'price',
  DIRECTION = 'direction',
  VOLATILITY = 'volatility',
  VOLUME = 'volume',
  TREND_STRENGTH = 'trend_strength',
  SUPPORT_RESISTANCE = 'support_resistance'
}

// 时间框架
export enum TimeFrame {
  MINUTE_1 = '1m',
  MINUTE_5 = '5m',
  MINUTE_15 = '15m',
  MINUTE_30 = '30m',
  HOUR_1 = '1h',
  HOUR_4 = '4h',
  HOUR_12 = '12h',
  DAY_1 = '1d',
  WEEK_1 = '1w'
}

// 市场数据特征
export interface MarketFeatures {
  timestamp: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  
  // 技术指标
  sma_20?: number;
  sma_50?: number;
  ema_12?: number;
  ema_26?: number;
  rsi?: number;
  macd?: number;
  macd_signal?: number;
  bollinger_upper?: number;
  bollinger_lower?: number;
  atr?: number;
  
  // 市场情绪指标
  fear_greed_index?: number;
  social_sentiment?: number;
  news_sentiment?: number;
  
  // 链上数据（适用于加密货币）
  active_addresses?: number;
  transaction_count?: number;
  network_hash_rate?: number;
  exchange_inflow?: number;
  exchange_outflow?: number;
}

// AI预测结果
export interface PredictionResult {
  type: PredictionType;
  value: number;
  confidence: number; // 0-1
  timestamp: Date;
  timeframe: TimeFrame;
  metadata?: {
    model_version?: string;
    feature_importance?: Record<string, number>;
    prediction_interval?: [number, number];
    model_accuracy?: number;
  };
}

// 模型训练配置
export interface TrainingConfig {
  modelType: AIModelType;
  symbol: string;
  timeframe: TimeFrame;
  lookbackPeriod: number; // 历史数据回看期
  predictionHorizon: number; // 预测时间范围
  features: string[]; // 使用的特征列表
  
  // 模型参数
  epochs?: number;
  batchSize?: number;
  learningRate?: number;
  hiddenLayers?: number[];
  dropout?: number;
  
  // 数据分割
  trainRatio: number;
  validationRatio: number;
  testRatio: number;
  
  // 其他配置
  normalizeData?: boolean;
  useGpu?: boolean;
  saveModel?: boolean;
}

// 模型评估指标
export interface ModelMetrics {
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1Score?: number;
  mse?: number; // Mean Squared Error
  mae?: number; // Mean Absolute Error
  rmse?: number; // Root Mean Squared Error
  mape?: number; // Mean Absolute Percentage Error
  sharpeRatio?: number;
  maxDrawdown?: number;
  winRate?: number;
  profitFactor?: number;
}

// 模型信息
export interface ModelInfo {
  id: string;
  name: string;
  type: AIModelType;
  symbol: string;
  timeframe: TimeFrame;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  metrics: ModelMetrics;
  config: TrainingConfig;
}

// AI服务接口
export interface IAIService {
  // 模型管理
  createModel(config: TrainingConfig): Promise<ModelInfo>;
  trainModel(modelId: string, data: MarketFeatures[]): Promise<ModelMetrics>;
  loadModel(modelId: string): Promise<void>;
  saveModel(modelId: string, path?: string): Promise<string>;
  deleteModel(modelId: string): Promise<void>;
  
  // 预测
  predict(modelId: string, features: MarketFeatures[]): Promise<PredictionResult>;
  batchPredict(modelId: string, featuresArray: MarketFeatures[][]): Promise<PredictionResult[]>;
  
  // 模型评估
  evaluateModel(modelId: string, testData: MarketFeatures[]): Promise<ModelMetrics>;
  backtestModel(modelId: string, historicalData: MarketFeatures[]): Promise<any>;
  
  // 特征工程
  extractFeatures(rawData: any[]): Promise<MarketFeatures[]>;
  calculateTechnicalIndicators(priceData: any[]): Promise<any>;
  
  // 模型优化
  optimizeHyperparameters(config: TrainingConfig, data: MarketFeatures[]): Promise<TrainingConfig>;
  ensemblePredict(modelIds: string[], features: MarketFeatures[]): Promise<PredictionResult>;
  
  // 实时预测
  startRealTimePrediction(modelId: string, symbol: string, callback: (prediction: PredictionResult) => void): Promise<void>;
  stopRealTimePrediction(modelId: string): Promise<void>;
  
  // 模型监控
  getModelPerformance(modelId: string, startDate?: Date, endDate?: Date): Promise<any>;
  detectModelDrift(modelId: string, recentData: MarketFeatures[]): Promise<boolean>;
  
  // 工具方法
  getAvailableModels(): Promise<ModelInfo[]>;
  getModelInfo(modelId: string): Promise<ModelInfo>;
  updateModelConfig(modelId: string, config: Partial<TrainingConfig>): Promise<void>;
}

// AI策略信号
export interface AISignal {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  price: number;
  quantity: number;
  stopLoss?: number;
  takeProfit?: number;
  reasoning: string;
  predictions: PredictionResult[];
  timestamp: Date;
}

// AI策略配置
export interface AIStrategyConfig {
  modelIds: string[];
  symbol: string;
  timeframe: TimeFrame;
  minConfidence: number;
  ensembleMethod: 'average' | 'weighted' | 'voting';
  riskManagement: {
    maxPositionSize: number;
    stopLossPercent: number;
    takeProfitPercent: number;
    maxDailyLoss: number;
  };
  rebalanceFrequency: number; // 重新平衡频率（分钟）
}

// AI错误类
export class AIError extends Error {
  constructor(
    message: string,
    public code?: string,
    public modelId?: string
  ) {
    super(message);
    this.name = 'AIError';
  }
}

// 模型训练错误
export class ModelTrainingError extends AIError {
  constructor(message: string, modelId?: string) {
    super(message, 'MODEL_TRAINING_ERROR', modelId);
    this.name = 'ModelTrainingError';
  }
}

// 预测错误
export class PredictionError extends AIError {
  constructor(message: string, modelId?: string) {
    super(message, 'PREDICTION_ERROR', modelId);
    this.name = 'PredictionError';
  }
}

// 数据错误
export class DataError extends AIError {
  constructor(message: string) {
    super(message, 'DATA_ERROR');
    this.name = 'DataError';
  }
}

// 模型不存在错误
export class ModelNotFoundError extends AIError {
  constructor(modelId: string) {
    super(`Model not found: ${modelId}`, 'MODEL_NOT_FOUND', modelId);
    this.name = 'ModelNotFoundError';
  }
}

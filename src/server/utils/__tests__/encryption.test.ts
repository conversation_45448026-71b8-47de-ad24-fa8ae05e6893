import { EncryptionUtils } from '../encryption';

describe('EncryptionUtils', () => {
  const testData = 'test-sensitive-data';
  const testPassword = 'test-password-123';

  describe('encryptSensitiveData and decryptSensitiveData', () => {
    it('should encrypt and decrypt data correctly', () => {
      const encrypted = EncryptionUtils.encryptSensitiveData(testData);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(testData);

      const decrypted = EncryptionUtils.decryptSensitiveData(encrypted);
      expect(decrypted).toBe(testData);
    });

    it('should throw error when decrypting invalid data', () => {
      expect(() => {
        EncryptionUtils.decryptSensitiveData('invalid-encrypted-data');
      }).toThrow('Decryption failed');
    });
  });

  describe('hashPassword and verifyPassword', () => {
    it('should hash and verify password correctly', async () => {
      const hashedPassword = await EncryptionUtils.hashPassword(testPassword);
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(testPassword);

      const isValid = await EncryptionUtils.verifyPassword(testPassword, hashedPassword);
      expect(isValid).toBe(true);

      const isInvalid = await EncryptionUtils.verifyPassword('wrong-password', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe('generateJWTToken and verifyJWTToken', () => {
    it('should generate and verify JWT token correctly', () => {
      const payload = { userId: '123', email: '<EMAIL>' };
      const token = EncryptionUtils.generateJWTToken(payload);
      expect(token).toBeDefined();

      const decoded = EncryptionUtils.verifyJWTToken(token);
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.email).toBe(payload.email);
    });

    it('should throw error for invalid token', () => {
      expect(() => {
        EncryptionUtils.verifyJWTToken('invalid-token');
      }).toThrow('Invalid token');
    });
  });

  describe('generateRandomString', () => {
    it('should generate random string of specified length', () => {
      const length = 32;
      const randomString = EncryptionUtils.generateRandomString(length);
      expect(randomString).toHaveLength(length);
      expect(typeof randomString).toBe('string');
    });

    it('should generate different strings on multiple calls', () => {
      const string1 = EncryptionUtils.generateRandomString(16);
      const string2 = EncryptionUtils.generateRandomString(16);
      expect(string1).not.toBe(string2);
    });
  });

  describe('encryptCSVContent and decryptCSVContent', () => {
    it('should encrypt and decrypt CSV content correctly', () => {
      const csvContent = 'address,private_key\n0x123,abc123\n0x456,def456';
      
      const encrypted = EncryptionUtils.encryptCSVContent(csvContent, testPassword);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(csvContent);

      const decrypted = EncryptionUtils.decryptCSVContent(encrypted, testPassword);
      expect(decrypted).toBe(csvContent);
    });

    it('should throw error with wrong password', () => {
      const csvContent = 'test,content';
      const encrypted = EncryptionUtils.encryptCSVContent(csvContent, testPassword);
      
      expect(() => {
        EncryptionUtils.decryptCSVContent(encrypted, 'wrong-password');
      }).toThrow('CSV decryption failed');
    });
  });

  describe('createSignature and verifySignature', () => {
    it('should create and verify signature correctly', () => {
      const data = 'test-data-to-sign';
      const secret = 'test-secret-key';
      
      const signature = EncryptionUtils.createSignature(data, secret);
      expect(signature).toBeDefined();

      const isValid = EncryptionUtils.verifySignature(data, signature, secret);
      expect(isValid).toBe(true);

      const isInvalid = EncryptionUtils.verifySignature('tampered-data', signature, secret);
      expect(isInvalid).toBe(false);
    });
  });

  describe('obfuscateAmount', () => {
    it('should obfuscate amount within variation range', () => {
      const amount = 100;
      const variation = 5; // 5%
      
      const obfuscated = EncryptionUtils.obfuscateAmount(amount, variation);
      expect(obfuscated).toBeGreaterThanOrEqual(95);
      expect(obfuscated).toBeLessThanOrEqual(105);
    });

    it('should not return negative amounts', () => {
      const amount = 1;
      const variation = 200; // 200% variation
      
      const obfuscated = EncryptionUtils.obfuscateAmount(amount, variation);
      expect(obfuscated).toBeGreaterThanOrEqual(0);
    });
  });

  describe('generateTimestampSignature and verifyTimestampSignature', () => {
    it('should generate and verify timestamp signature correctly', () => {
      const data = 'test-data';
      const timestamp = Date.now();
      const secret = 'test-secret';
      
      const signature = EncryptionUtils.generateTimestampSignature(data, timestamp, secret);
      expect(signature).toBeDefined();

      const isValid = EncryptionUtils.verifyTimestampSignature(data, timestamp, signature, secret);
      expect(isValid).toBe(true);
    });

    it('should reject expired timestamp', () => {
      const data = 'test-data';
      const timestamp = Date.now() - 400000; // 6+ minutes ago
      const secret = 'test-secret';
      
      const signature = EncryptionUtils.generateTimestampSignature(data, timestamp, secret);
      const isValid = EncryptionUtils.verifyTimestampSignature(data, timestamp, signature, secret, 300000); // 5 min max age
      expect(isValid).toBe(false);
    });
  });
});

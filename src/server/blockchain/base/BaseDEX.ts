import { ethers } from 'ethers';
import { EventEmitter } from 'events';
import { logger } from '../../utils/logger';
import { BlockchainNetwork, DEXProtocol } from '../../../shared/types';
import {
  IDEX,
  DEXConfig,
  DEXToken,
  DEXPair,
  DEXRoute,
  DEXPrice,
  DEXSwapParams,
  DEXSwapResult,
  DEXLiquidityParams,
  DEXLiquidityResult,
  WalletConfig,
  DEXError,
  BlockchainNetworkError,
  SwapError,
  GasError
} from './DEXInterface';

export abstract class BaseDEX extends EventEmitter implements IDEX {
  public readonly protocol: DEXProtocol;
  public readonly network: BlockchainNetwork;
  protected config: DEXConfig;
  protected provider: ethers.JsonRpcProvider;
  protected routerContract: ethers.Contract;
  protected factoryContract: ethers.Contract;
  protected _isConnected: boolean = false;

  constructor(protocol: DEXProtocol, network: BlockchainNetwork) {
    super();
    this.protocol = protocol;
    this.network = network;
  }

  get isConnected(): boolean {
    return this._isConnected;
  }

  // 抽象方法，子类必须实现
  protected abstract getRouterABI(): any[];
  protected abstract getFactoryABI(): any[];
  protected abstract getERC20ABI(): any[];
  protected abstract getPairABI(): any[];

  // 初始化DEX
  async initialize(config: DEXConfig): Promise<void> {
    this.config = config;

    try {
      // 创建提供者
      this.provider = new ethers.JsonRpcProvider(config.rpcUrl);

      // 创建合约实例
      this.routerContract = new ethers.Contract(
        config.routerAddress,
        this.getRouterABI(),
        this.provider
      );

      this.factoryContract = new ethers.Contract(
        config.factoryAddress,
        this.getFactoryABI(),
        this.provider
      );

      logger.info(`${this.protocol} on ${this.network} initialized`);
    } catch (error) {
      logger.error(`Failed to initialize ${this.protocol}:`, error);
      throw new DEXError(`Initialization failed: ${error.message}`, this.protocol, this.network);
    }
  }

  // 连接到区块链网络
  async connect(): Promise<void> {
    try {
      // 测试网络连接
      const network = await this.provider.getNetwork();
      const blockNumber = await this.provider.getBlockNumber();

      this._isConnected = true;
      this.emit('connected');

      logger.info(`Connected to ${this.protocol} on ${this.network}, block: ${blockNumber}`);
    } catch (error) {
      this._isConnected = false;
      this.emit('error', error);
      throw new BlockchainNetworkError(`Connection failed: ${error.message}`, this.protocol, this.network);
    }
  }

  // 断开连接
  async disconnect(): Promise<void> {
    this._isConnected = false;
    this.emit('disconnected');
    logger.info(`Disconnected from ${this.protocol} on ${this.network}`);
  }

  // 获取代币信息
  async getToken(address: string): Promise<DEXToken> {
    try {
      const tokenContract = new ethers.Contract(address, this.getERC20ABI(), this.provider);

      const [symbol, name, decimals] = await Promise.all([
        tokenContract.symbol(),
        tokenContract.name(),
        tokenContract.decimals()
      ]);

      return {
        address,
        symbol,
        name,
        decimals: Number(decimals)
      };
    } catch (error) {
      throw new DEXError(`Failed to get token info: ${error.message}`, this.protocol, this.network);
    }
  }

  // 获取代币余额
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    try {
      if (tokenAddress === ethers.ZeroAddress) {
        // 原生代币余额
        const balance = await this.provider.getBalance(walletAddress);
        return balance.toString();
      } else {
        // ERC20代币余额
        const tokenContract = new ethers.Contract(tokenAddress, this.getERC20ABI(), this.provider);
        const balance = await tokenContract.balanceOf(walletAddress);
        return balance.toString();
      }
    } catch (error) {
      throw new DEXError(`Failed to get token balance: ${error.message}`, this.protocol, this.network);
    }
  }

  // 获取交易对信息
  async getPair(tokenA: string, tokenB: string): Promise<DEXPair | null> {
    try {
      const pairAddress = await this.factoryContract.getPair(tokenA, tokenB);
      
      if (pairAddress === ethers.ZeroAddress) {
        return null;
      }

      const pairContract = new ethers.Contract(pairAddress, this.getPairABI(), this.provider);
      const [token0Address, token1Address, reserves] = await Promise.all([
        pairContract.token0(),
        pairContract.token1(),
        pairContract.getReserves()
      ]);

      const [token0, token1] = await Promise.all([
        this.getToken(token0Address),
        this.getToken(token1Address)
      ]);

      return {
        address: pairAddress,
        token0,
        token1,
        reserve0: reserves[0].toString(),
        reserve1: reserves[1].toString()
      };
    } catch (error) {
      throw new DEXError(`Failed to get pair info: ${error.message}`, this.protocol, this.network);
    }
  }

  // 获取储备量
  async getReserves(pairAddress: string): Promise<{ reserve0: string; reserve1: string }> {
    try {
      const pairContract = new ethers.Contract(pairAddress, this.getPairABI(), this.provider);
      const reserves = await pairContract.getReserves();
      
      return {
        reserve0: reserves[0].toString(),
        reserve1: reserves[1].toString()
      };
    } catch (error) {
      throw new DEXError(`Failed to get reserves: ${error.message}`, this.protocol, this.network);
    }
  }

  // 获取价格
  async getPrice(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXPrice> {
    try {
      const route = await this.getRoute(tokenIn, tokenOut, amountIn);
      
      return {
        tokenIn,
        tokenOut,
        amountIn,
        amountOut: route.amountOut,
        priceImpact: route.priceImpact,
        route
      };
    } catch (error) {
      throw new DEXError(`Failed to get price: ${error.message}`, this.protocol, this.network);
    }
  }

  // 执行交换
  async swap(params: DEXSwapParams, wallet: WalletConfig): Promise<DEXSwapResult> {
    try {
      const signer = new ethers.Wallet(wallet.privateKey, this.provider);
      const routerWithSigner = this.routerContract.connect(signer);

      // 构建交易路径
      const path = [params.tokenIn, params.tokenOut];

      // 估算Gas
      const gasEstimate = await routerWithSigner.swapExactTokensForTokens.estimateGas(
        params.amountIn,
        params.amountOutMin,
        path,
        params.to,
        params.deadline
      );

      // 执行交易
      const tx = await routerWithSigner.swapExactTokensForTokens(
        params.amountIn,
        params.amountOutMin,
        path,
        params.to,
        params.deadline,
        {
          gasLimit: gasEstimate,
          gasPrice: this.config.gasPrice
        }
      );

      const receipt = await tx.wait();

      return {
        txHash: receipt.hash,
        amountIn: params.amountIn,
        amountOut: params.amountOutMin, // 实际输出需要从事件中解析
        gasUsed: receipt.gasUsed.toString(),
        gasPrice: receipt.gasPrice?.toString() || '0',
        fee: (receipt.gasUsed * (receipt.gasPrice || 0n)).toString()
      };
    } catch (error) {
      throw new SwapError(`Swap failed: ${error.message}`, this.protocol, this.network);
    }
  }

  // 精确代币交换
  async swapExactTokensForTokens(
    amountIn: string,
    amountOutMin: string,
    path: string[],
    to: string,
    deadline: number,
    wallet: WalletConfig
  ): Promise<DEXSwapResult> {
    return this.swap({
      tokenIn: path[0],
      tokenOut: path[path.length - 1],
      amountIn,
      amountOutMin,
      to,
      deadline,
      slippage: this.config.slippage || 0.5
    }, wallet);
  }

  // 添加流动性
  async addLiquidity(params: DEXLiquidityParams, wallet: WalletConfig): Promise<DEXLiquidityResult> {
    try {
      const signer = new ethers.Wallet(wallet.privateKey, this.provider);
      const routerWithSigner = this.routerContract.connect(signer);

      const tx = await routerWithSigner.addLiquidity(
        params.tokenA,
        params.tokenB,
        params.amountADesired,
        params.amountBDesired,
        params.amountAMin,
        params.amountBMin,
        params.to,
        params.deadline
      );

      const receipt = await tx.wait();

      return {
        txHash: receipt.hash,
        amountA: params.amountADesired,
        amountB: params.amountBDesired,
        liquidity: '0', // 需要从事件中解析
        gasUsed: receipt.gasUsed.toString(),
        gasPrice: receipt.gasPrice?.toString() || '0'
      };
    } catch (error) {
      throw new DEXError(`Add liquidity failed: ${error.message}`, this.protocol, this.network);
    }
  }

  // 移除流动性
  async removeLiquidity(
    tokenA: string,
    tokenB: string,
    liquidity: string,
    amountAMin: string,
    amountBMin: string,
    to: string,
    deadline: number,
    wallet: WalletConfig
  ): Promise<DEXLiquidityResult> {
    try {
      const signer = new ethers.Wallet(wallet.privateKey, this.provider);
      const routerWithSigner = this.routerContract.connect(signer);

      const tx = await routerWithSigner.removeLiquidity(
        tokenA,
        tokenB,
        liquidity,
        amountAMin,
        amountBMin,
        to,
        deadline
      );

      const receipt = await tx.wait();

      return {
        txHash: receipt.hash,
        amountA: amountAMin,
        amountB: amountBMin,
        liquidity,
        gasUsed: receipt.gasUsed.toString(),
        gasPrice: receipt.gasPrice?.toString() || '0'
      };
    } catch (error) {
      throw new DEXError(`Remove liquidity failed: ${error.message}`, this.protocol, this.network);
    }
  }

  // 格式化单位
  formatUnits(value: string, decimals: number): string {
    return ethers.formatUnits(value, decimals);
  }

  // 解析单位
  parseUnits(value: string, decimals: number): string {
    return ethers.parseUnits(value, decimals).toString();
  }

  // 计算滑点
  calculateSlippage(amountOut: string, slippage: number): string {
    const slippageAmount = (BigInt(amountOut) * BigInt(Math.floor(slippage * 100))) / 10000n;
    return (BigInt(amountOut) - slippageAmount).toString();
  }

  // 估算Gas费用
  async estimateGas(transaction: any): Promise<string> {
    try {
      const gasEstimate = await this.provider.estimateGas(transaction);
      return gasEstimate.toString();
    } catch (error) {
      throw new GasError(`Gas estimation failed: ${error.message}`, this.protocol, this.network);
    }
  }

  // 抽象方法，子类需要实现
  abstract getTokens(): Promise<DEXToken[]>;
  abstract getPairs(): Promise<DEXPair[]>;
  abstract getRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
  abstract getBestRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
}

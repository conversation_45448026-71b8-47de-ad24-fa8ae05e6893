import { BlockchainNetwork, DEXProtocol } from '../../../shared/types';

// DEX 交易对信息
export interface DEXPair {
  address: string;
  token0: DEXToken;
  token1: DEXToken;
  fee?: number;
  liquidity?: string;
  reserve0?: string;
  reserve1?: string;
}

// 代币信息
export interface DEXToken {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
}

// 交易路径
export interface DEXRoute {
  path: string[];
  pairs: DEXPair[];
  amountIn: string;
  amountOut: string;
  priceImpact: number;
  fee: number;
}

// 交易参数
export interface DEXSwapParams {
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  amountOutMin: string;
  to: string;
  deadline: number;
  slippage: number;
}

// 流动性参数
export interface DEXLiquidityParams {
  tokenA: string;
  tokenB: string;
  amountADesired: string;
  amountBDesired: string;
  amountAMin: string;
  amountBMin: string;
  to: string;
  deadline: number;
}

// 交易结果
export interface DEXSwapResult {
  txHash: string;
  amountIn: string;
  amountOut: string;
  gasUsed: string;
  gasPrice: string;
  fee: string;
}

// 流动性结果
export interface DEXLiquidityResult {
  txHash: string;
  amountA: string;
  amountB: string;
  liquidity: string;
  gasUsed: string;
  gasPrice: string;
}

// 价格信息
export interface DEXPrice {
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  amountOut: string;
  priceImpact: number;
  route: DEXRoute;
}

// 钱包配置
export interface WalletConfig {
  privateKey: string;
  address: string;
}

// DEX 配置
export interface DEXConfig {
  network: BlockchainNetwork;
  rpcUrl: string;
  routerAddress: string;
  factoryAddress: string;
  gasLimit?: number;
  gasPrice?: string;
  slippage?: number;
}

// DEX 接口
export interface IDEX {
  readonly protocol: DEXProtocol;
  readonly network: BlockchainNetwork;
  readonly isConnected: boolean;

  // 初始化和连接
  initialize(config: DEXConfig): Promise<void>;
  connect(): Promise<void>;
  disconnect(): Promise<void>;

  // 代币信息
  getToken(address: string): Promise<DEXToken>;
  getTokens(): Promise<DEXToken[]>;
  getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string>;

  // 交易对信息
  getPair(tokenA: string, tokenB: string): Promise<DEXPair | null>;
  getPairs(): Promise<DEXPair[]>;
  getReserves(pairAddress: string): Promise<{ reserve0: string; reserve1: string }>;

  // 价格和路由
  getPrice(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXPrice>;
  getRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;
  getBestRoute(tokenIn: string, tokenOut: string, amountIn: string): Promise<DEXRoute>;

  // 交易操作
  swap(params: DEXSwapParams, wallet: WalletConfig): Promise<DEXSwapResult>;
  swapExactTokensForTokens(
    amountIn: string,
    amountOutMin: string,
    path: string[],
    to: string,
    deadline: number,
    wallet: WalletConfig
  ): Promise<DEXSwapResult>;

  // 流动性操作
  addLiquidity(params: DEXLiquidityParams, wallet: WalletConfig): Promise<DEXLiquidityResult>;
  removeLiquidity(
    tokenA: string,
    tokenB: string,
    liquidity: string,
    amountAMin: string,
    amountBMin: string,
    to: string,
    deadline: number,
    wallet: WalletConfig
  ): Promise<DEXLiquidityResult>;

  // 工具方法
  formatUnits(value: string, decimals: number): string;
  parseUnits(value: string, decimals: number): string;
  calculateSlippage(amountOut: string, slippage: number): string;
  estimateGas(transaction: any): Promise<string>;

  // 事件监听
  onSwap?(callback: (event: any) => void): void;
  onLiquidityAdd?(callback: (event: any) => void): void;
  onLiquidityRemove?(callback: (event: any) => void): void;
}

// DEX 错误类
export class DEXError extends Error {
  constructor(
    message: string,
    public protocol: DEXProtocol,
    public network: BlockchainNetwork,
    public code?: string
  ) {
    super(message);
    this.name = 'DEXError';
  }
}

// 网络错误
export class BlockchainNetworkError extends DEXError {
  constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork) {
    super(message, protocol, network, 'NETWORK_ERROR');
    this.name = 'BlockchainNetworkError';
  }
}

// 交易错误
export class SwapError extends DEXError {
  constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork) {
    super(message, protocol, network, 'SWAP_ERROR');
    this.name = 'SwapError';
  }
}

// 流动性错误
export class LiquidityError extends DEXError {
  constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork) {
    super(message, protocol, network, 'LIQUIDITY_ERROR');
    this.name = 'LiquidityError';
  }
}

// 余额不足错误
export class InsufficientBalanceError extends DEXError {
  constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork) {
    super(message, protocol, network, 'INSUFFICIENT_BALANCE');
    this.name = 'InsufficientBalanceError';
  }
}

// 滑点过大错误
export class SlippageError extends DEXError {
  constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork) {
    super(message, protocol, network, 'SLIPPAGE_ERROR');
    this.name = 'SlippageError';
  }
}

// Gas 费用错误
export class GasError extends DEXError {
  constructor(message: string, protocol: DEXProtocol, network: BlockchainNetwork) {
    super(message, protocol, network, 'GAS_ERROR');
    this.name = 'GasError';
  }
}

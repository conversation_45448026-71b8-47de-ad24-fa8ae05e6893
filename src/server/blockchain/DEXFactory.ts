import { BlockchainNetwork, DEXProtocol } from '../../shared/types';
import { IDEX, DEXConfig } from './base/DEXInterface';
import { UniswapV2 } from './uniswap/UniswapV2';
import { PancakeSwap } from './pancakeswap/PancakeSwap';
// 其他DEX的导入将在后续添加
// import { UniswapV3 } from './uniswap/UniswapV3';
// import { Raydium } from './raydium/Raydium';
// import { Jupiter } from './jupiter/Jupiter';

export class DEXFactory {
  private static instances: Map<string, IDEX> = new Map();

  /**
   * 创建DEX实例
   * @param protocol DEX协议
   * @param network 区块链网络
   * @param config 配置信息
   * @returns DEX实例
   */
  static async createDEX(
    protocol: DEXProtocol,
    network: BlockchainNetwork,
    config: DEXConfig
  ): Promise<IDEX> {
    const key = `${protocol}_${network}`;
    
    // 检查是否已存在实例
    if (this.instances.has(key)) {
      const instance = this.instances.get(key)!;
      if (instance.isConnected) {
        return instance;
      }
    }

    // 创建新实例
    let dexInstance: IDEX;

    switch (protocol) {
      case DEXProtocol.UNISWAP_V2:
        if (network === BlockchainNetwork.ETH) {
          dexInstance = new UniswapV2(network);
        } else {
          throw new Error(`Uniswap V2 is not supported on ${network}`);
        }
        break;
      
      // case DEXProtocol.UNISWAP_V3:
      //   if ([BlockchainNetwork.ETH, BlockchainNetwork.BASE].includes(network)) {
      //     dexInstance = new UniswapV3(network);
      //   } else {
      //     throw new Error(`Uniswap V3 is not supported on ${network}`);
      //   }
      //   break;
      
      case DEXProtocol.PANCAKESWAP:
        if (network === BlockchainNetwork.BSC) {
          dexInstance = new PancakeSwap();
        } else {
          throw new Error(`PancakeSwap is not supported on ${network}`);
        }
        break;
      
      // case DEXProtocol.RAYDIUM:
      //   if (network === BlockchainNetwork.SOLANA) {
      //     dexInstance = new Raydium();
      //   } else {
      //     throw new Error(`Raydium is not supported on ${network}`);
      //   }
      //   break;
      
      // case DEXProtocol.JUPITER:
      //   if (network === BlockchainNetwork.SOLANA) {
      //     dexInstance = new Jupiter();
      //   } else {
      //     throw new Error(`Jupiter is not supported on ${network}`);
      //   }
      //   break;

      default:
        throw new Error(`Unsupported DEX protocol: ${protocol}`);
    }

    // 初始化并连接
    await dexInstance.initialize(config);
    await dexInstance.connect();

    // 缓存实例
    this.instances.set(key, dexInstance);

    return dexInstance;
  }

  /**
   * 获取已存在的DEX实例
   * @param protocol DEX协议
   * @param network 区块链网络
   * @returns DEX实例或null
   */
  static getDEX(protocol: DEXProtocol, network: BlockchainNetwork): IDEX | null {
    const key = `${protocol}_${network}`;
    return this.instances.get(key) || null;
  }

  /**
   * 移除DEX实例
   * @param protocol DEX协议
   * @param network 区块链网络
   */
  static async removeDEX(protocol: DEXProtocol, network: BlockchainNetwork): Promise<void> {
    const key = `${protocol}_${network}`;
    const instance = this.instances.get(key);
    
    if (instance) {
      await instance.disconnect();
      this.instances.delete(key);
    }
  }

  /**
   * 获取所有活跃的DEX实例
   * @returns 活跃的DEX实例数组
   */
  static getActiveDEXes(): IDEX[] {
    return Array.from(this.instances.values()).filter(dex => dex.isConnected);
  }

  /**
   * 断开所有DEX连接
   */
  static async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.instances.values()).map(dex => 
      dex.disconnect()
    );
    
    await Promise.all(disconnectPromises);
    this.instances.clear();
  }

  /**
   * 获取支持的DEX协议列表
   * @param network 区块链网络
   * @returns 支持的DEX协议数组
   */
  static getSupportedProtocols(network: BlockchainNetwork): DEXProtocol[] {
    const supportedProtocols: Record<BlockchainNetwork, DEXProtocol[]> = {
      [BlockchainNetwork.ETH]: [
        DEXProtocol.UNISWAP_V2,
        // DEXProtocol.UNISWAP_V3
      ],
      [BlockchainNetwork.BSC]: [
        DEXProtocol.PANCAKESWAP,
        DEXProtocol.UNISWAP_V2 // PancakeSwap 基于 Uniswap V2
      ],
      [BlockchainNetwork.BASE]: [
        // DEXProtocol.UNISWAP_V3
      ],
      [BlockchainNetwork.SOLANA]: [
        // DEXProtocol.RAYDIUM,
        // DEXProtocol.JUPITER
      ]
    };

    return supportedProtocols[network] || [];
  }

  /**
   * 检查协议是否在指定网络上受支持
   * @param protocol DEX协议
   * @param network 区块链网络
   * @returns 是否支持
   */
  static isSupported(protocol: DEXProtocol, network: BlockchainNetwork): boolean {
    return this.getSupportedProtocols(network).includes(protocol);
  }

  /**
   * 获取DEX的默认配置
   * @param protocol DEX协议
   * @param network 区块链网络
   * @returns 默认配置
   */
  static getDefaultConfig(protocol: DEXProtocol, network: BlockchainNetwork): Partial<DEXConfig> {
    const configs: Record<string, Partial<DEXConfig>> = {
      [`${DEXProtocol.UNISWAP_V2}_${BlockchainNetwork.ETH}`]: {
        routerAddress: '******************************************',
        factoryAddress: '******************************************',
        gasLimit: 300000,
        slippage: 0.5
      },
      [`${DEXProtocol.PANCAKESWAP}_${BlockchainNetwork.BSC}`]: {
        routerAddress: '******************************************',
        factoryAddress: '******************************************',
        gasLimit: 300000,
        slippage: 0.5
      },
      // [`${DEXProtocol.UNISWAP_V3}_${BlockchainNetwork.ETH}`]: {
      //   routerAddress: '******************************************',
      //   factoryAddress: '******************************************',
      //   gasLimit: 500000,
      //   slippage: 0.5
      // },
      // [`${DEXProtocol.UNISWAP_V3}_${BlockchainNetwork.BASE}`]: {
      //   routerAddress: '******************************************',
      //   factoryAddress: '******************************************',
      //   gasLimit: 500000,
      //   slippage: 0.5
      // }
    };

    const key = `${protocol}_${network}`;
    return configs[key] || {};
  }

  /**
   * 获取网络的默认RPC URL
   * @param network 区块链网络
   * @returns RPC URL
   */
  static getDefaultRPCUrl(network: BlockchainNetwork): string {
    const rpcUrls: Record<BlockchainNetwork, string> = {
      [BlockchainNetwork.ETH]: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
      [BlockchainNetwork.BSC]: 'https://bsc-dataseed.binance.org/',
      [BlockchainNetwork.BASE]: 'https://mainnet.base.org',
      [BlockchainNetwork.SOLANA]: 'https://api.mainnet-beta.solana.com'
    };

    return rpcUrls[network] || '';
  }

  /**
   * 验证DEX配置
   * @param protocol DEX协议
   * @param network 区块链网络
   * @param config 配置信息
   * @returns 验证结果
   */
  static validateConfig(
    protocol: DEXProtocol,
    network: BlockchainNetwork,
    config: DEXConfig
  ): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 基础验证
    if (!config.rpcUrl) {
      errors.push('RPC URL is required');
    }

    if (!config.routerAddress) {
      errors.push('Router address is required');
    }

    if (!config.factoryAddress) {
      errors.push('Factory address is required');
    }

    // 网络特定验证
    if (!this.isSupported(protocol, network)) {
      errors.push(`${protocol} is not supported on ${network}`);
    }

    // 地址格式验证（简化版）
    const addressRegex = /^0x[a-fA-F0-9]{40}$/;
    if (config.routerAddress && !addressRegex.test(config.routerAddress)) {
      errors.push('Invalid router address format');
    }

    if (config.factoryAddress && !addressRegex.test(config.factoryAddress)) {
      errors.push('Invalid factory address format');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 测试DEX连接
   * @param protocol DEX协议
   * @param network 区块链网络
   * @param config 配置信息
   * @returns 测试结果
   */
  static async testConnection(
    protocol: DEXProtocol,
    network: BlockchainNetwork,
    config: DEXConfig
  ): Promise<{
    success: boolean;
    message: string;
    blockNumber?: number;
    networkInfo?: any;
  }> {
    try {
      // 验证配置
      const validation = this.validateConfig(protocol, network, config);
      if (!validation.valid) {
        return {
          success: false,
          message: `Configuration error: ${validation.errors.join(', ')}`
        };
      }

      // 创建临时实例进行测试
      const tempInstance = await this.createDEX(protocol, network, config);
      
      // 获取网络信息
      const tokens = await tempInstance.getTokens();
      
      // 测试完成后断开连接
      await tempInstance.disconnect();
      
      return {
        success: true,
        message: 'Connection successful',
        networkInfo: {
          protocol,
          network,
          tokensCount: tokens.length
        }
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 获取系统统计信息
   * @returns 统计信息
   */
  static getSystemStats(): {
    totalConnections: number;
    protocolStats: Record<DEXProtocol, number>;
    networkStats: Record<BlockchainNetwork, number>;
  } {
    let totalConnections = 0;
    const protocolStats: Record<DEXProtocol, number> = {} as any;
    const networkStats: Record<BlockchainNetwork, number> = {} as any;

    // 初始化统计
    Object.values(DEXProtocol).forEach(protocol => {
      protocolStats[protocol] = 0;
    });

    Object.values(BlockchainNetwork).forEach(network => {
      networkStats[network] = 0;
    });

    // 统计连接数
    for (const instance of this.instances.values()) {
      if (instance.isConnected) {
        totalConnections++;
        protocolStats[instance.protocol]++;
        networkStats[instance.network]++;
      }
    }

    return {
      totalConnections,
      protocolStats,
      networkStats
    };
  }
}

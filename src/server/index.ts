import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import path from 'path';
import fs from 'fs';

// 简化的日志函数
const log = {
  info: (msg: string) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg: string, error?: any) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`, error || ''),
  warn: (msg: string) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`)
};

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 3001;

// 确保日志目录存在
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 基础中间件 - 开发环境下完全禁用安全限制
app.use((req, res, next) => {
  // 完全禁用 CSP 以解决 eval 问题
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('X-Content-Security-Policy');
  res.removeHeader('X-WebKit-CSP');

  // 设置开发友好的头部
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');

  next();
});

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
if (process.env.NODE_ENV === 'production') {
  const clientDistPath = path.join(__dirname, '../../client/dist');
  if (fs.existsSync(clientDistPath)) {
    app.use(express.static(clientDistPath));
  }
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

// API 基础路由
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'TradeAI Bot API - Minimal Version',
    version: '1.0.0-minimal',
    endpoints: [
      'GET /health',
      'GET /api',
      'GET /api/test',
      'GET /api/auth',
      'GET /api/exchanges',
      'GET /api/strategies',
      'GET /api/wallets',
      'GET /api/rewards'
    ]
  });
});

// 测试端点
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API test endpoint working!',
    timestamp: new Date().toISOString(),
    server: 'minimal-typescript'
  });
});

// 临时API路由
app.use('/api/auth', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Auth API - Coming soon',
    endpoints: ['POST /login', 'POST /register', 'GET /profile']
  });
});

app.use('/api/exchanges', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Exchanges API - Coming soon',
    supported: ['binance', 'okx', 'htx', 'pancakeswap', 'uniswap']
  });
});

app.use('/api/strategies', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Strategies API - Coming soon',
    types: ['grid', 'trend_following', 'ai', 'dex_grid']
  });
});

app.use('/api/wallets', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Wallets API - Coming soon',
    networks: ['ethereum', 'bsc', 'base', 'solana']
  });
});

app.use('/api/rewards', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Rewards API - Coming soon',
    features: ['trading_rewards', 'referral', 'achievements']
  });
});

app.use('/api/orders', (req, res) => {
  res.json({ success: true, message: 'Orders API - Coming soon' });
});

app.use('/api/trades', (req, res) => {
  res.json({ success: true, message: 'Trades API - Coming soon' });
});

app.use('/api/subscriptions', (req, res) => {
  res.json({ success: true, message: 'Subscriptions API - Coming soon' });
});

// 404 处理
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.path
  });
});

// 生产环境下的前端路由处理
if (process.env.NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    const indexPath = path.join(__dirname, '../../client/dist/index.html');
    if (fs.existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      res.status(404).send('Frontend not built. Please run: npm run build');
    }
  });
}

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  log.error('Unhandled error:', err);
  
  res.status(err.status || 500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// 启动服务器
const startServer = async () => {
  try {
    log.info('Starting TradeAI Bot minimal server...');
    
    server.listen(PORT, () => {
      log.info(`🚀 TradeAI Bot server is running on port ${PORT}`);
      log.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      log.info(`🔗 Health check: http://localhost:${PORT}/health`);
      log.info(`🔗 API Base: http://localhost:${PORT}/api`);
      log.info(`🧪 API Test: http://localhost:${PORT}/api/test`);
      
      if (process.env.NODE_ENV !== 'production') {
        log.info(`🌐 Frontend: http://localhost:5173`);
      }
      
      log.info('✅ Minimal server started successfully!');
    });
  } catch (error) {
    log.error('Failed to start server:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  log.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    log.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  log.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    log.info('Process terminated');
    process.exit(0);
  });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  log.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Rejection at promise:', reason);
  process.exit(1);
});

// 启动服务器
startServer();

export { app, server };

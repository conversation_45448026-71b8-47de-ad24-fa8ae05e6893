import express from 'express';
import { EncryptionUtils } from '../utils/encryption';
import { db } from '../models/database';
import { logger, logUtils } from '../utils/logger';
import { APIResponse, UserRole } from '../../shared/types';

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        isActive: boolean;
        subscriptionExpiry?: Date;
      };
    }
  }
}

// JWT认证中间件
export const authenticateToken = async (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required'
      } as APIResponse);
    }

    // 验证token
    const decoded = EncryptionUtils.verifyJWTToken(token);

    // 从数据库获取最新用户信息
    const result = await db.query(
      'SELECT id, email, role, is_active, subscription_expiry FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      } as APIResponse);
    }

    const user = result.rows[0];

    // 检查用户是否激活
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      } as APIResponse);
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      isActive: user.is_active,
      subscriptionExpiry: user.subscription_expiry
    };

    next();

  } catch (error) {
    if (error.message === 'Token expired') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      } as APIResponse);
    } else if (error.message === 'Invalid token') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      } as APIResponse);
    }

    logger.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
};

// 角色权限检查中间件
export const requireRole = (roles: UserRole[]) => {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      } as APIResponse);
    }

    if (!roles.includes(req.user.role)) {
      logUtils.logSecurityEvent('unauthorized_access', {
        userId: req.user.id,
        requiredRoles: roles,
        userRole: req.user.role,
        endpoint: req.path
      });

      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      } as APIResponse);
    }

    next();
  };
};

// 高级功能权限检查中间件
export const requirePremium = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    } as APIResponse);
    }

  // 检查是否为管理员
  if (req.user.role === UserRole.ADMIN) {
    return next();
  }

  // 检查是否为高级用户
  if (req.user.role === UserRole.PREMIUM) {
    // 检查订阅是否过期
    if (req.user.subscriptionExpiry && new Date() > req.user.subscriptionExpiry) {
      return res.status(403).json({
        success: false,
        message: 'Premium subscription expired'
      } as APIResponse);
    }
    return next();
  }

  return res.status(403).json({
    success: false,
    message: 'Premium subscription required'
  } as APIResponse);
};

// 速率限制中间件
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export const rateLimit = (maxRequests: number, windowMs: number) => {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const key = req.ip + (req.user?.id || 'anonymous');
    const now = Date.now();
    
    // 清理过期的记录
    for (const [k, v] of rateLimitStore.entries()) {
      if (now > v.resetTime) {
        rateLimitStore.delete(k);
      }
    }

    const record = rateLimitStore.get(key);
    
    if (!record) {
      rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (now > record.resetTime) {
      rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (record.count >= maxRequests) {
      logUtils.logSecurityEvent('rate_limit_exceeded', {
        ip: req.ip,
        userId: req.user?.id,
        endpoint: req.path
      });

      return res.status(429).json({
        success: false,
        message: 'Too many requests'
      } as APIResponse);
    }

    record.count++;
    next();
  };
};

// API密钥验证中间件（用于外部API调用）
export const validateApiKey = async (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  try {
    const apiKey = req.headers['x-api-key'] as string;

    if (!apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API key is required'
      } as APIResponse);
    }

    // 这里可以实现API密钥验证逻辑
    // 例如从数据库查询API密钥对应的用户
    
    // 临时实现：检查是否为有效格式
    if (apiKey.length < 32) {
      return res.status(401).json({
        success: false,
        message: 'Invalid API key'
      } as APIResponse);
    }

    next();

  } catch (error) {
    logger.error('API key validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
};

// 请求日志中间件
export const requestLogger = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id
    });

    // 记录慢请求
    if (duration > 1000) {
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.url,
        duration,
        userId: req.user?.id
      });
    }
  });

  next();
};

// 错误处理中间件
export const errorHandler = (
  error: any,
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userId: req.user?.id
  });

  // 不要在生产环境中暴露错误详情
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(error.status || 500).json({
    success: false,
    message: isDevelopment ? error.message : 'Internal server error',
    ...(isDevelopment && { stack: error.stack })
  } as APIResponse);
};

// CORS配置中间件
export const corsConfig = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:5173'
    ];

    // 允许没有origin的请求（如移动应用）
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key']
};

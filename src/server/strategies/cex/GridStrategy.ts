import { BaseStrategy } from '../base/BaseStrategy';
import { ExchangeService } from '../../services/exchangeService';
import { StrategyType, CEXExchange, OrderType } from '../../../shared/types';
import {
  StrategyExecutionResult,
  StrategySignal,
  SignalType,
  ConfigurationError,
  ExecutionError
} from '../base/StrategyInterface';

// 网格交易配置
export interface GridStrategyConfig {
  userId: string;
  exchange: CEXExchange;
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  gridType: 'arithmetic' | 'geometric';
  gridLevels: number;
  upperPrice: number;
  lowerPrice: number;
  totalAmount: number;
  executionInterval: number;
  stopLoss?: number;
  takeProfit?: number;
  rebalanceThreshold: number;
}

// 网格订单
interface GridOrder {
  level: number;
  price: number;
  amount: number;
  side: 'buy' | 'sell';
  orderId?: string;
  filled: boolean;
}

export class GridStrategy extends BaseStrategy {
  private config: GridStrategyConfig;
  private gridOrders: GridOrder[] = [];
  private currentPrice: number = 0;
  private baseBalance: number = 0;
  private quoteBalance: number = 0;

  constructor(id: string, name: string) {
    super(id, name, StrategyType.GRID);
  }

  async initialize(config: GridStrategyConfig): Promise<void> {
    this.config = config;
    
    // 验证配置
    this.validateConfig();
    
    // 初始化网格
    await this.initializeGrid();
    
    // 获取当前余额
    await this.updateBalances();
  }

  async validate(): Promise<boolean> {
    try {
      // 检查配置
      if (!this.config) {
        throw new ConfigurationError('Strategy not configured', this.id, this.type);
      }

      // 检查交易所连接
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      if (!exchange.isConnected) {
        throw new ConfigurationError('Exchange not connected', this.id, this.type);
      }

      // 检查余额
      await this.updateBalances();
      const requiredQuoteAmount = this.calculateRequiredQuoteAmount();
      if (this.quoteBalance < requiredQuoteAmount) {
        throw new ConfigurationError('Insufficient quote balance', this.id, this.type);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  async execute(): Promise<StrategyExecutionResult> {
    try {
      // 更新当前价格
      await this.updateCurrentPrice();
      
      // 检查并执行网格订单
      const result = await this.executeGridLogic();
      
      return result;
    } catch (error) {
      throw new ExecutionError(`Grid execution failed: ${error.message}`, this.id, this.type);
    }
  }

  protected async generateSignal(): Promise<StrategySignal | null> {
    // 网格策略不依赖传统的买卖信号，而是基于价格网格
    return null;
  }

  protected async executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult> {
    // 网格策略不使用传统信号执行
    return {
      success: false,
      message: 'Grid strategy does not use traditional signals',
      timestamp: new Date()
    };
  }

  // 验证配置
  private validateConfig(): void {
    if (!this.config.symbol || !this.config.baseAsset || !this.config.quoteAsset) {
      throw new ConfigurationError('Missing symbol or asset configuration', this.id, this.type);
    }

    if (this.config.upperPrice <= this.config.lowerPrice) {
      throw new ConfigurationError('Upper price must be greater than lower price', this.id, this.type);
    }

    if (this.config.gridLevels < 2) {
      throw new ConfigurationError('Grid levels must be at least 2', this.id, this.type);
    }

    if (this.config.totalAmount <= 0) {
      throw new ConfigurationError('Total amount must be positive', this.id, this.type);
    }
  }

  // 初始化网格
  private async initializeGrid(): Promise<void> {
    this.gridOrders = [];
    
    const priceStep = this.calculatePriceStep();
    const amountPerLevel = this.config.totalAmount / this.config.gridLevels;

    for (let i = 0; i < this.config.gridLevels; i++) {
      const price = this.config.lowerPrice + (priceStep * i);
      
      // 买单（在当前价格下方）
      this.gridOrders.push({
        level: i,
        price: price,
        amount: amountPerLevel,
        side: 'buy',
        filled: false
      });

      // 卖单（在当前价格上方）
      if (i > 0) {
        this.gridOrders.push({
          level: i,
          price: price,
          amount: amountPerLevel,
          side: 'sell',
          filled: false
        });
      }
    }
  }

  // 计算价格步长
  private calculatePriceStep(): number {
    if (this.config.gridType === 'arithmetic') {
      return (this.config.upperPrice - this.config.lowerPrice) / (this.config.gridLevels - 1);
    } else {
      // 几何网格
      const ratio = Math.pow(this.config.upperPrice / this.config.lowerPrice, 1 / (this.config.gridLevels - 1));
      return this.config.lowerPrice * (ratio - 1);
    }
  }

  // 执行网格逻辑
  private async executeGridLogic(): Promise<StrategyExecutionResult> {
    let totalProfit = 0;
    let executedOrders = 0;

    // 检查需要执行的订单
    for (const gridOrder of this.gridOrders) {
      if (gridOrder.filled) continue;

      const shouldExecute = this.shouldExecuteOrder(gridOrder);
      if (shouldExecute) {
        try {
          const result = await this.executeGridOrder(gridOrder);
          if (result.success) {
            gridOrder.filled = true;
            gridOrder.orderId = result.orderId;
            totalProfit += result.profit || 0;
            executedOrders++;

            // 创建对应的反向订单
            await this.createCounterOrder(gridOrder);
          }
        } catch (error) {
          // 记录错误但继续执行其他订单
          console.error(`Failed to execute grid order at level ${gridOrder.level}:`, error);
        }
      }
    }

    // 检查是否需要重新平衡网格
    if (this.shouldRebalanceGrid()) {
      await this.rebalanceGrid();
    }

    return {
      success: executedOrders > 0,
      message: `Executed ${executedOrders} grid orders`,
      profit: totalProfit,
      timestamp: new Date()
    };
  }

  // 判断是否应该执行订单
  private shouldExecuteOrder(gridOrder: GridOrder): boolean {
    if (gridOrder.side === 'buy') {
      return this.currentPrice <= gridOrder.price;
    } else {
      return this.currentPrice >= gridOrder.price;
    }
  }

  // 执行网格订单
  private async executeGridOrder(gridOrder: GridOrder): Promise<StrategyExecutionResult> {
    try {
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      
      const order = await exchange.createOrder({
        symbol: this.config.symbol,
        type: OrderType.MARKET,
        side: gridOrder.side,
        amount: gridOrder.amount
      });

      return {
        success: true,
        message: `Grid order executed at level ${gridOrder.level}`,
        orderId: order.id,
        amount: gridOrder.amount,
        price: gridOrder.price,
        timestamp: new Date()
      };
    } catch (error) {
      throw new ExecutionError(`Failed to execute grid order: ${error.message}`, this.id, this.type);
    }
  }

  // 创建反向订单
  private async createCounterOrder(executedOrder: GridOrder): Promise<void> {
    const counterSide = executedOrder.side === 'buy' ? 'sell' : 'buy';
    const counterPrice = executedOrder.side === 'buy' 
      ? executedOrder.price * (1 + this.config.rebalanceThreshold)
      : executedOrder.price * (1 - this.config.rebalanceThreshold);

    const counterOrder: GridOrder = {
      level: executedOrder.level,
      price: counterPrice,
      amount: executedOrder.amount,
      side: counterSide,
      filled: false
    };

    this.gridOrders.push(counterOrder);
  }

  // 判断是否需要重新平衡网格
  private shouldRebalanceGrid(): boolean {
    const filledOrders = this.gridOrders.filter(order => order.filled).length;
    const totalOrders = this.gridOrders.length;
    
    return filledOrders / totalOrders > 0.8; // 80%的订单被执行时重新平衡
  }

  // 重新平衡网格
  private async rebalanceGrid(): Promise<void> {
    // 取消所有未成交的订单
    await this.cancelAllPendingOrders();
    
    // 重新初始化网格
    await this.initializeGrid();
    
    // 根据当前价格调整网格中心
    await this.adjustGridCenter();
  }

  // 取消所有待成交订单
  private async cancelAllPendingOrders(): Promise<void> {
    const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
    
    for (const gridOrder of this.gridOrders) {
      if (gridOrder.orderId && !gridOrder.filled) {
        try {
          await exchange.cancelOrder(gridOrder.orderId, this.config.symbol);
        } catch (error) {
          // 忽略取消失败的订单（可能已经成交）
        }
      }
    }
  }

  // 调整网格中心
  private async adjustGridCenter(): Promise<void> {
    const priceRange = this.config.upperPrice - this.config.lowerPrice;
    const newLowerPrice = this.currentPrice - (priceRange / 2);
    const newUpperPrice = this.currentPrice + (priceRange / 2);
    
    this.config.lowerPrice = Math.max(newLowerPrice, this.currentPrice * 0.5); // 防止价格过低
    this.config.upperPrice = Math.min(newUpperPrice, this.currentPrice * 2); // 防止价格过高
  }

  // 更新当前价格
  private async updateCurrentPrice(): Promise<void> {
    try {
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      const ticker = await exchange.getTicker(this.config.symbol);
      this.currentPrice = ticker.last;
    } catch (error) {
      throw new ExecutionError(`Failed to update current price: ${error.message}`, this.id, this.type);
    }
  }

  // 更新余额
  private async updateBalances(): Promise<void> {
    try {
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      
      const [baseBalance, quoteBalance] = await Promise.all([
        exchange.getBalance(this.config.baseAsset),
        exchange.getBalance(this.config.quoteAsset)
      ]);
      
      this.baseBalance = baseBalance.free;
      this.quoteBalance = quoteBalance.free;
    } catch (error) {
      throw new ExecutionError(`Failed to update balances: ${error.message}`, this.id, this.type);
    }
  }

  // 计算所需的报价资产数量
  private calculateRequiredQuoteAmount(): number {
    const buyOrders = this.gridOrders.filter(order => order.side === 'buy');
    return buyOrders.reduce((total, order) => total + (order.price * order.amount), 0);
  }

  // 获取网格状态
  public getGridStatus(): {
    totalOrders: number;
    filledOrders: number;
    pendingOrders: number;
    currentPrice: number;
    gridRange: { lower: number; upper: number };
    profitLoss: number;
  } {
    const filledOrders = this.gridOrders.filter(order => order.filled).length;
    const pendingOrders = this.gridOrders.length - filledOrders;
    
    return {
      totalOrders: this.gridOrders.length,
      filledOrders,
      pendingOrders,
      currentPrice: this.currentPrice,
      gridRange: {
        lower: this.config.lowerPrice,
        upper: this.config.upperPrice
      },
      profitLoss: this.trades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
    };
  }
}

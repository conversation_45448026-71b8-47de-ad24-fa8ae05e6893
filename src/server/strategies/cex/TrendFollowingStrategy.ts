import { BaseStrategy } from '../base/BaseStrategy';
import { ExchangeService } from '../../services/exchangeService';
import { StrategyType, CEXExchange, OrderType } from '../../../shared/types';
import {
  StrategyExecutionResult,
  StrategySignal,
  SignalType,
  ConfigurationError,
  ExecutionError
} from '../base/StrategyInterface';

// 趋势跟踪配置
export interface TrendFollowingConfig {
  userId: string;
  exchange: CEXExchange;
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d';
  maType: 'SMA' | 'EMA' | 'WMA';
  fastPeriod: number;
  slowPeriod: number;
  signalPeriod: number;
  positionSize: number;
  stopLossPercent: number;
  takeProfitPercent: number;
  trailingStopPercent: number;
  minTrendStrength: number;
  executionInterval: number;
}

// 技术指标数据
interface TechnicalIndicators {
  fastMA: number;
  slowMA: number;
  signalMA: number;
  rsi: number;
  macd: number;
  macdSignal: number;
  macdHistogram: number;
  atr: number;
  trendStrength: number;
}

// 持仓信息
interface Position {
  symbol: string;
  side: 'long' | 'short';
  entryPrice: number;
  quantity: number;
  stopLoss: number;
  takeProfit: number;
  trailingStop: number;
  unrealizedPnl: number;
  timestamp: Date;
}

export class TrendFollowingStrategy extends BaseStrategy {
  private config: TrendFollowingConfig;
  private priceHistory: number[] = [];
  private indicators: TechnicalIndicators | null = null;
  private currentPosition: Position | null = null;
  private currentPrice: number = 0;

  constructor(id: string, name: string) {
    super(id, name, StrategyType.TREND_FOLLOWING);
  }

  async initialize(config: TrendFollowingConfig): Promise<void> {
    this.config = config;
    
    // 验证配置
    this.validateConfig();
    
    // 初始化价格历史
    await this.initializePriceHistory();
  }

  async validate(): Promise<boolean> {
    try {
      // 检查配置
      if (!this.config) {
        throw new ConfigurationError('Strategy not configured', this.id, this.type);
      }

      // 检查交易所连接
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      if (!exchange.isConnected) {
        throw new ConfigurationError('Exchange not connected', this.id, this.type);
      }

      // 检查余额
      const balance = await exchange.getBalance(this.config.quoteAsset);
      const requiredAmount = this.config.positionSize;
      if (balance.free < requiredAmount) {
        throw new ConfigurationError('Insufficient balance', this.id, this.type);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  async execute(): Promise<StrategyExecutionResult> {
    try {
      // 更新价格数据
      await this.updatePriceData();
      
      // 计算技术指标
      this.calculateIndicators();
      
      // 生成交易信号
      const signal = await this.generateSignal();
      
      if (signal) {
        return await this.executeSignal(signal);
      }
      
      // 管理现有持仓
      if (this.currentPosition) {
        return await this.managePosition();
      }

      return {
        success: true,
        message: 'No signal generated',
        timestamp: new Date()
      };
    } catch (error) {
      throw new ExecutionError(`Trend following execution failed: ${error.message}`, this.id, this.type);
    }
  }

  protected async generateSignal(): Promise<StrategySignal | null> {
    if (!this.indicators || this.priceHistory.length < this.config.slowPeriod) {
      return null;
    }

    const { fastMA, slowMA, rsi, macd, macdSignal, trendStrength } = this.indicators;
    
    // 检查趋势强度
    if (trendStrength < this.config.minTrendStrength) {
      return null;
    }

    let signalType: SignalType | null = null;
    let confidence = 0;

    // 多头信号条件
    if (fastMA > slowMA && macd > macdSignal && rsi < 70 && !this.currentPosition) {
      signalType = SignalType.BUY;
      confidence = this.calculateSignalConfidence('buy');
    }
    // 空头信号条件
    else if (fastMA < slowMA && macd < macdSignal && rsi > 30 && !this.currentPosition) {
      signalType = SignalType.SELL;
      confidence = this.calculateSignalConfidence('sell');
    }
    // 平仓信号
    else if (this.currentPosition) {
      if (this.shouldClosePosition()) {
        signalType = SignalType.CLOSE;
        confidence = 0.8;
      }
    }

    if (signalType && confidence > 0.6) {
      return {
        type: signalType,
        symbol: this.config.symbol,
        price: this.currentPrice,
        amount: this.calculatePositionSize({ type: signalType } as any),
        confidence,
        timestamp: new Date()
      };
    }

    return null;
  }

  protected async executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult> {
    try {
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);

      if (signal.type === SignalType.CLOSE && this.currentPosition) {
        // 平仓
        const order = await exchange.createOrder({
          symbol: this.config.symbol,
          type: OrderType.MARKET,
          side: this.currentPosition.side === 'long' ? 'sell' : 'buy',
          amount: this.currentPosition.quantity
        });

        const profit = this.calculatePositionProfit();
        this.currentPosition = null;

        return {
          success: true,
          message: 'Position closed',
          orderId: order.id,
          amount: signal.amount,
          price: signal.price,
          profit,
          timestamp: new Date()
        };
      } else if (signal.type === SignalType.BUY || signal.type === SignalType.SELL) {
        // 开仓
        const side = signal.type === SignalType.BUY ? 'buy' : 'sell';
        
        const order = await exchange.createOrder({
          symbol: this.config.symbol,
          type: OrderType.MARKET,
          side,
          amount: signal.amount
        });

        // 创建持仓记录
        this.currentPosition = {
          symbol: this.config.symbol,
          side: signal.type === SignalType.BUY ? 'long' : 'short',
          entryPrice: signal.price,
          quantity: signal.amount,
          stopLoss: this.calculateStopLoss(signal.price, signal.type === SignalType.BUY ? 'long' : 'short'),
          takeProfit: this.calculateTakeProfit(signal.price, signal.type === SignalType.BUY ? 'long' : 'short'),
          trailingStop: this.calculateTrailingStop(signal.price, signal.type === SignalType.BUY ? 'long' : 'short'),
          unrealizedPnl: 0,
          timestamp: new Date()
        };

        return {
          success: true,
          message: `Position opened: ${side}`,
          orderId: order.id,
          amount: signal.amount,
          price: signal.price,
          timestamp: new Date()
        };
      }

      return {
        success: false,
        message: 'Invalid signal type',
        timestamp: new Date()
      };
    } catch (error) {
      throw new ExecutionError(`Failed to execute signal: ${error.message}`, this.id, this.type);
    }
  }

  // 验证配置
  private validateConfig(): void {
    if (!this.config.symbol || !this.config.baseAsset || !this.config.quoteAsset) {
      throw new ConfigurationError('Missing symbol or asset configuration', this.id, this.type);
    }

    if (this.config.fastPeriod >= this.config.slowPeriod) {
      throw new ConfigurationError('Fast period must be less than slow period', this.id, this.type);
    }

    if (this.config.positionSize <= 0) {
      throw new ConfigurationError('Position size must be positive', this.id, this.type);
    }

    if (this.config.stopLossPercent <= 0 || this.config.stopLossPercent >= 100) {
      throw new ConfigurationError('Stop loss percent must be between 0 and 100', this.id, this.type);
    }
  }

  // 初始化价格历史
  private async initializePriceHistory(): Promise<void> {
    try {
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      const klines = await exchange.getKlines(
        this.config.symbol,
        this.config.timeframe,
        this.config.slowPeriod + 50 // 获取足够的历史数据
      );

      this.priceHistory = klines.map(kline => kline.close);
    } catch (error) {
      throw new ConfigurationError(`Failed to initialize price history: ${error.message}`, this.id, this.type);
    }
  }

  // 更新价格数据
  private async updatePriceData(): Promise<void> {
    try {
      const exchange = await ExchangeService.getUserExchange(this.config.userId, this.config.exchange);
      const ticker = await exchange.getTicker(this.config.symbol);
      
      this.currentPrice = ticker.last;
      this.priceHistory.push(this.currentPrice);
      
      // 保持历史数据长度
      const maxLength = this.config.slowPeriod + 100;
      if (this.priceHistory.length > maxLength) {
        this.priceHistory = this.priceHistory.slice(-maxLength);
      }
    } catch (error) {
      throw new ExecutionError(`Failed to update price data: ${error.message}`, this.id, this.type);
    }
  }

  // 计算技术指标
  private calculateIndicators(): void {
    if (this.priceHistory.length < this.config.slowPeriod) {
      return;
    }

    const prices = this.priceHistory;
    
    this.indicators = {
      fastMA: this.calculateMA(prices, this.config.fastPeriod, this.config.maType),
      slowMA: this.calculateMA(prices, this.config.slowPeriod, this.config.maType),
      signalMA: this.calculateMA(prices, this.config.signalPeriod, this.config.maType),
      rsi: this.calculateRSI(prices, 14),
      macd: 0, // 简化实现
      macdSignal: 0,
      macdHistogram: 0,
      atr: this.calculateATR(prices, 14),
      trendStrength: this.calculateTrendStrength(prices)
    };
  }

  // 计算移动平均线
  private calculateMA(prices: number[], period: number, type: string): number {
    if (prices.length < period) return 0;
    
    const recentPrices = prices.slice(-period);
    
    switch (type) {
      case 'SMA':
        return recentPrices.reduce((sum, price) => sum + price, 0) / period;
      case 'EMA':
        return this.calculateEMA(prices, period);
      case 'WMA':
        return this.calculateWMA(recentPrices);
      default:
        return recentPrices.reduce((sum, price) => sum + price, 0) / period;
    }
  }

  // 计算指数移动平均线
  private calculateEMA(prices: number[], period: number): number {
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  }

  // 计算加权移动平均线
  private calculateWMA(prices: number[]): number {
    const weights = prices.map((_, i) => i + 1);
    const weightSum = weights.reduce((sum, weight) => sum + weight, 0);
    const weightedSum = prices.reduce((sum, price, i) => sum + (price * weights[i]), 0);
    
    return weightedSum / weightSum;
  }

  // 计算RSI
  private calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return 50;
    
    const changes = [];
    for (let i = 1; i < prices.length; i++) {
      changes.push(prices[i] - prices[i - 1]);
    }
    
    const recentChanges = changes.slice(-period);
    const gains = recentChanges.filter(change => change > 0);
    const losses = recentChanges.filter(change => change < 0).map(loss => Math.abs(loss));
    
    const avgGain = gains.length > 0 ? gains.reduce((sum, gain) => sum + gain, 0) / period : 0;
    const avgLoss = losses.length > 0 ? losses.reduce((sum, loss) => sum + loss, 0) / period : 0;
    
    if (avgLoss === 0) return 100;
    
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  // 计算ATR
  private calculateATR(prices: number[], period: number): number {
    if (prices.length < period + 1) return 0;
    
    const trueRanges = [];
    for (let i = 1; i < prices.length; i++) {
      const high = prices[i];
      const low = prices[i];
      const prevClose = prices[i - 1];
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      
      trueRanges.push(tr);
    }
    
    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((sum, tr) => sum + tr, 0) / period;
  }

  // 计算趋势强度
  private calculateTrendStrength(prices: number[]): number {
    if (prices.length < 20) return 0;
    
    const recentPrices = prices.slice(-20);
    const firstPrice = recentPrices[0];
    const lastPrice = recentPrices[recentPrices.length - 1];
    
    const trendDirection = lastPrice > firstPrice ? 1 : -1;
    let consistentMoves = 0;
    
    for (let i = 1; i < recentPrices.length; i++) {
      const move = recentPrices[i] - recentPrices[i - 1];
      if ((move > 0 && trendDirection > 0) || (move < 0 && trendDirection < 0)) {
        consistentMoves++;
      }
    }
    
    return consistentMoves / (recentPrices.length - 1);
  }

  // 计算信号置信度
  private calculateSignalConfidence(direction: 'buy' | 'sell'): number {
    if (!this.indicators) return 0;
    
    let confidence = 0.5;
    
    // 基于趋势强度
    confidence += this.indicators.trendStrength * 0.3;
    
    // 基于RSI
    if (direction === 'buy' && this.indicators.rsi < 50) {
      confidence += 0.1;
    } else if (direction === 'sell' && this.indicators.rsi > 50) {
      confidence += 0.1;
    }
    
    // 基于移动平均线分离度
    const maSeparation = Math.abs(this.indicators.fastMA - this.indicators.slowMA) / this.currentPrice;
    confidence += Math.min(maSeparation * 10, 0.2);
    
    return Math.min(confidence, 1);
  }

  // 管理持仓
  private async managePosition(): Promise<StrategyExecutionResult> {
    if (!this.currentPosition) {
      return {
        success: true,
        message: 'No position to manage',
        timestamp: new Date()
      };
    }

    // 更新未实现盈亏
    this.currentPosition.unrealizedPnl = this.calculatePositionProfit();
    
    // 更新追踪止损
    this.updateTrailingStop();
    
    // 检查止损和止盈
    if (this.shouldClosePosition()) {
      const signal: StrategySignal = {
        type: SignalType.CLOSE,
        symbol: this.config.symbol,
        price: this.currentPrice,
        amount: this.currentPosition.quantity,
        confidence: 0.8,
        timestamp: new Date()
      };
      
      return await this.executeSignal(signal);
    }

    return {
      success: true,
      message: 'Position managed',
      timestamp: new Date()
    };
  }

  // 计算持仓盈亏
  private calculatePositionProfit(): number {
    if (!this.currentPosition) return 0;
    
    const priceDiff = this.currentPosition.side === 'long'
      ? this.currentPrice - this.currentPosition.entryPrice
      : this.currentPosition.entryPrice - this.currentPrice;
    
    return priceDiff * this.currentPosition.quantity;
  }

  // 更新追踪止损
  private updateTrailingStop(): void {
    if (!this.currentPosition) return;
    
    const trailingDistance = this.currentPrice * (this.config.trailingStopPercent / 100);
    
    if (this.currentPosition.side === 'long') {
      const newTrailingStop = this.currentPrice - trailingDistance;
      this.currentPosition.trailingStop = Math.max(this.currentPosition.trailingStop, newTrailingStop);
    } else {
      const newTrailingStop = this.currentPrice + trailingDistance;
      this.currentPosition.trailingStop = Math.min(this.currentPosition.trailingStop, newTrailingStop);
    }
  }

  // 判断是否应该平仓
  private shouldClosePosition(): boolean {
    if (!this.currentPosition) return false;
    
    // 止损
    if (this.currentPosition.side === 'long' && this.currentPrice <= this.currentPosition.stopLoss) {
      return true;
    }
    if (this.currentPosition.side === 'short' && this.currentPrice >= this.currentPosition.stopLoss) {
      return true;
    }
    
    // 止盈
    if (this.currentPosition.side === 'long' && this.currentPrice >= this.currentPosition.takeProfit) {
      return true;
    }
    if (this.currentPosition.side === 'short' && this.currentPrice <= this.currentPosition.takeProfit) {
      return true;
    }
    
    // 追踪止损
    if (this.currentPosition.side === 'long' && this.currentPrice <= this.currentPosition.trailingStop) {
      return true;
    }
    if (this.currentPosition.side === 'short' && this.currentPrice >= this.currentPosition.trailingStop) {
      return true;
    }
    
    return false;
  }

  // 计算止损价格
  private calculateStopLoss(entryPrice: number, side: 'long' | 'short'): number {
    const stopLossDistance = entryPrice * (this.config.stopLossPercent / 100);
    return side === 'long' ? entryPrice - stopLossDistance : entryPrice + stopLossDistance;
  }

  // 计算止盈价格
  private calculateTakeProfit(entryPrice: number, side: 'long' | 'short'): number {
    const takeProfitDistance = entryPrice * (this.config.takeProfitPercent / 100);
    return side === 'long' ? entryPrice + takeProfitDistance : entryPrice - takeProfitDistance;
  }

  // 计算追踪止损价格
  private calculateTrailingStop(entryPrice: number, side: 'long' | 'short'): number {
    const trailingDistance = entryPrice * (this.config.trailingStopPercent / 100);
    return side === 'long' ? entryPrice - trailingDistance : entryPrice + trailingDistance;
  }

  // 获取当前持仓信息
  public getCurrentPosition(): Position | null {
    return this.currentPosition;
  }

  // 获取技术指标
  public getIndicators(): TechnicalIndicators | null {
    return this.indicators;
  }
}

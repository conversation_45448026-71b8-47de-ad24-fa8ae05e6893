import { BaseStrategy } from '../base/BaseStrategy';
import { ExchangeService } from '../../services/exchangeService';
import { PredictionService } from '../../ai/services/PredictionService';
import { StrategyType, CEXExchange, OrderType } from '../../../shared/types';
import {
  StrategyExecutionResult,
  StrategySignal,
  SignalType,
  ConfigurationError,
  ExecutionError
} from '../base/StrategyInterface';
import {
  AIStrategyConfig,
  AISignal,
  PredictionResult,
  PredictionType,
  MarketFeatures
} from '../../ai/base/AIInterface';

export class AIStrategy extends BaseStrategy {
  private config: AIStrategyConfig;
  private predictionService: PredictionService;
  private lastPredictions: Map<string, PredictionResult> = new Map();
  private currentPrice: number = 0;
  private marketFeatures: MarketFeatures[] = [];

  constructor(id: string, name: string) {
    super(id, name, StrategyType.AI_STRATEGY);
    this.predictionService = new PredictionService();
  }

  async initialize(config: AIStrategyConfig): Promise<void> {
    this.config = config;
    
    // 验证配置
    this.validateConfig();
    
    // 初始化市场数据
    await this.initializeMarketData();
    
    // 验证AI模型
    await this.validateModels();
  }

  async validate(): Promise<boolean> {
    try {
      // 检查配置
      if (!this.config) {
        throw new ConfigurationError('AI strategy not configured', this.id, this.type);
      }

      // 检查模型可用性
      for (const modelId of this.config.modelIds) {
        try {
          await this.predictionService.getModelInfo(modelId);
        } catch (error) {
          throw new ConfigurationError(`Model ${modelId} not found`, this.id, this.type);
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  async execute(): Promise<StrategyExecutionResult> {
    try {
      // 更新市场数据
      await this.updateMarketData();
      
      // 生成AI信号
      const aiSignal = await this.generateAISignal();
      
      if (aiSignal && aiSignal.confidence >= this.config.minConfidence) {
        return await this.executeAISignal(aiSignal);
      }

      return {
        success: true,
        message: 'No AI signal generated or confidence too low',
        timestamp: new Date()
      };
    } catch (error) {
      throw new ExecutionError(`AI strategy execution failed: ${error.message}`, this.id, this.type);
    }
  }

  protected async generateSignal(): Promise<StrategySignal | null> {
    const aiSignal = await this.generateAISignal();
    
    if (!aiSignal || aiSignal.confidence < this.config.minConfidence) {
      return null;
    }

    let signalType: SignalType;
    switch (aiSignal.action) {
      case 'buy':
        signalType = SignalType.BUY;
        break;
      case 'sell':
        signalType = SignalType.SELL;
        break;
      default:
        return null;
    }

    return {
      type: signalType,
      symbol: this.config.symbol,
      price: aiSignal.price,
      amount: aiSignal.quantity,
      confidence: aiSignal.confidence,
      timestamp: aiSignal.timestamp
    };
  }

  protected async executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult> {
    // 转换为AI信号并执行
    const aiSignal: AISignal = {
      action: signal.type === SignalType.BUY ? 'buy' : 'sell',
      confidence: signal.confidence,
      price: signal.price,
      quantity: signal.amount,
      reasoning: 'AI model prediction',
      predictions: Array.from(this.lastPredictions.values()),
      timestamp: signal.timestamp
    };

    return await this.executeAISignal(aiSignal);
  }

  // 生成AI信号
  private async generateAISignal(): Promise<AISignal | null> {
    try {
      if (this.marketFeatures.length < 100) {
        return null; // 需要足够的历史数据
      }

      // 获取最近的市场特征
      const recentFeatures = this.marketFeatures.slice(-100);
      const predictions: PredictionResult[] = [];

      // 从所有模型获取预测
      for (const modelId of this.config.modelIds) {
        try {
          const prediction = await this.predictionService.predict(modelId, recentFeatures);
          predictions.push(prediction);
          this.lastPredictions.set(modelId, prediction);
        } catch (error) {
          console.warn(`Failed to get prediction from model ${modelId}:`, error);
        }
      }

      if (predictions.length === 0) {
        return null;
      }

      // 集成预测结果
      const ensemblePrediction = this.ensemblePredictions(predictions);
      
      // 生成交易信号
      const signal = this.generateTradingSignal(ensemblePrediction, predictions);
      
      return signal;
    } catch (error) {
      throw new ExecutionError(`Failed to generate AI signal: ${error.message}`, this.id, this.type);
    }
  }

  // 集成多个预测结果
  private ensemblePredictions(predictions: PredictionResult[]): PredictionResult {
    switch (this.config.ensembleMethod) {
      case 'average':
        return this.averageEnsemble(predictions);
      case 'weighted':
        return this.weightedEnsemble(predictions);
      case 'voting':
        return this.votingEnsemble(predictions);
      default:
        return this.averageEnsemble(predictions);
    }
  }

  // 平均集成
  private averageEnsemble(predictions: PredictionResult[]): PredictionResult {
    const avgValue = predictions.reduce((sum, pred) => sum + pred.value, 0) / predictions.length;
    const avgConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;

    return {
      type: PredictionType.PRICE,
      value: avgValue,
      confidence: avgConfidence,
      timestamp: new Date(),
      timeframe: this.config.timeframe,
      metadata: {
        ensemble_method: 'average',
        model_count: predictions.length
      }
    };
  }

  // 加权集成
  private weightedEnsemble(predictions: PredictionResult[]): PredictionResult {
    const totalWeight = predictions.reduce((sum, pred) => sum + pred.confidence, 0);
    
    const weightedValue = predictions.reduce((sum, pred) => 
      sum + (pred.value * pred.confidence), 0) / totalWeight;
    
    const avgConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;

    return {
      type: PredictionType.PRICE,
      value: weightedValue,
      confidence: avgConfidence,
      timestamp: new Date(),
      timeframe: this.config.timeframe,
      metadata: {
        ensemble_method: 'weighted',
        model_count: predictions.length
      }
    };
  }

  // 投票集成
  private votingEnsemble(predictions: PredictionResult[]): PredictionResult {
    const currentPrice = this.currentPrice;
    let buyVotes = 0;
    let sellVotes = 0;
    let totalConfidence = 0;

    predictions.forEach(pred => {
      if (pred.value > currentPrice) {
        buyVotes += pred.confidence;
      } else {
        sellVotes += pred.confidence;
      }
      totalConfidence += pred.confidence;
    });

    const avgValue = predictions.reduce((sum, pred) => sum + pred.value, 0) / predictions.length;
    const confidence = Math.max(buyVotes, sellVotes) / totalConfidence;

    return {
      type: PredictionType.DIRECTION,
      value: avgValue,
      confidence,
      timestamp: new Date(),
      timeframe: this.config.timeframe,
      metadata: {
        ensemble_method: 'voting',
        buy_votes: buyVotes,
        sell_votes: sellVotes,
        model_count: predictions.length
      }
    };
  }

  // 生成交易信号
  private generateTradingSignal(prediction: PredictionResult, allPredictions: PredictionResult[]): AISignal | null {
    const currentPrice = this.currentPrice;
    const priceChange = (prediction.value - currentPrice) / currentPrice;
    const minChangeThreshold = 0.01; // 1% 最小变化阈值

    // 判断交易方向
    let action: 'buy' | 'sell' | 'hold';
    if (Math.abs(priceChange) < minChangeThreshold) {
      action = 'hold';
    } else if (priceChange > 0) {
      action = 'buy';
    } else {
      action = 'sell';
    }

    if (action === 'hold') {
      return null;
    }

    // 计算持仓大小
    const baseQuantity = this.config.riskManagement.maxPositionSize;
    const confidenceMultiplier = prediction.confidence;
    const quantity = baseQuantity * confidenceMultiplier;

    // 计算止损和止盈
    const stopLossPercent = this.config.riskManagement.stopLossPercent / 100;
    const takeProfitPercent = this.config.riskManagement.takeProfitPercent / 100;

    let stopLoss: number;
    let takeProfit: number;

    if (action === 'buy') {
      stopLoss = currentPrice * (1 - stopLossPercent);
      takeProfit = currentPrice * (1 + takeProfitPercent);
    } else {
      stopLoss = currentPrice * (1 + stopLossPercent);
      takeProfit = currentPrice * (1 - takeProfitPercent);
    }

    // 生成推理说明
    const reasoning = this.generateReasoning(prediction, allPredictions, priceChange);

    return {
      action,
      confidence: prediction.confidence,
      price: currentPrice,
      quantity,
      stopLoss,
      takeProfit,
      reasoning,
      predictions: allPredictions,
      timestamp: new Date()
    };
  }

  // 生成推理说明
  private generateReasoning(
    prediction: PredictionResult,
    allPredictions: PredictionResult[],
    priceChange: number
  ): string {
    const direction = priceChange > 0 ? 'upward' : 'downward';
    const magnitude = Math.abs(priceChange * 100).toFixed(2);
    const modelCount = allPredictions.length;
    const avgConfidence = (allPredictions.reduce((sum, pred) => sum + pred.confidence, 0) / modelCount * 100).toFixed(1);

    return `AI ensemble of ${modelCount} models predicts ${direction} price movement of ${magnitude}% with ${avgConfidence}% average confidence. Ensemble method: ${this.config.ensembleMethod}.`;
  }

  // 执行AI信号
  private async executeAISignal(signal: AISignal): Promise<StrategyExecutionResult> {
    try {
      // 检查风险限制
      if (!await this.checkRiskLimits()) {
        return {
          success: false,
          message: 'Risk limits exceeded',
          timestamp: new Date()
        };
      }

      // 检查每日损失限制
      const todayLoss = this.calculateTodayLoss();
      if (Math.abs(todayLoss) >= this.config.riskManagement.maxDailyLoss) {
        return {
          success: false,
          message: 'Daily loss limit reached',
          timestamp: new Date()
        };
      }

      // 执行交易
      const order = await this.executeOrder(signal);

      return {
        success: true,
        message: `AI signal executed: ${signal.action}`,
        orderId: order.id,
        amount: signal.quantity,
        price: signal.price,
        timestamp: new Date()
      };
    } catch (error) {
      throw new ExecutionError(`Failed to execute AI signal: ${error.message}`, this.id, this.type);
    }
  }

  // 执行订单
  private async executeOrder(signal: AISignal): Promise<any> {
    // 这里需要根据具体的交易所实现
    // 暂时返回模拟订单
    return {
      id: `ai_order_${Date.now()}`,
      symbol: this.config.symbol,
      side: signal.action,
      amount: signal.quantity,
      price: signal.price,
      status: 'filled'
    };
  }

  // 验证配置
  private validateConfig(): void {
    if (!this.config.symbol) {
      throw new ConfigurationError('Symbol is required', this.id, this.type);
    }

    if (!this.config.modelIds || this.config.modelIds.length === 0) {
      throw new ConfigurationError('At least one model ID is required', this.id, this.type);
    }

    if (this.config.minConfidence < 0 || this.config.minConfidence > 1) {
      throw new ConfigurationError('Min confidence must be between 0 and 1', this.id, this.type);
    }

    if (!['average', 'weighted', 'voting'].includes(this.config.ensembleMethod)) {
      throw new ConfigurationError('Invalid ensemble method', this.id, this.type);
    }
  }

  // 初始化市场数据
  private async initializeMarketData(): Promise<void> {
    try {
      // 这里应该从交易所获取历史数据
      // 暂时使用模拟数据
      this.marketFeatures = [];
      this.currentPrice = 100; // 模拟当前价格
    } catch (error) {
      throw new ConfigurationError(`Failed to initialize market data: ${error.message}`, this.id, this.type);
    }
  }

  // 验证模型
  private async validateModels(): Promise<void> {
    for (const modelId of this.config.modelIds) {
      try {
        const modelInfo = await this.predictionService.getModelInfo(modelId);
        if (!modelInfo.isActive) {
          throw new ConfigurationError(`Model ${modelId} is not active`, this.id, this.type);
        }
      } catch (error) {
        throw new ConfigurationError(`Model validation failed for ${modelId}: ${error.message}`, this.id, this.type);
      }
    }
  }

  // 更新市场数据
  private async updateMarketData(): Promise<void> {
    try {
      // 这里应该获取最新的市场数据
      // 暂时使用模拟数据
      const newFeature: MarketFeatures = {
        timestamp: new Date(),
        open: this.currentPrice,
        high: this.currentPrice * 1.02,
        low: this.currentPrice * 0.98,
        close: this.currentPrice * (1 + (Math.random() - 0.5) * 0.02),
        volume: Math.random() * 1000000
      };

      this.marketFeatures.push(newFeature);
      this.currentPrice = newFeature.close;

      // 保持数据长度
      if (this.marketFeatures.length > 1000) {
        this.marketFeatures = this.marketFeatures.slice(-1000);
      }
    } catch (error) {
      throw new ExecutionError(`Failed to update market data: ${error.message}`, this.id, this.type);
    }
  }

  // 获取AI策略状态
  public getAIStatus(): {
    modelCount: number;
    lastPredictions: Record<string, PredictionResult>;
    currentPrice: number;
    marketDataPoints: number;
    ensembleMethod: string;
    minConfidence: number;
  } {
    const lastPredictionsObj: Record<string, PredictionResult> = {};
    this.lastPredictions.forEach((prediction, modelId) => {
      lastPredictionsObj[modelId] = prediction;
    });

    return {
      modelCount: this.config.modelIds.length,
      lastPredictions: lastPredictionsObj,
      currentPrice: this.currentPrice,
      marketDataPoints: this.marketFeatures.length,
      ensembleMethod: this.config.ensembleMethod,
      minConfidence: this.config.minConfidence
    };
  }

  // 获取预测服务实例
  public getPredictionService(): PredictionService {
    return this.predictionService;
  }
}

import { CEXExchange, DEXProtocol, BlockchainNetwork, StrategyType, StrategyStatus } from '../../../shared/types';

// 策略配置基础接口
export interface BaseStrategyConfig {
  id: string;
  userId: string;
  name: string;
  type: StrategyType;
  exchangeType: 'cex' | 'dex';
  exchange?: CEXExchange;
  network?: BlockchainNetwork;
  dexProtocol?: DEXProtocol;
  status: StrategyStatus;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 策略执行结果
export interface StrategyExecutionResult {
  success: boolean;
  message: string;
  orderId?: string;
  txHash?: string;
  amount?: number;
  price?: number;
  fee?: number;
  profit?: number;
  timestamp: Date;
}

// 策略统计信息
export interface StrategyStats {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  totalProfit: number;
  totalFees: number;
  winRate: number;
  averageProfit: number;
  maxProfit: number;
  maxLoss: number;
  sharpeRatio?: number;
  maxDrawdown?: number;
}

// 策略风险参数
export interface RiskParameters {
  maxPositionSize: number;
  maxDailyLoss: number;
  maxDrawdown: number;
  stopLossPercentage: number;
  takeProfitPercentage: number;
  maxConcurrentTrades: number;
}

// 策略接口
export interface IStrategy {
  readonly id: string;
  readonly name: string;
  readonly type: StrategyType;
  readonly status: StrategyStatus;
  readonly isRunning: boolean;

  // 生命周期方法
  initialize(config: any): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  pause(): Promise<void>;
  resume(): Promise<void>;

  // 执行方法
  execute(): Promise<StrategyExecutionResult>;
  validate(): Promise<boolean>;

  // 配置方法
  updateConfig(config: any): Promise<void>;
  getConfig(): any;

  // 统计方法
  getStats(): Promise<StrategyStats>;
  getPerformance(startDate?: Date, endDate?: Date): Promise<any>;

  // 风险管理
  checkRiskLimits(): Promise<boolean>;
  calculatePositionSize(signal: any): number;

  // 事件处理
  onMarketData?(data: any): void;
  onOrderFilled?(order: any): void;
  onError?(error: Error): void;
}

// 策略工厂接口
export interface IStrategyFactory {
  createStrategy(type: StrategyType, config: any): Promise<IStrategy>;
  getSupportedTypes(): StrategyType[];
  validateConfig(type: StrategyType, config: any): { valid: boolean; errors: string[] };
}

// 策略管理器接口
export interface IStrategyManager {
  // 策略管理
  addStrategy(strategy: IStrategy): Promise<void>;
  removeStrategy(strategyId: string): Promise<void>;
  getStrategy(strategyId: string): IStrategy | null;
  getAllStrategies(): IStrategy[];
  getActiveStrategies(): IStrategy[];

  // 批量操作
  startAll(): Promise<void>;
  stopAll(): Promise<void>;
  pauseAll(): Promise<void>;
  resumeAll(): Promise<void>;

  // 监控
  getSystemStats(): Promise<any>;
  getStrategyPerformance(strategyId: string): Promise<any>;
}

// 策略错误类
export class StrategyError extends Error {
  constructor(
    message: string,
    public strategyId: string,
    public strategyType: StrategyType,
    public code?: string
  ) {
    super(message);
    this.name = 'StrategyError';
  }
}

// 配置错误
export class ConfigurationError extends StrategyError {
  constructor(message: string, strategyId: string, strategyType: StrategyType) {
    super(message, strategyId, strategyType, 'CONFIG_ERROR');
    this.name = 'ConfigurationError';
  }
}

// 执行错误
export class ExecutionError extends StrategyError {
  constructor(message: string, strategyId: string, strategyType: StrategyType) {
    super(message, strategyId, strategyType, 'EXECUTION_ERROR');
    this.name = 'ExecutionError';
  }
}

// 风险管理错误
export class RiskManagementError extends StrategyError {
  constructor(message: string, strategyId: string, strategyType: StrategyType) {
    super(message, strategyId, strategyType, 'RISK_ERROR');
    this.name = 'RiskManagementError';
  }
}

// 市场数据错误
export class MarketDataError extends StrategyError {
  constructor(message: string, strategyId: string, strategyType: StrategyType) {
    super(message, strategyId, strategyType, 'MARKET_DATA_ERROR');
    this.name = 'MarketDataError';
  }
}

// 策略信号类型
export enum SignalType {
  BUY = 'buy',
  SELL = 'sell',
  HOLD = 'hold',
  CLOSE = 'close'
}

// 策略信号
export interface StrategySignal {
  type: SignalType;
  symbol: string;
  price: number;
  amount: number;
  confidence: number; // 0-1
  timestamp: Date;
  metadata?: any;
}

// 市场数据
export interface MarketData {
  symbol: string;
  price: number;
  volume: number;
  timestamp: Date;
  bid?: number;
  ask?: number;
  high24h?: number;
  low24h?: number;
  change24h?: number;
}

// 订单信息
export interface OrderInfo {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit';
  amount: number;
  price?: number;
  status: string;
  filled: number;
  remaining: number;
  timestamp: Date;
}

// 持仓信息
export interface PositionInfo {
  symbol: string;
  side: 'long' | 'short';
  size: number;
  entryPrice: number;
  currentPrice: number;
  unrealizedPnl: number;
  realizedPnl: number;
  timestamp: Date;
}

// 策略回测结果
export interface BacktestResult {
  strategyId: string;
  startDate: Date;
  endDate: Date;
  initialBalance: number;
  finalBalance: number;
  totalReturn: number;
  annualizedReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  winRate: number;
  totalTrades: number;
  profitFactor: number;
  trades: any[];
  equity: any[];
}

// 策略优化参数
export interface OptimizationParameter {
  name: string;
  type: 'number' | 'boolean' | 'string';
  min?: number;
  max?: number;
  step?: number;
  values?: any[];
  default: any;
}

// 策略优化结果
export interface OptimizationResult {
  parameters: Record<string, any>;
  performance: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
  backtest: BacktestResult;
}

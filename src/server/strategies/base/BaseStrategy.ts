import { EventEmitter } from 'events';
import { logger, strategyLogger } from '../../utils/logger';
import { StrategyType, StrategyStatus } from '../../../shared/types';
import {
  IStrategy,
  StrategyExecutionResult,
  StrategyStats,
  RiskParameters,
  StrategySignal,
  MarketData,
  OrderInfo,
  PositionInfo,
  StrategyError,
  ExecutionError,
  RiskManagementError
} from './StrategyInterface';

export abstract class BaseStrategy extends EventEmitter implements IStrategy {
  public readonly id: string;
  public readonly name: string;
  public readonly type: StrategyType;
  protected config: any;
  protected _status: StrategyStatus = StrategyStatus.INACTIVE;
  protected _isRunning: boolean = false;
  protected riskParameters: RiskParameters;
  protected positions: Map<string, PositionInfo> = new Map();
  protected orders: Map<string, OrderInfo> = new Map();
  protected trades: any[] = [];
  protected lastExecution: Date | null = null;
  protected executionInterval: NodeJS.Timeout | null = null;

  constructor(id: string, name: string, type: StrategyType) {
    super();
    this.id = id;
    this.name = name;
    this.type = type;
    
    // 默认风险参数
    this.riskParameters = {
      maxPositionSize: 1000,
      maxDailyLoss: 100,
      maxDrawdown: 0.1,
      stopLossPercentage: 0.05,
      takeProfitPercentage: 0.1,
      maxConcurrentTrades: 5
    };
  }

  get status(): StrategyStatus {
    return this._status;
  }

  get isRunning(): boolean {
    return this._isRunning;
  }

  // 抽象方法，子类必须实现
  abstract initialize(config: any): Promise<void>;
  abstract execute(): Promise<StrategyExecutionResult>;
  abstract validate(): Promise<boolean>;
  protected abstract generateSignal(): Promise<StrategySignal | null>;
  protected abstract executeSignal(signal: StrategySignal): Promise<StrategyExecutionResult>;

  // 启动策略
  async start(): Promise<void> {
    try {
      if (this._isRunning) {
        throw new StrategyError('Strategy is already running', this.id, this.type);
      }

      // 验证配置
      const isValid = await this.validate();
      if (!isValid) {
        throw new StrategyError('Strategy validation failed', this.id, this.type);
      }

      this._status = StrategyStatus.ACTIVE;
      this._isRunning = true;

      // 开始执行循环
      this.startExecutionLoop();

      this.emit('started');
      strategyLogger.info(`Strategy ${this.name} (${this.id}) started`);
    } catch (error) {
      this._status = StrategyStatus.ERROR;
      this._isRunning = false;
      this.emit('error', error);
      throw error;
    }
  }

  // 停止策略
  async stop(): Promise<void> {
    try {
      this._isRunning = false;
      this._status = StrategyStatus.INACTIVE;

      // 停止执行循环
      if (this.executionInterval) {
        clearInterval(this.executionInterval);
        this.executionInterval = null;
      }

      // 关闭所有持仓
      await this.closeAllPositions();

      this.emit('stopped');
      strategyLogger.info(`Strategy ${this.name} (${this.id}) stopped`);
    } catch (error) {
      this._status = StrategyStatus.ERROR;
      this.emit('error', error);
      throw error;
    }
  }

  // 暂停策略
  async pause(): Promise<void> {
    if (!this._isRunning) {
      throw new StrategyError('Strategy is not running', this.id, this.type);
    }

    this._status = StrategyStatus.PAUSED;
    
    if (this.executionInterval) {
      clearInterval(this.executionInterval);
      this.executionInterval = null;
    }

    this.emit('paused');
    strategyLogger.info(`Strategy ${this.name} (${this.id}) paused`);
  }

  // 恢复策略
  async resume(): Promise<void> {
    if (this._status !== StrategyStatus.PAUSED) {
      throw new StrategyError('Strategy is not paused', this.id, this.type);
    }

    this._status = StrategyStatus.ACTIVE;
    this.startExecutionLoop();

    this.emit('resumed');
    strategyLogger.info(`Strategy ${this.name} (${this.id}) resumed`);
  }

  // 更新配置
  async updateConfig(config: any): Promise<void> {
    const oldConfig = { ...this.config };
    
    try {
      this.config = { ...this.config, ...config };
      
      // 如果策略正在运行，需要重新验证
      if (this._isRunning) {
        const isValid = await this.validate();
        if (!isValid) {
          this.config = oldConfig; // 回滚配置
          throw new StrategyError('Invalid configuration', this.id, this.type);
        }
      }

      this.emit('configUpdated', config);
      strategyLogger.info(`Strategy ${this.name} (${this.id}) configuration updated`);
    } catch (error) {
      this.config = oldConfig; // 回滚配置
      throw error;
    }
  }

  // 获取配置
  getConfig(): any {
    return { ...this.config };
  }

  // 获取统计信息
  async getStats(): Promise<StrategyStats> {
    const successfulTrades = this.trades.filter(trade => trade.profit > 0).length;
    const failedTrades = this.trades.filter(trade => trade.profit <= 0).length;
    const totalProfit = this.trades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
    const totalFees = this.trades.reduce((sum, trade) => sum + (trade.fee || 0), 0);
    const profits = this.trades.map(trade => trade.profit || 0);

    return {
      totalTrades: this.trades.length,
      successfulTrades,
      failedTrades,
      totalProfit,
      totalFees,
      winRate: this.trades.length > 0 ? successfulTrades / this.trades.length : 0,
      averageProfit: this.trades.length > 0 ? totalProfit / this.trades.length : 0,
      maxProfit: profits.length > 0 ? Math.max(...profits) : 0,
      maxLoss: profits.length > 0 ? Math.min(...profits) : 0,
      sharpeRatio: this.calculateSharpeRatio(profits),
      maxDrawdown: this.calculateMaxDrawdown()
    };
  }

  // 获取性能数据
  async getPerformance(startDate?: Date, endDate?: Date): Promise<any> {
    let filteredTrades = this.trades;

    if (startDate || endDate) {
      filteredTrades = this.trades.filter(trade => {
        const tradeDate = new Date(trade.timestamp);
        if (startDate && tradeDate < startDate) return false;
        if (endDate && tradeDate > endDate) return false;
        return true;
      });
    }

    const equity = this.calculateEquityCurve(filteredTrades);
    const monthlyReturns = this.calculateMonthlyReturns(filteredTrades);

    return {
      trades: filteredTrades,
      equity,
      monthlyReturns,
      stats: await this.getStats()
    };
  }

  // 检查风险限制
  async checkRiskLimits(): Promise<boolean> {
    try {
      // 检查最大持仓数量
      if (this.positions.size >= this.riskParameters.maxConcurrentTrades) {
        return false;
      }

      // 检查当日损失
      const todayLoss = this.calculateTodayLoss();
      if (todayLoss >= this.riskParameters.maxDailyLoss) {
        return false;
      }

      // 检查最大回撤
      const currentDrawdown = this.calculateMaxDrawdown();
      if (currentDrawdown >= this.riskParameters.maxDrawdown) {
        return false;
      }

      return true;
    } catch (error) {
      throw new RiskManagementError(`Risk check failed: ${error.message}`, this.id, this.type);
    }
  }

  // 计算持仓大小
  calculatePositionSize(signal: StrategySignal): number {
    const maxSize = this.riskParameters.maxPositionSize;
    const confidence = signal.confidence;
    
    // 基于信号置信度调整持仓大小
    return Math.min(maxSize * confidence, maxSize);
  }

  // 开始执行循环
  protected startExecutionLoop(): void {
    const interval = this.config.executionInterval || 60000; // 默认1分钟
    
    this.executionInterval = setInterval(async () => {
      if (this._status === StrategyStatus.ACTIVE) {
        try {
          await this.executeStrategy();
        } catch (error) {
          this.handleExecutionError(error);
        }
      }
    }, interval);
  }

  // 执行策略逻辑
  protected async executeStrategy(): Promise<void> {
    try {
      // 检查风险限制
      const riskCheckPassed = await this.checkRiskLimits();
      if (!riskCheckPassed) {
        strategyLogger.warn(`Strategy ${this.id} risk limits exceeded, skipping execution`);
        return;
      }

      // 生成信号
      const signal = await this.generateSignal();
      if (!signal) {
        return; // 没有信号，跳过执行
      }

      // 执行信号
      const result = await this.executeSignal(signal);
      
      // 记录执行结果
      this.recordExecution(result);
      this.lastExecution = new Date();

      this.emit('executed', result);
    } catch (error) {
      throw new ExecutionError(`Strategy execution failed: ${error.message}`, this.id, this.type);
    }
  }

  // 关闭所有持仓
  protected async closeAllPositions(): Promise<void> {
    const closePromises = Array.from(this.positions.values()).map(position => 
      this.closePosition(position.symbol)
    );
    
    await Promise.all(closePromises);
  }

  // 关闭单个持仓
  protected async closePosition(symbol: string): Promise<void> {
    // 子类实现具体的平仓逻辑
  }

  // 记录执行结果
  protected recordExecution(result: StrategyExecutionResult): void {
    if (result.success && result.profit !== undefined) {
      this.trades.push({
        timestamp: result.timestamp,
        profit: result.profit,
        fee: result.fee || 0,
        amount: result.amount,
        price: result.price
      });
    }
  }

  // 处理执行错误
  protected handleExecutionError(error: any): void {
    this._status = StrategyStatus.ERROR;
    this.emit('error', error);
    strategyLogger.error(`Strategy ${this.id} execution error:`, error);
  }

  // 计算夏普比率
  protected calculateSharpeRatio(returns: number[]): number {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev === 0 ? 0 : mean / stdDev;
  }

  // 计算最大回撤
  protected calculateMaxDrawdown(): number {
    if (this.trades.length === 0) return 0;
    
    let peak = 0;
    let maxDrawdown = 0;
    let runningTotal = 0;
    
    for (const trade of this.trades) {
      runningTotal += trade.profit || 0;
      if (runningTotal > peak) {
        peak = runningTotal;
      }
      const drawdown = (peak - runningTotal) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }

  // 计算当日损失
  protected calculateTodayLoss(): number {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return this.trades
      .filter(trade => new Date(trade.timestamp) >= today)
      .reduce((sum, trade) => sum + Math.min(0, trade.profit || 0), 0);
  }

  // 计算权益曲线
  protected calculateEquityCurve(trades: any[]): any[] {
    let runningTotal = 0;
    return trades.map(trade => {
      runningTotal += trade.profit || 0;
      return {
        timestamp: trade.timestamp,
        equity: runningTotal
      };
    });
  }

  // 计算月度收益
  protected calculateMonthlyReturns(trades: any[]): any[] {
    const monthlyData: Record<string, number> = {};
    
    trades.forEach(trade => {
      const date = new Date(trade.timestamp);
      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + (trade.profit || 0);
    });
    
    return Object.entries(monthlyData).map(([month, profit]) => ({
      month,
      profit
    }));
  }

  // 事件处理方法
  onMarketData?(data: MarketData): void {
    // 子类可以重写此方法处理市场数据
  }

  onOrderFilled?(order: OrderInfo): void {
    // 子类可以重写此方法处理订单成交
  }

  onError?(error: Error): void {
    // 子类可以重写此方法处理错误
  }
}

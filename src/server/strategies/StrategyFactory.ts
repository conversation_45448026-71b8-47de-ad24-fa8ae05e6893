import { StrategyType } from '../../shared/types';
import { IStrategy, IStrategyFactory, ConfigurationError } from './base/StrategyInterface';
import { GridStrategy } from './cex/GridStrategy';
import { TrendFollowingStrategy } from './cex/TrendFollowingStrategy';
// 其他策略的导入将在后续添加
// import { PositionBuildingStrategy } from './cex/PositionBuildingStrategy';
// import { LiquidityMaintenanceStrategy } from './cex/LiquidityMaintenanceStrategy';
// import { VolumeWashingStrategy } from './cex/VolumeWashingStrategy';
// import { HedgingStrategy } from './cex/HedgingStrategy';
// import { AIStrategy } from './cex/AIStrategy';
// import { DEXVolumeWashingStrategy } from './dex/DEXVolumeWashingStrategy';
// import { DEXTrendFollowingStrategy } from './dex/DEXTrendFollowingStrategy';
// import { AccountTrackingStrategy } from './dex/AccountTrackingStrategy';
// import { DEXPositionBuildingStrategy } from './dex/DEXPositionBuildingStrategy';
// import { DEXAIStrategy } from './dex/DEXAIStrategy';

export class StrategyFactory implements IStrategyFactory {
  private static instance: StrategyFactory;
  private strategyCounter: number = 0;

  private constructor() {}

  static getInstance(): StrategyFactory {
    if (!StrategyFactory.instance) {
      StrategyFactory.instance = new StrategyFactory();
    }
    return StrategyFactory.instance;
  }

  /**
   * 创建策略实例
   * @param type 策略类型
   * @param config 策略配置
   * @returns 策略实例
   */
  async createStrategy(type: StrategyType, config: any): Promise<IStrategy> {
    // 验证配置
    const validation = this.validateConfig(type, config);
    if (!validation.valid) {
      throw new ConfigurationError(
        `Invalid configuration: ${validation.errors.join(', ')}`,
        config.id || 'unknown',
        type
      );
    }

    // 生成策略ID和名称
    const strategyId = config.id || this.generateStrategyId(type);
    const strategyName = config.name || this.generateStrategyName(type);

    let strategy: IStrategy;

    switch (type) {
      case StrategyType.GRID:
        strategy = new GridStrategy(strategyId, strategyName);
        break;

      case StrategyType.TREND_FOLLOWING:
        strategy = new TrendFollowingStrategy(strategyId, strategyName);
        break;

      // case StrategyType.POSITION_BUILDING:
      //   strategy = new PositionBuildingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.LIQUIDITY_MAINTENANCE:
      //   strategy = new LiquidityMaintenanceStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.VOLUME_WASHING:
      //   strategy = new VolumeWashingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.HEDGING:
      //   strategy = new HedgingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.AI_STRATEGY:
      //   strategy = new AIStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.DEX_VOLUME_WASHING:
      //   strategy = new DEXVolumeWashingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.DEX_TREND_FOLLOWING:
      //   strategy = new DEXTrendFollowingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.ACCOUNT_TRACKING:
      //   strategy = new AccountTrackingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.DEX_POSITION_BUILDING:
      //   strategy = new DEXPositionBuildingStrategy(strategyId, strategyName);
      //   break;

      // case StrategyType.DEX_AI_STRATEGY:
      //   strategy = new DEXAIStrategy(strategyId, strategyName);
      //   break;

      default:
        throw new ConfigurationError(
          `Unsupported strategy type: ${type}`,
          strategyId,
          type
        );
    }

    // 初始化策略
    await strategy.initialize(config);

    return strategy;
  }

  /**
   * 获取支持的策略类型
   * @returns 支持的策略类型数组
   */
  getSupportedTypes(): StrategyType[] {
    return [
      StrategyType.GRID,
      StrategyType.TREND_FOLLOWING,
      // StrategyType.POSITION_BUILDING,
      // StrategyType.LIQUIDITY_MAINTENANCE,
      // StrategyType.VOLUME_WASHING,
      // StrategyType.HEDGING,
      // StrategyType.AI_STRATEGY,
      // StrategyType.DEX_VOLUME_WASHING,
      // StrategyType.DEX_TREND_FOLLOWING,
      // StrategyType.ACCOUNT_TRACKING,
      // StrategyType.DEX_POSITION_BUILDING,
      // StrategyType.DEX_AI_STRATEGY
    ];
  }

  /**
   * 验证策略配置
   * @param type 策略类型
   * @param config 配置对象
   * @returns 验证结果
   */
  validateConfig(type: StrategyType, config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 基础验证
    if (!config) {
      errors.push('Configuration is required');
      return { valid: false, errors };
    }

    if (!config.userId) {
      errors.push('User ID is required');
    }

    // 根据策略类型进行特定验证
    switch (type) {
      case StrategyType.GRID:
        this.validateGridConfig(config, errors);
        break;

      case StrategyType.TREND_FOLLOWING:
        this.validateTrendFollowingConfig(config, errors);
        break;

      // case StrategyType.POSITION_BUILDING:
      //   this.validatePositionBuildingConfig(config, errors);
      //   break;

      // case StrategyType.LIQUIDITY_MAINTENANCE:
      //   this.validateLiquidityMaintenanceConfig(config, errors);
      //   break;

      // case StrategyType.VOLUME_WASHING:
      //   this.validateVolumeWashingConfig(config, errors);
      //   break;

      // case StrategyType.HEDGING:
      //   this.validateHedgingConfig(config, errors);
      //   break;

      // case StrategyType.AI_STRATEGY:
      //   this.validateAIConfig(config, errors);
      //   break;

      // DEX策略验证...

      default:
        errors.push(`Unknown strategy type: ${type}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证网格策略配置
   */
  private validateGridConfig(config: any, errors: string[]): void {
    if (!config.exchange) {
      errors.push('Exchange is required for grid strategy');
    }

    if (!config.symbol) {
      errors.push('Symbol is required for grid strategy');
    }

    if (!config.baseAsset || !config.quoteAsset) {
      errors.push('Base and quote assets are required for grid strategy');
    }

    if (typeof config.upperPrice !== 'number' || config.upperPrice <= 0) {
      errors.push('Upper price must be a positive number');
    }

    if (typeof config.lowerPrice !== 'number' || config.lowerPrice <= 0) {
      errors.push('Lower price must be a positive number');
    }

    if (config.upperPrice <= config.lowerPrice) {
      errors.push('Upper price must be greater than lower price');
    }

    if (typeof config.gridLevels !== 'number' || config.gridLevels < 2) {
      errors.push('Grid levels must be at least 2');
    }

    if (typeof config.totalAmount !== 'number' || config.totalAmount <= 0) {
      errors.push('Total amount must be a positive number');
    }

    if (!['arithmetic', 'geometric'].includes(config.gridType)) {
      errors.push('Grid type must be either "arithmetic" or "geometric"');
    }
  }

  /**
   * 验证趋势跟踪策略配置
   */
  private validateTrendFollowingConfig(config: any, errors: string[]): void {
    if (!config.exchange) {
      errors.push('Exchange is required for trend following strategy');
    }

    if (!config.symbol) {
      errors.push('Symbol is required for trend following strategy');
    }

    if (!config.baseAsset || !config.quoteAsset) {
      errors.push('Base and quote assets are required for trend following strategy');
    }

    if (!['1m', '5m', '15m', '1h', '4h', '1d'].includes(config.timeframe)) {
      errors.push('Invalid timeframe');
    }

    if (!['SMA', 'EMA', 'WMA'].includes(config.maType)) {
      errors.push('MA type must be SMA, EMA, or WMA');
    }

    if (typeof config.fastPeriod !== 'number' || config.fastPeriod < 1) {
      errors.push('Fast period must be a positive number');
    }

    if (typeof config.slowPeriod !== 'number' || config.slowPeriod < 1) {
      errors.push('Slow period must be a positive number');
    }

    if (config.fastPeriod >= config.slowPeriod) {
      errors.push('Fast period must be less than slow period');
    }

    if (typeof config.positionSize !== 'number' || config.positionSize <= 0) {
      errors.push('Position size must be a positive number');
    }

    if (typeof config.stopLossPercent !== 'number' || config.stopLossPercent <= 0 || config.stopLossPercent >= 100) {
      errors.push('Stop loss percent must be between 0 and 100');
    }

    if (typeof config.takeProfitPercent !== 'number' || config.takeProfitPercent <= 0) {
      errors.push('Take profit percent must be a positive number');
    }
  }

  /**
   * 生成策略ID
   */
  private generateStrategyId(type: StrategyType): string {
    this.strategyCounter++;
    const timestamp = Date.now();
    return `${type}_${timestamp}_${this.strategyCounter}`;
  }

  /**
   * 生成策略名称
   */
  private generateStrategyName(type: StrategyType): string {
    const typeNames: Record<StrategyType, string> = {
      [StrategyType.GRID]: 'Grid Trading',
      [StrategyType.POSITION_BUILDING]: 'Position Building',
      [StrategyType.LIQUIDITY_MAINTENANCE]: 'Liquidity Maintenance',
      [StrategyType.VOLUME_WASHING]: 'Volume Washing',
      [StrategyType.TREND_FOLLOWING]: 'Trend Following',
      [StrategyType.HEDGING]: 'Hedging',
      [StrategyType.AI_STRATEGY]: 'AI Strategy',
      [StrategyType.DEX_VOLUME_WASHING]: 'DEX Volume Washing',
      [StrategyType.DEX_TREND_FOLLOWING]: 'DEX Trend Following',
      [StrategyType.ACCOUNT_TRACKING]: 'Account Tracking',
      [StrategyType.DEX_POSITION_BUILDING]: 'DEX Position Building',
      [StrategyType.DEX_AI_STRATEGY]: 'DEX AI Strategy'
    };

    const baseName = typeNames[type] || 'Unknown Strategy';
    return `${baseName} #${this.strategyCounter}`;
  }

  /**
   * 获取策略类型的默认配置
   * @param type 策略类型
   * @returns 默认配置
   */
  getDefaultConfig(type: StrategyType): any {
    const defaultConfigs: Record<StrategyType, any> = {
      [StrategyType.GRID]: {
        gridType: 'arithmetic',
        gridLevels: 10,
        executionInterval: 60000,
        rebalanceThreshold: 0.02
      },
      [StrategyType.TREND_FOLLOWING]: {
        timeframe: '1h',
        maType: 'EMA',
        fastPeriod: 12,
        slowPeriod: 26,
        signalPeriod: 9,
        stopLossPercent: 2,
        takeProfitPercent: 4,
        trailingStopPercent: 1,
        minTrendStrength: 0.6,
        executionInterval: 300000
      },
      [StrategyType.POSITION_BUILDING]: {
        executionInterval: 120000,
        maxSlippage: 0.1
      },
      [StrategyType.LIQUIDITY_MAINTENANCE]: {
        spreadPercentage: 0.2,
        executionInterval: 30000
      },
      [StrategyType.VOLUME_WASHING]: {
        executionInterval: 180000,
        randomDelay: true
      },
      [StrategyType.HEDGING]: {
        hedgeRatio: 1.0,
        executionInterval: 60000
      },
      [StrategyType.AI_STRATEGY]: {
        modelType: 'lstm',
        executionInterval: 300000
      },
      [StrategyType.DEX_VOLUME_WASHING]: {
        executionInterval: 300000,
        gasLimit: 300000
      },
      [StrategyType.DEX_TREND_FOLLOWING]: {
        timeframe: '1h',
        executionInterval: 300000,
        gasLimit: 300000
      },
      [StrategyType.ACCOUNT_TRACKING]: {
        executionInterval: 60000,
        copyRatio: 1.0
      },
      [StrategyType.DEX_POSITION_BUILDING]: {
        executionInterval: 180000,
        gasLimit: 300000
      },
      [StrategyType.DEX_AI_STRATEGY]: {
        modelType: 'lstm',
        executionInterval: 300000,
        gasLimit: 300000
      }
    };

    return defaultConfigs[type] || {};
  }

  /**
   * 获取策略类型的描述信息
   * @param type 策略类型
   * @returns 描述信息
   */
  getStrategyDescription(type: StrategyType): {
    name: string;
    description: string;
    category: 'cex' | 'dex';
    complexity: 'beginner' | 'intermediate' | 'advanced';
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const descriptions: Record<StrategyType, any> = {
      [StrategyType.GRID]: {
        name: 'Grid Trading',
        description: 'Places buy and sell orders at regular intervals around a set price range',
        category: 'cex',
        complexity: 'beginner',
        riskLevel: 'medium'
      },
      [StrategyType.TREND_FOLLOWING]: {
        name: 'Trend Following',
        description: 'Follows market trends using technical indicators like moving averages',
        category: 'cex',
        complexity: 'intermediate',
        riskLevel: 'medium'
      },
      [StrategyType.POSITION_BUILDING]: {
        name: 'Position Building',
        description: 'Gradually builds positions through small frequent orders',
        category: 'cex',
        complexity: 'beginner',
        riskLevel: 'low'
      },
      [StrategyType.LIQUIDITY_MAINTENANCE]: {
        name: 'Liquidity Maintenance',
        description: 'Maintains liquidity by placing orders within a certain spread',
        category: 'cex',
        complexity: 'intermediate',
        riskLevel: 'low'
      },
      [StrategyType.VOLUME_WASHING]: {
        name: 'Volume Washing',
        description: 'Creates artificial trading volume through coordinated trades',
        category: 'cex',
        complexity: 'advanced',
        riskLevel: 'high'
      },
      [StrategyType.HEDGING]: {
        name: 'Hedging',
        description: 'Automatically hedges positions to reduce risk',
        category: 'cex',
        complexity: 'advanced',
        riskLevel: 'low'
      },
      [StrategyType.AI_STRATEGY]: {
        name: 'AI Strategy',
        description: 'Uses artificial intelligence to make trading decisions',
        category: 'cex',
        complexity: 'advanced',
        riskLevel: 'high'
      },
      [StrategyType.DEX_VOLUME_WASHING]: {
        name: 'DEX Volume Washing',
        description: 'Creates artificial volume on decentralized exchanges',
        category: 'dex',
        complexity: 'advanced',
        riskLevel: 'high'
      },
      [StrategyType.DEX_TREND_FOLLOWING]: {
        name: 'DEX Trend Following',
        description: 'Follows trends on decentralized exchanges',
        category: 'dex',
        complexity: 'intermediate',
        riskLevel: 'medium'
      },
      [StrategyType.ACCOUNT_TRACKING]: {
        name: 'Account Tracking',
        description: 'Copies trades from specified accounts',
        category: 'dex',
        complexity: 'intermediate',
        riskLevel: 'medium'
      },
      [StrategyType.DEX_POSITION_BUILDING]: {
        name: 'DEX Position Building',
        description: 'Builds positions on decentralized exchanges',
        category: 'dex',
        complexity: 'beginner',
        riskLevel: 'low'
      },
      [StrategyType.DEX_AI_STRATEGY]: {
        name: 'DEX AI Strategy',
        description: 'AI-powered trading on decentralized exchanges',
        category: 'dex',
        complexity: 'advanced',
        riskLevel: 'high'
      }
    };

    return descriptions[type] || {
      name: 'Unknown Strategy',
      description: 'Unknown strategy type',
      category: 'cex',
      complexity: 'beginner',
      riskLevel: 'low'
    };
  }
}

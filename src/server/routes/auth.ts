import express from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcrypt';
import { db } from '../models/database';
import { EncryptionUtils } from '../utils/encryption';
import { logger, logUtils } from '../utils/logger';
import { APIResponse, User, UserRole } from '../../shared/types';

const router = express.Router();

// 注册
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.password) {
      throw new Error('Password confirmation does not match password');
    }
    return true;
  })
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const { email, password } = req.body;

    // 检查用户是否已存在
    const existingUser = await db.query(
      'SELECT id FROM users WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'User already exists'
      } as APIResponse);
    }

    // 哈希密码
    const passwordHash = await EncryptionUtils.hashPassword(password);

    // 创建用户
    const result = await db.query(
      `INSERT INTO users (email, password_hash, role, is_active, total_rewards) 
       VALUES ($1, $2, $3, $4, $5) 
       RETURNING id, email, role, is_active, created_at`,
      [email, passwordHash, UserRole.USER, true, 0]
    );

    const user = result.rows[0];

    // 生成JWT token
    const token = EncryptionUtils.generateJWTToken({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    logUtils.logUserAction(user.id, 'register', { email });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          isActive: user.is_active,
          createdAt: user.created_at
        },
        token
      }
    } as APIResponse);

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
});

// 登录
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const { email, password } = req.body;

    // 查找用户
    const result = await db.query(
      'SELECT id, email, password_hash, role, is_active, subscription_expiry FROM users WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      } as APIResponse);
    }

    const user = result.rows[0];

    // 检查用户是否激活
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      } as APIResponse);
    }

    // 验证密码
    const isPasswordValid = await EncryptionUtils.verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      logUtils.logSecurityEvent('failed_login', { email, ip: req.ip });
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      } as APIResponse);
    }

    // 生成JWT token
    const token = EncryptionUtils.generateJWTToken({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    // 更新最后登录时间
    await db.query(
      'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );

    logUtils.logUserAction(user.id, 'login', { email, ip: req.ip });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          isActive: user.is_active,
          subscriptionExpiry: user.subscription_expiry
        },
        token
      }
    } as APIResponse);

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
});

// 刷新token
router.post('/refresh', async (req: express.Request, res: express.Response) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      } as APIResponse);
    }

    // 验证token
    const decoded = EncryptionUtils.verifyJWTToken(token);
    
    // 检查用户是否仍然存在且激活
    const result = await db.query(
      'SELECT id, email, role, is_active FROM users WHERE id = $1 AND is_active = true',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found or deactivated'
      } as APIResponse);
    }

    const user = result.rows[0];

    // 生成新token
    const newToken = EncryptionUtils.generateJWTToken({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken
      }
    } as APIResponse);

  } catch (error) {
    if (error.message === 'Token expired' || error.message === 'Invalid token') {
      return res.status(401).json({
        success: false,
        message: error.message
      } as APIResponse);
    }

    logger.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
});

// 登出
router.post('/logout', async (req: express.Request, res: express.Response) => {
  try {
    // 在实际应用中，这里可以将token加入黑名单
    // 目前只是返回成功响应
    
    res.json({
      success: true,
      message: 'Logout successful'
    } as APIResponse);

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
});

// 忘记密码
router.post('/forgot-password', [
  body('email').isEmail().normalizeEmail()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const { email } = req.body;

    // 检查用户是否存在
    const result = await db.query(
      'SELECT id FROM users WHERE email = $1 AND is_active = true',
      [email]
    );

    // 无论用户是否存在，都返回成功响应（安全考虑）
    res.json({
      success: true,
      message: 'If the email exists, a password reset link has been sent'
    } as APIResponse);

    // 如果用户存在，发送重置邮件（这里需要实现邮件服务）
    if (result.rows.length > 0) {
      const userId = result.rows[0].id;
      // TODO: 实现邮件发送逻辑
      logUtils.logUserAction(userId, 'password_reset_requested', { email });
    }

  } catch (error) {
    logger.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    } as APIResponse);
  }
});

module.exports = router;

import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken } from '../middleware/auth';
import { RewardService } from '../rewards/services/RewardService';
import { logger } from '../utils/logger';
import { APIResponse } from '../../shared/types';
import { RewardType, TokenType, RewardStatus } from '../rewards/base/RewardInterface';

const router = express.Router();
const rewardService = RewardService.getInstance();

// 应用认证中间件
router.use(authenticateToken);

// 获取用户奖励列表
router.get('/', [
  query('status').optional().isIn(Object.values(RewardStatus)),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 })
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const status = req.query.status as RewardStatus;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const rewards = await rewardService.getUserRewards(userId, status, limit, offset);

    res.json({
      success: true,
      data: rewards
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting user rewards:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get rewards'
    } as APIResponse);
  }
});

// 获取用户奖励统计
router.get('/stats', async (req: express.Request, res: express.Response) => {
  try {
    const userId = req.user!.id;
    const stats = await rewardService.getUserRewardStats(userId);

    res.json({
      success: true,
      data: stats
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting reward stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get reward statistics'
    } as APIResponse);
  }
});

// 获取单个奖励详情
router.get('/:rewardId', [
  param('rewardId').isUUID()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const rewardId = req.params.rewardId;
    const reward = await rewardService.getReward(rewardId);

    if (!reward) {
      return res.status(404).json({
        success: false,
        message: 'Reward not found'
      } as APIResponse);
    }

    // 检查权限：只能查看自己的奖励
    if (reward.userId !== req.user!.id && req.user!.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      } as APIResponse);
    }

    res.json({
      success: true,
      data: reward
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting reward:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get reward'
    } as APIResponse);
  }
});

// 领取奖励
router.post('/:rewardId/claim', [
  param('rewardId').isUUID()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const rewardId = req.params.rewardId;
    const userId = req.user!.id;

    // 验证奖励归属
    const reward = await rewardService.getReward(rewardId);
    if (!reward) {
      return res.status(404).json({
        success: false,
        message: 'Reward not found'
      } as APIResponse);
    }

    if (reward.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      } as APIResponse);
    }

    if (reward.status === RewardStatus.DISTRIBUTED) {
      return res.status(400).json({
        success: false,
        message: 'Reward already claimed'
      } as APIResponse);
    }

    // 分发奖励
    const result = await rewardService.distributeReward(rewardId);

    res.json({
      success: result.success,
      message: result.message,
      data: result
    } as APIResponse);
  } catch (error) {
    logger.error('Error claiming reward:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to claim reward'
    } as APIResponse);
  }
});

// 获取推荐统计
router.get('/referral/stats', async (req: express.Request, res: express.Response) => {
  try {
    const userId = req.user!.id;
    const stats = await rewardService.getReferralStats(userId);

    res.json({
      success: true,
      data: stats
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting referral stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get referral statistics'
    } as APIResponse);
  }
});

// 处理推荐
router.post('/referral', [
  body('refereeId').isUUID()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const referrerId = req.user!.id;
    const { refereeId } = req.body;

    if (referrerId === refereeId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot refer yourself'
      } as APIResponse);
    }

    const rewards = await rewardService.processReferral(referrerId, refereeId);

    res.json({
      success: true,
      message: 'Referral processed successfully',
      data: rewards
    } as APIResponse);
  } catch (error) {
    logger.error('Error processing referral:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process referral'
    } as APIResponse);
  }
});

// 获取用户成就
router.get('/achievements/user', async (req: express.Request, res: express.Response) => {
  try {
    const userId = req.user!.id;
    const achievements = await rewardService.getUserAchievements(userId);

    res.json({
      success: true,
      data: achievements
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting user achievements:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get achievements'
    } as APIResponse);
  }
});

// 检查新成就
router.post('/achievements/check', async (req: express.Request, res: express.Response) => {
  try {
    const userId = req.user!.id;
    const newAchievements = await rewardService.checkAchievements(userId);

    // 为新解锁的成就创建奖励
    const rewards = [];
    for (const achievement of newAchievements) {
      const achievementRewards = await rewardService.unlockAchievement(userId, achievement.id);
      rewards.push(...achievementRewards);
    }

    res.json({
      success: true,
      data: {
        newAchievements,
        rewards
      }
    } as APIResponse);
  } catch (error) {
    logger.error('Error checking achievements:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check achievements'
    } as APIResponse);
  }
});

// 获取活动列表
router.get('/campaigns', [
  query('active').optional().isBoolean()
], async (req: express.Request, res: express.Response) => {
  try {
    const isActive = req.query.active === 'true' ? true : undefined;
    const campaigns = await rewardService.getCampaigns(isActive);

    res.json({
      success: true,
      data: campaigns
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get campaigns'
    } as APIResponse);
  }
});

// 管理员路由
// 获取系统奖励统计
router.get('/admin/stats', async (req: express.Request, res: express.Response) => {
  try {
    if (req.user!.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      } as APIResponse);
    }

    const stats = await rewardService.getSystemStats();

    res.json({
      success: true,
      data: stats
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting system stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system statistics'
    } as APIResponse);
  }
});

// 批量分发奖励
router.post('/admin/distribute', [
  body('rewardIds').isArray().notEmpty(),
  body('rewardIds.*').isUUID()
], async (req: express.Request, res: express.Response) => {
  try {
    if (req.user!.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      } as APIResponse);
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const { rewardIds } = req.body;
    const results = await rewardService.batchDistributeRewards(rewardIds);

    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    res.json({
      success: true,
      message: `Distributed ${successCount} rewards, ${failCount} failed`,
      data: results
    } as APIResponse);
  } catch (error) {
    logger.error('Error batch distributing rewards:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to distribute rewards'
    } as APIResponse);
  }
});

// 导出奖励数据
router.get('/admin/export', [
  query('startDate').isISO8601(),
  query('endDate').isISO8601()
], async (req: express.Request, res: express.Response) => {
  try {
    if (req.user!.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      } as APIResponse);
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const startDate = new Date(req.query.startDate as string);
    const endDate = new Date(req.query.endDate as string);

    const data = await rewardService.exportRewards(startDate, endDate);

    res.json({
      success: true,
      data
    } as APIResponse);
  } catch (error) {
    logger.error('Error exporting rewards:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export rewards'
    } as APIResponse);
  }
});

// 创建手动奖励（管理员）
router.post('/admin/create', [
  body('userId').isUUID(),
  body('type').isIn(Object.values(RewardType)),
  body('amount').isFloat({ min: 0 }),
  body('tokenType').isIn(Object.values(TokenType)),
  body('reason').isString().notEmpty()
], async (req: express.Request, res: express.Response) => {
  try {
    if (req.user!.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      } as APIResponse);
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const { userId, type, amount, tokenType, reason } = req.body;

    const reward = await rewardService.createReward(
      userId,
      type,
      amount,
      tokenType,
      reason,
      { createdBy: req.user!.id, manual: true }
    );

    res.json({
      success: true,
      message: 'Reward created successfully',
      data: reward
    } as APIResponse);
  } catch (error) {
    logger.error('Error creating manual reward:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create reward'
    } as APIResponse);
  }
});

module.exports = router;

import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken } from '../middleware/auth';
import { ExchangeService } from '../services/exchangeService';
import { APIKeyService } from '../services/apiKeyService';
import { ExchangeFactory } from '../exchanges/ExchangeFactory';
import { logger } from '../utils/logger';
import { CEXExchange, APIResponse } from '../../shared/types';

const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 获取支持的交易所列表
router.get('/supported', async (req: express.Request, res: express.Response) => {
  try {
    const supportedExchanges = ExchangeFactory.getSupportedExchanges();
    
    res.json({
      success: true,
      data: supportedExchanges.map(exchange => ({
        name: exchange,
        displayName: exchange.charAt(0).toUpperCase() + exchange.slice(1),
        isSupported: true
      }))
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting supported exchanges:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get supported exchanges'
    } as APIResponse);
  }
});

// 获取用户已连接的交易所
router.get('/connected', async (req: express.Request, res: express.Response) => {
  try {
    const userId = req.user!.id;
    const connectedExchanges = ExchangeService.getUserConnectedExchanges(userId);
    const userApiKeys = await APIKeyService.getUserAPIKeys(userId);
    
    const exchangeStatus = userApiKeys.map(apiKey => ({
      exchange: apiKey.exchange,
      isConnected: connectedExchanges.includes(apiKey.exchange),
      isActive: apiKey.isActive,
      createdAt: apiKey.createdAt
    }));
    
    res.json({
      success: true,
      data: exchangeStatus
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting connected exchanges:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get connected exchanges'
    } as APIResponse);
  }
});

// 测试交易所连接
router.post('/test/:exchange', [
  param('exchange').isIn(Object.values(CEXExchange))
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    
    const result = await ExchangeService.testUserExchangeConnection(userId, exchange);
    
    res.json({
      success: result.success,
      message: result.message,
      data: result.accountInfo
    } as APIResponse);
  } catch (error) {
    logger.error('Error testing exchange connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test exchange connection'
    } as APIResponse);
  }
});

// 获取账户信息
router.get('/:exchange/account', [
  param('exchange').isIn(Object.values(CEXExchange))
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    
    const exchangeInstance = await ExchangeService.getUserExchange(userId, exchange);
    const accountInfo = await exchangeInstance.getAccountInfo();
    
    res.json({
      success: true,
      data: accountInfo
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting account info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get account information'
    } as APIResponse);
  }
});

// 获取余额
router.get('/:exchange/balances', [
  param('exchange').isIn(Object.values(CEXExchange)),
  query('asset').optional().isString()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    const asset = req.query.asset as string;
    
    const balances = await ExchangeService.getUserBalance(userId, exchange, asset);
    
    res.json({
      success: true,
      data: balances
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting balances:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get balances'
    } as APIResponse);
  }
});

// 获取交易对列表
router.get('/:exchange/symbols', [
  param('exchange').isIn(Object.values(CEXExchange))
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    
    const symbols = await ExchangeService.getSymbols(userId, exchange);
    
    res.json({
      success: true,
      data: symbols
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting symbols:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get symbols'
    } as APIResponse);
  }
});

// 获取行情数据
router.get('/:exchange/ticker/:symbol', [
  param('exchange').isIn(Object.values(CEXExchange)),
  param('symbol').isString().notEmpty()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    const symbol = req.params.symbol;
    
    const ticker = await ExchangeService.getTicker(userId, exchange, symbol);
    
    res.json({
      success: true,
      data: ticker
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting ticker:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get ticker data'
    } as APIResponse);
  }
});

// 获取开放订单
router.get('/:exchange/orders/open', [
  param('exchange').isIn(Object.values(CEXExchange)),
  query('symbol').optional().isString()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    const symbol = req.query.symbol as string;
    
    const orders = await ExchangeService.getOpenOrders(userId, exchange, symbol);
    
    res.json({
      success: true,
      data: orders
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting open orders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get open orders'
    } as APIResponse);
  }
});

// 获取订单历史
router.get('/:exchange/orders/history', [
  param('exchange').isIn(Object.values(CEXExchange)),
  query('symbol').optional().isString(),
  query('limit').optional().isInt({ min: 1, max: 1000 })
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    const symbol = req.query.symbol as string;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
    
    const orders = await ExchangeService.getOrderHistory(userId, exchange, symbol, limit);
    
    res.json({
      success: true,
      data: orders
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting order history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get order history'
    } as APIResponse);
  }
});

// 获取交易费率
router.get('/:exchange/fees', [
  param('exchange').isIn(Object.values(CEXExchange)),
  query('symbol').optional().isString()
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    const symbol = req.query.symbol as string;
    
    const fees = await ExchangeService.getTradingFees(userId, exchange, symbol);
    
    res.json({
      success: true,
      data: fees
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting trading fees:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trading fees'
    } as APIResponse);
  }
});

// 断开交易所连接
router.post('/:exchange/disconnect', [
  param('exchange').isIn(Object.values(CEXExchange))
], async (req: express.Request, res: express.Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      } as APIResponse);
    }

    const userId = req.user!.id;
    const exchange = req.params.exchange as CEXExchange;
    
    await ExchangeService.disconnectUserExchange(userId, exchange);
    
    res.json({
      success: true,
      message: `Disconnected from ${exchange}`
    } as APIResponse);
  } catch (error) {
    logger.error('Error disconnecting exchange:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to disconnect exchange'
    } as APIResponse);
  }
});

// 获取系统统计信息（管理员功能）
router.get('/admin/stats', async (req: express.Request, res: express.Response) => {
  try {
    // 检查管理员权限
    if (req.user!.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      } as APIResponse);
    }

    const stats = ExchangeService.getSystemStats();
    
    res.json({
      success: true,
      data: stats
    } as APIResponse);
  } catch (error) {
    logger.error('Error getting system stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system statistics'
    } as APIResponse);
  }
});

module.exports = router;

import { CEXExchange, OrderType, OrderStatus } from '../../../shared/types';

// 交易所账户信息
export interface ExchangeAccountInfo {
  balances: ExchangeBalance[];
  permissions: string[];
  tradingEnabled: boolean;
  withdrawEnabled: boolean;
}

// 余额信息
export interface ExchangeBalance {
  asset: string;
  free: number;
  locked: number;
  total: number;
}

// 订单信息
export interface ExchangeOrder {
  id: string;
  clientOrderId?: string;
  symbol: string;
  type: OrderType;
  side: 'buy' | 'sell';
  amount: number;
  price?: number;
  status: OrderStatus;
  filled: number;
  remaining: number;
  cost: number;
  fee: number;
  feeAsset: string;
  timestamp: number;
  lastTradeTimestamp?: number;
}

// 市场数据
export interface ExchangeTicker {
  symbol: string;
  bid: number;
  ask: number;
  last: number;
  high: number;
  low: number;
  volume: number;
  quoteVolume: number;
  change: number;
  percentage: number;
  timestamp: number;
}

// K线数据
export interface ExchangeKline {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// 订单簿
export interface ExchangeOrderBook {
  symbol: string;
  bids: [number, number][]; // [price, amount]
  asks: [number, number][]; // [price, amount]
  timestamp: number;
}

// 交易记录
export interface ExchangeTrade {
  id: string;
  orderId: string;
  symbol: string;
  side: 'buy' | 'sell';
  amount: number;
  price: number;
  cost: number;
  fee: number;
  feeAsset: string;
  timestamp: number;
}

// 创建订单参数
export interface CreateOrderParams {
  symbol: string;
  type: OrderType;
  side: 'buy' | 'sell';
  amount: number;
  price?: number;
  stopPrice?: number;
  timeInForce?: 'GTC' | 'IOC' | 'FOK';
  clientOrderId?: string;
}

// 交易所配置
export interface ExchangeConfig {
  apiKey: string;
  secretKey: string;
  passphrase?: string;
  sandbox?: boolean;
  rateLimit?: number;
  timeout?: number;
}

// 交易所接口
export interface IExchange {
  // 基础信息
  readonly name: CEXExchange;
  readonly isConnected: boolean;
  
  // 初始化和连接
  initialize(config: ExchangeConfig): Promise<void>;
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  testConnection(): Promise<boolean>;
  
  // 账户信息
  getAccountInfo(): Promise<ExchangeAccountInfo>;
  getBalances(): Promise<ExchangeBalance[]>;
  getBalance(asset: string): Promise<ExchangeBalance>;
  
  // 市场数据
  getTicker(symbol: string): Promise<ExchangeTicker>;
  getTickers(symbols?: string[]): Promise<ExchangeTicker[]>;
  getOrderBook(symbol: string, limit?: number): Promise<ExchangeOrderBook>;
  getKlines(symbol: string, interval: string, limit?: number, startTime?: number, endTime?: number): Promise<ExchangeKline[]>;
  
  // 订单管理
  createOrder(params: CreateOrderParams): Promise<ExchangeOrder>;
  cancelOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
  cancelAllOrders(symbol?: string): Promise<ExchangeOrder[]>;
  getOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
  getOpenOrders(symbol?: string): Promise<ExchangeOrder[]>;
  getOrderHistory(symbol?: string, limit?: number): Promise<ExchangeOrder[]>;
  
  // 交易记录
  getTrades(symbol?: string, limit?: number): Promise<ExchangeTrade[]>;
  getMyTrades(symbol: string, limit?: number): Promise<ExchangeTrade[]>;
  
  // 工具方法
  getSymbols(): Promise<string[]>;
  getSymbolInfo(symbol: string): Promise<any>;
  formatSymbol(base: string, quote: string): string;
  parseSymbol(symbol: string): { base: string; quote: string };
  
  // 费率信息
  getTradingFees(symbol?: string): Promise<{ maker: number; taker: number }>;
  
  // WebSocket 支持
  subscribeToTicker?(symbol: string, callback: (ticker: ExchangeTicker) => void): void;
  subscribeToOrderBook?(symbol: string, callback: (orderBook: ExchangeOrderBook) => void): void;
  subscribeToTrades?(symbol: string, callback: (trade: ExchangeTrade) => void): void;
  subscribeToOrders?(callback: (order: ExchangeOrder) => void): void;
  
  unsubscribeFromTicker?(symbol: string): void;
  unsubscribeFromOrderBook?(symbol: string): void;
  unsubscribeFromTrades?(symbol: string): void;
  unsubscribeFromOrders?(): void;
}

// 交易所错误类
export class ExchangeError extends Error {
  constructor(
    message: string,
    public exchange: CEXExchange,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'ExchangeError';
  }
}

// 网络错误
export class NetworkError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange) {
    super(message, exchange, 'NETWORK_ERROR');
    this.name = 'NetworkError';
  }
}

// 认证错误
export class AuthenticationError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange) {
    super(message, exchange, 'AUTH_ERROR', 401);
    this.name = 'AuthenticationError';
  }
}

// 权限错误
export class PermissionError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange) {
    super(message, exchange, 'PERMISSION_ERROR', 403);
    this.name = 'PermissionError';
  }
}

// 速率限制错误
export class RateLimitError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange) {
    super(message, exchange, 'RATE_LIMIT_ERROR', 429);
    this.name = 'RateLimitError';
  }
}

// 余额不足错误
export class InsufficientBalanceError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange) {
    super(message, exchange, 'INSUFFICIENT_BALANCE');
    this.name = 'InsufficientBalanceError';
  }
}

// 订单错误
export class OrderError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange, code?: string) {
    super(message, exchange, code || 'ORDER_ERROR');
    this.name = 'OrderError';
  }
}

// 市场错误
export class MarketError extends ExchangeError {
  constructor(message: string, exchange: CEXExchange) {
    super(message, exchange, 'MARKET_ERROR');
    this.name = 'MarketError';
  }
}

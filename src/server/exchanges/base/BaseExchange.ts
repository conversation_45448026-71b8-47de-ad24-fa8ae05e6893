import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import crypto from 'crypto';
import { EventEmitter } from 'events';
import { logger } from '../../utils/logger';
import { CEXExchange } from '../../../shared/types';
import {
  IExchange,
  ExchangeConfig,
  ExchangeError,
  NetworkError,
  AuthenticationError,
  RateLimitError,
  ExchangeAccountInfo,
  ExchangeBalance,
  ExchangeOrder,
  ExchangeTicker,
  ExchangeOrderBook,
  ExchangeKline,
  ExchangeTrade,
  CreateOrderParams
} from './ExchangeInterface';

export abstract class BaseExchange extends EventEmitter implements IExchange {
  public readonly name: CEXExchange;
  protected config: ExchangeConfig;
  protected httpClient: AxiosInstance;
  protected _isConnected: boolean = false;
  protected rateLimiter: Map<string, number> = new Map();
  protected lastRequestTime: number = 0;

  constructor(name: CEXExchange) {
    super();
    this.name = name;
  }

  get isConnected(): boolean {
    return this._isConnected;
  }

  // 抽象方法，子类必须实现
  protected abstract getBaseUrl(): string;
  protected abstract signRequest(method: string, path: string, params?: any, body?: any): any;
  protected abstract getHeaders(signature?: any): Record<string, string>;

  // 初始化交易所
  async initialize(config: ExchangeConfig): Promise<void> {
    this.config = config;
    
    this.httpClient = axios.create({
      baseURL: this.getBaseUrl(),
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TradeAI-Bot/1.0'
      }
    });

    // 添加请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.enforceRateLimit();
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 添加响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => response,
      (error) => {
        this.handleHttpError(error);
        return Promise.reject(error);
      }
    );

    logger.info(`${this.name} exchange initialized`);
  }

  // 连接到交易所
  async connect(): Promise<void> {
    try {
      const isValid = await this.testConnection();
      if (isValid) {
        this._isConnected = true;
        this.emit('connected');
        logger.info(`Connected to ${this.name} exchange`);
      } else {
        throw new AuthenticationError('Invalid API credentials', this.name);
      }
    } catch (error) {
      this._isConnected = false;
      this.emit('error', error);
      throw error;
    }
  }

  // 断开连接
  async disconnect(): Promise<void> {
    this._isConnected = false;
    this.emit('disconnected');
    logger.info(`Disconnected from ${this.name} exchange`);
  }

  // 测试连接
  async testConnection(): Promise<boolean> {
    try {
      await this.getAccountInfo();
      return true;
    } catch (error) {
      logger.error(`Connection test failed for ${this.name}:`, error);
      return false;
    }
  }

  // 速率限制
  protected enforceRateLimit(): void {
    const now = Date.now();
    const minInterval = this.config.rateLimit || 100; // 默认100ms间隔
    
    if (now - this.lastRequestTime < minInterval) {
      const delay = minInterval - (now - this.lastRequestTime);
      // 简单的同步延迟，实际应用中可能需要更复杂的实现
      const start = Date.now();
      while (Date.now() - start < delay) {
        // 忙等待
      }
    }
    
    this.lastRequestTime = Date.now();
  }

  // 处理HTTP错误
  protected handleHttpError(error: any): void {
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.msg || error.response.data?.message || error.message;
      
      switch (status) {
        case 401:
          throw new AuthenticationError(message, this.name);
        case 429:
          throw new RateLimitError(message, this.name);
        default:
          throw new ExchangeError(message, this.name, 'HTTP_ERROR', status);
      }
    } else if (error.request) {
      throw new NetworkError('Network error', this.name);
    } else {
      throw new ExchangeError(error.message, this.name);
    }
  }

  // 发送认证请求
  protected async makeAuthenticatedRequest(
    method: string,
    path: string,
    params?: any,
    body?: any
  ): Promise<any> {
    const signature = this.signRequest(method, path, params, body);
    const headers = this.getHeaders(signature);
    
    const config: AxiosRequestConfig = {
      method: method.toLowerCase() as any,
      url: path,
      headers,
      params,
      data: body
    };

    try {
      const response = await this.httpClient.request(config);
      return response.data;
    } catch (error) {
      logger.error(`${this.name} API request failed:`, error);
      throw error;
    }
  }

  // 发送公开请求
  protected async makePublicRequest(
    method: string,
    path: string,
    params?: any
  ): Promise<any> {
    try {
      const response = await this.httpClient.request({
        method: method.toLowerCase() as any,
        url: path,
        params
      });
      return response.data;
    } catch (error) {
      logger.error(`${this.name} public API request failed:`, error);
      throw error;
    }
  }

  // 生成HMAC签名
  protected generateHmacSignature(
    message: string,
    secret: string,
    algorithm: string = 'sha256'
  ): string {
    return crypto
      .createHmac(algorithm, secret)
      .update(message)
      .digest('hex');
  }

  // 生成查询字符串
  protected buildQueryString(params: Record<string, any>): string {
    return Object.keys(params)
      .sort()
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
  }

  // 格式化数字
  protected formatNumber(num: number, precision: number = 8): number {
    return parseFloat(num.toFixed(precision));
  }

  // 解析时间戳
  protected parseTimestamp(timestamp: any): number {
    if (typeof timestamp === 'string') {
      return new Date(timestamp).getTime();
    }
    if (typeof timestamp === 'number') {
      // 如果是秒级时间戳，转换为毫秒
      return timestamp < 1e12 ? timestamp * 1000 : timestamp;
    }
    return Date.now();
  }

  // 抽象方法，子类需要实现具体的API调用
  abstract getAccountInfo(): Promise<ExchangeAccountInfo>;
  abstract getBalances(): Promise<ExchangeBalance[]>;
  abstract getBalance(asset: string): Promise<ExchangeBalance>;
  abstract getTicker(symbol: string): Promise<ExchangeTicker>;
  abstract getTickers(symbols?: string[]): Promise<ExchangeTicker[]>;
  abstract getOrderBook(symbol: string, limit?: number): Promise<ExchangeOrderBook>;
  abstract getKlines(symbol: string, interval: string, limit?: number, startTime?: number, endTime?: number): Promise<ExchangeKline[]>;
  abstract createOrder(params: CreateOrderParams): Promise<ExchangeOrder>;
  abstract cancelOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
  abstract cancelAllOrders(symbol?: string): Promise<ExchangeOrder[]>;
  abstract getOrder(orderId: string, symbol: string): Promise<ExchangeOrder>;
  abstract getOpenOrders(symbol?: string): Promise<ExchangeOrder[]>;
  abstract getOrderHistory(symbol?: string, limit?: number): Promise<ExchangeOrder[]>;
  abstract getTrades(symbol?: string, limit?: number): Promise<ExchangeTrade[]>;
  abstract getMyTrades(symbol: string, limit?: number): Promise<ExchangeTrade[]>;
  abstract getSymbols(): Promise<string[]>;
  abstract getSymbolInfo(symbol: string): Promise<any>;
  abstract formatSymbol(base: string, quote: string): string;
  abstract parseSymbol(symbol: string): { base: string; quote: string };
  abstract getTradingFees(symbol?: string): Promise<{ maker: number; taker: number }>;
}

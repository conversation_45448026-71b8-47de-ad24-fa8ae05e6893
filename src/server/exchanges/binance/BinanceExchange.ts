import { BaseExchange } from '../base/BaseExchange';
import { CEXExchange, OrderType, OrderStatus } from '../../../shared/types';
import {
  ExchangeAccountInfo,
  ExchangeBalance,
  ExchangeOrder,
  ExchangeTicker,
  ExchangeOrderBook,
  ExchangeKline,
  ExchangeTrade,
  CreateOrderParams,
  ExchangeError,
  OrderError,
  InsufficientBalanceError
} from '../base/ExchangeInterface';

export class BinanceExchange extends BaseExchange {
  constructor() {
    super(CEXExchange.BINANCE);
  }

  protected getBaseUrl(): string {
    return this.config.sandbox 
      ? 'https://testnet.binance.vision'
      : 'https://api.binance.com';
  }

  protected signRequest(method: string, path: string, params?: any, body?: any): string {
    const timestamp = Date.now();
    const queryString = params ? this.buildQueryString({ ...params, timestamp }) : `timestamp=${timestamp}`;
    
    return this.generateHmacSignature(queryString, this.config.secretKey);
  }

  protected getHeaders(signature?: string): Record<string, string> {
    const headers: Record<string, string> = {
      'X-MBX-APIKEY': this.config.apiKey
    };
    
    if (signature) {
      headers['signature'] = signature;
    }
    
    return headers;
  }

  // 获取账户信息
  async getAccountInfo(): Promise<ExchangeAccountInfo> {
    const data = await this.makeAuthenticatedRequest('GET', '/api/v3/account');
    
    return {
      balances: data.balances.map((balance: any) => ({
        asset: balance.asset,
        free: parseFloat(balance.free),
        locked: parseFloat(balance.locked),
        total: parseFloat(balance.free) + parseFloat(balance.locked)
      })),
      permissions: data.permissions || [],
      tradingEnabled: data.canTrade || false,
      withdrawEnabled: data.canWithdraw || false
    };
  }

  // 获取所有余额
  async getBalances(): Promise<ExchangeBalance[]> {
    const accountInfo = await this.getAccountInfo();
    return accountInfo.balances.filter(balance => balance.total > 0);
  }

  // 获取单个资产余额
  async getBalance(asset: string): Promise<ExchangeBalance> {
    const balances = await this.getBalances();
    const balance = balances.find(b => b.asset === asset.toUpperCase());
    
    if (!balance) {
      return {
        asset: asset.toUpperCase(),
        free: 0,
        locked: 0,
        total: 0
      };
    }
    
    return balance;
  }

  // 获取单个交易对行情
  async getTicker(symbol: string): Promise<ExchangeTicker> {
    const data = await this.makePublicRequest('GET', '/api/v3/ticker/24hr', { symbol });
    
    return {
      symbol: data.symbol,
      bid: parseFloat(data.bidPrice),
      ask: parseFloat(data.askPrice),
      last: parseFloat(data.lastPrice),
      high: parseFloat(data.highPrice),
      low: parseFloat(data.lowPrice),
      volume: parseFloat(data.volume),
      quoteVolume: parseFloat(data.quoteVolume),
      change: parseFloat(data.priceChange),
      percentage: parseFloat(data.priceChangePercent),
      timestamp: parseInt(data.closeTime)
    };
  }

  // 获取多个交易对行情
  async getTickers(symbols?: string[]): Promise<ExchangeTicker[]> {
    const params = symbols ? { symbols: JSON.stringify(symbols) } : {};
    const data = await this.makePublicRequest('GET', '/api/v3/ticker/24hr', params);
    
    const tickers = Array.isArray(data) ? data : [data];
    
    return tickers.map((ticker: any) => ({
      symbol: ticker.symbol,
      bid: parseFloat(ticker.bidPrice),
      ask: parseFloat(ticker.askPrice),
      last: parseFloat(ticker.lastPrice),
      high: parseFloat(ticker.highPrice),
      low: parseFloat(ticker.lowPrice),
      volume: parseFloat(ticker.volume),
      quoteVolume: parseFloat(ticker.quoteVolume),
      change: parseFloat(ticker.priceChange),
      percentage: parseFloat(ticker.priceChangePercent),
      timestamp: parseInt(ticker.closeTime)
    }));
  }

  // 获取订单簿
  async getOrderBook(symbol: string, limit: number = 100): Promise<ExchangeOrderBook> {
    const data = await this.makePublicRequest('GET', '/api/v3/depth', { symbol, limit });
    
    return {
      symbol,
      bids: data.bids.map((bid: string[]) => [parseFloat(bid[0]), parseFloat(bid[1])]),
      asks: data.asks.map((ask: string[]) => [parseFloat(ask[0]), parseFloat(ask[1])]),
      timestamp: data.lastUpdateId
    };
  }

  // 获取K线数据
  async getKlines(
    symbol: string,
    interval: string,
    limit: number = 500,
    startTime?: number,
    endTime?: number
  ): Promise<ExchangeKline[]> {
    const params: any = { symbol, interval, limit };
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    
    const data = await this.makePublicRequest('GET', '/api/v3/klines', params);
    
    return data.map((kline: any[]) => ({
      timestamp: kline[0],
      open: parseFloat(kline[1]),
      high: parseFloat(kline[2]),
      low: parseFloat(kline[3]),
      close: parseFloat(kline[4]),
      volume: parseFloat(kline[5])
    }));
  }

  // 创建订单
  async createOrder(params: CreateOrderParams): Promise<ExchangeOrder> {
    const orderParams: any = {
      symbol: params.symbol,
      side: params.side.toUpperCase(),
      type: this.mapOrderType(params.type),
      quantity: params.amount,
      timestamp: Date.now()
    };

    if (params.price) {
      orderParams.price = params.price;
    }

    if (params.stopPrice) {
      orderParams.stopPrice = params.stopPrice;
    }

    if (params.timeInForce) {
      orderParams.timeInForce = params.timeInForce;
    }

    if (params.clientOrderId) {
      orderParams.newClientOrderId = params.clientOrderId;
    }

    try {
      const data = await this.makeAuthenticatedRequest('POST', '/api/v3/order', orderParams);
      return this.parseOrder(data);
    } catch (error) {
      this.handleOrderError(error);
      throw error;
    }
  }

  // 取消订单
  async cancelOrder(orderId: string, symbol: string): Promise<ExchangeOrder> {
    const data = await this.makeAuthenticatedRequest('DELETE', '/api/v3/order', {
      symbol,
      orderId,
      timestamp: Date.now()
    });
    
    return this.parseOrder(data);
  }

  // 取消所有订单
  async cancelAllOrders(symbol?: string): Promise<ExchangeOrder[]> {
    if (!symbol) {
      throw new ExchangeError('Symbol is required for Binance cancelAllOrders', this.name);
    }
    
    const data = await this.makeAuthenticatedRequest('DELETE', '/api/v3/openOrders', {
      symbol,
      timestamp: Date.now()
    });
    
    return data.map((order: any) => this.parseOrder(order));
  }

  // 获取订单信息
  async getOrder(orderId: string, symbol: string): Promise<ExchangeOrder> {
    const data = await this.makeAuthenticatedRequest('GET', '/api/v3/order', {
      symbol,
      orderId,
      timestamp: Date.now()
    });
    
    return this.parseOrder(data);
  }

  // 获取开放订单
  async getOpenOrders(symbol?: string): Promise<ExchangeOrder[]> {
    const params: any = { timestamp: Date.now() };
    if (symbol) params.symbol = symbol;
    
    const data = await this.makeAuthenticatedRequest('GET', '/api/v3/openOrders', params);
    return data.map((order: any) => this.parseOrder(order));
  }

  // 获取订单历史
  async getOrderHistory(symbol?: string, limit: number = 500): Promise<ExchangeOrder[]> {
    if (!symbol) {
      throw new ExchangeError('Symbol is required for Binance order history', this.name);
    }
    
    const data = await this.makeAuthenticatedRequest('GET', '/api/v3/allOrders', {
      symbol,
      limit,
      timestamp: Date.now()
    });
    
    return data.map((order: any) => this.parseOrder(order));
  }

  // 获取交易记录
  async getTrades(symbol?: string, limit: number = 500): Promise<ExchangeTrade[]> {
    if (!symbol) {
      const data = await this.makePublicRequest('GET', '/api/v3/trades', { limit });
      return data.map((trade: any) => this.parseTrade(trade));
    }
    
    const data = await this.makePublicRequest('GET', '/api/v3/trades', { symbol, limit });
    return data.map((trade: any) => this.parseTrade(trade));
  }

  // 获取我的交易记录
  async getMyTrades(symbol: string, limit: number = 500): Promise<ExchangeTrade[]> {
    const data = await this.makeAuthenticatedRequest('GET', '/api/v3/myTrades', {
      symbol,
      limit,
      timestamp: Date.now()
    });
    
    return data.map((trade: any) => this.parseMyTrade(trade));
  }

  // 获取交易对列表
  async getSymbols(): Promise<string[]> {
    const data = await this.makePublicRequest('GET', '/api/v3/exchangeInfo');
    return data.symbols
      .filter((symbol: any) => symbol.status === 'TRADING')
      .map((symbol: any) => symbol.symbol);
  }

  // 获取交易对信息
  async getSymbolInfo(symbol: string): Promise<any> {
    const data = await this.makePublicRequest('GET', '/api/v3/exchangeInfo');
    return data.symbols.find((s: any) => s.symbol === symbol);
  }

  // 格式化交易对
  formatSymbol(base: string, quote: string): string {
    return `${base.toUpperCase()}${quote.toUpperCase()}`;
  }

  // 解析交易对
  parseSymbol(symbol: string): { base: string; quote: string } {
    // 币安的交易对格式比较复杂，需要通过交易所信息来解析
    // 这里提供一个简化版本
    const commonQuotes = ['USDT', 'BUSD', 'BTC', 'ETH', 'BNB'];
    
    for (const quote of commonQuotes) {
      if (symbol.endsWith(quote)) {
        return {
          base: symbol.slice(0, -quote.length),
          quote
        };
      }
    }
    
    throw new ExchangeError(`Cannot parse symbol: ${symbol}`, this.name);
  }

  // 获取交易费率
  async getTradingFees(symbol?: string): Promise<{ maker: number; taker: number }> {
    const data = await this.makeAuthenticatedRequest('GET', '/api/v3/account', {
      timestamp: Date.now()
    });
    
    return {
      maker: data.makerCommission / 10000, // 币安返回的是万分比
      taker: data.takerCommission / 10000
    };
  }

  // 辅助方法
  private mapOrderType(type: OrderType): string {
    const typeMap: Record<OrderType, string> = {
      [OrderType.MARKET]: 'MARKET',
      [OrderType.LIMIT]: 'LIMIT',
      [OrderType.STOP_LOSS]: 'STOP_LOSS',
      [OrderType.TAKE_PROFIT]: 'TAKE_PROFIT'
    };
    
    return typeMap[type] || 'LIMIT';
  }

  private parseOrderStatus(status: string): OrderStatus {
    const statusMap: Record<string, OrderStatus> = {
      'NEW': OrderStatus.PENDING,
      'PARTIALLY_FILLED': OrderStatus.PARTIALLY_FILLED,
      'FILLED': OrderStatus.FILLED,
      'CANCELED': OrderStatus.CANCELLED,
      'REJECTED': OrderStatus.FAILED,
      'EXPIRED': OrderStatus.CANCELLED
    };
    
    return statusMap[status] || OrderStatus.PENDING;
  }

  private parseOrder(data: any): ExchangeOrder {
    return {
      id: data.orderId.toString(),
      clientOrderId: data.clientOrderId,
      symbol: data.symbol,
      type: data.type.toLowerCase() as OrderType,
      side: data.side.toLowerCase() as 'buy' | 'sell',
      amount: parseFloat(data.origQty),
      price: parseFloat(data.price) || undefined,
      status: this.parseOrderStatus(data.status),
      filled: parseFloat(data.executedQty),
      remaining: parseFloat(data.origQty) - parseFloat(data.executedQty),
      cost: parseFloat(data.cummulativeQuoteQty),
      fee: 0, // 需要从交易记录中获取
      feeAsset: '',
      timestamp: data.time,
      lastTradeTimestamp: data.updateTime
    };
  }

  private parseTrade(data: any): ExchangeTrade {
    return {
      id: data.id.toString(),
      orderId: '',
      symbol: data.symbol || '',
      side: data.isBuyerMaker ? 'sell' : 'buy',
      amount: parseFloat(data.qty),
      price: parseFloat(data.price),
      cost: parseFloat(data.quoteQty),
      fee: 0,
      feeAsset: '',
      timestamp: data.time
    };
  }

  private parseMyTrade(data: any): ExchangeTrade {
    return {
      id: data.id.toString(),
      orderId: data.orderId.toString(),
      symbol: data.symbol,
      side: data.isBuyer ? 'buy' : 'sell',
      amount: parseFloat(data.qty),
      price: parseFloat(data.price),
      cost: parseFloat(data.quoteQty),
      fee: parseFloat(data.commission),
      feeAsset: data.commissionAsset,
      timestamp: data.time
    };
  }

  private handleOrderError(error: any): void {
    const message = error.response?.data?.msg || error.message;
    
    if (message.includes('insufficient balance')) {
      throw new InsufficientBalanceError(message, this.name);
    }
    
    throw new OrderError(message, this.name);
  }
}

import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';

// 模拟数据库连接
jest.mock('../server/models/database', () => ({
  db: {
    query: jest.fn(),
    getConnection: jest.fn(),
    setCache: jest.fn(),
    getCache: jest.fn(),
    deleteCache: jest.fn(),
    clearCache: jest.fn(),
    close: jest.fn()
  }
}));

// 全局测试设置
beforeAll(async () => {
  // 在所有测试开始前的设置
});

afterAll(async () => {
  // 在所有测试结束后的清理
});

beforeEach(() => {
  // 在每个测试前重置模拟
  jest.clearAllMocks();
});

afterEach(() => {
  // 在每个测试后的清理
});

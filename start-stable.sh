#!/bin/bash

# TradeAI Bot 稳定启动脚本
# 使用编译后的 JavaScript 避免 TypeScript 问题

set -e

echo "🚀 TradeAI Bot 稳定启动"
echo "======================="

# 清理端口
echo "🧹 清理端口占用..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 修复前端依赖
echo "🔧 修复前端依赖..."
if [ -f "fix-main-app.sh" ]; then
    chmod +x fix-main-app.sh
    ./fix-main-app.sh
fi

# 检查依赖
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi

if [ ! -d "client/node_modules" ]; then
    echo "安装前端依赖..."
    cd client
    npm install
    cd ..
fi

# 尝试编译 TypeScript
echo "🔨 编译 TypeScript..."
if npm run build:server; then
    echo "✅ TypeScript 编译成功"
    
    # 使用编译后的 JavaScript 启动
    echo "🔧 启动编译后的后端..."
    node dist/server/index.js &
    BACKEND_PID=$!
    BACKEND_TYPE="compiled"
else
    echo "⚠️  TypeScript 编译失败，使用简化后端..."
    
    # 使用简化的测试服务器
    node test-server.js &
    BACKEND_PID=$!
    BACKEND_TYPE="simple"
fi

echo "✅ 后端已启动 (PID: $BACKEND_PID, Type: $BACKEND_TYPE)"

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 5

# 验证后端
echo "🔍 验证后端状态..."
for i in {1..10}; do
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ 后端健康检查通过"
        break
    else
        echo "⏳ 等待后端启动... ($i/10)"
        sleep 2
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 后端启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
done

# 启动前端
echo "🎨 启动前端服务器..."
cd client
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 前端已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 8

# 显示访问信息
echo ""
echo "🎉 TradeAI Bot 启动完成！"
echo "========================="
echo ""
echo "📱 前端地址: http://localhost:5173"
echo "🔗 后端地址: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo "🧪 API测试: http://localhost:3001/api"
echo ""
echo "🔧 后端类型: $BACKEND_TYPE"
if [ "$BACKEND_TYPE" = "compiled" ]; then
    echo "  ✅ 使用完整的 TypeScript 编译版本"
    echo "  📚 支持所有 API 端点"
else
    echo "  ⚠️  使用简化版本 (TypeScript 编译失败)"
    echo "  🔧 基础 API 功能可用"
fi
echo ""
echo "🌟 功能页面:"
echo "  📊 仪表板: http://localhost:5173/"
echo "  🔐 登录: http://localhost:5173/login"
echo "  📝 注册: http://localhost:5173/register"
echo ""
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待进程
wait $BACKEND_PID $FRONTEND_PID

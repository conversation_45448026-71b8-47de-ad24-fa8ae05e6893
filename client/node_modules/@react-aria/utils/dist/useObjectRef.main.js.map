{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAaM,SAAS,0CAAgB,GAAuF;IACrH,MAAM,SAAqC,CAAA,GAAA,mBAAK,EAAK;IACrD,MAAM,aAAoD,CAAA,GAAA,mBAAK,EAAE;IAEjE,MAAM,YAAY,CAAA,GAAA,wBAAU,EAC1B,CAAC;QACC,IAAI,OAAO,QAAQ,YAAY;YAC7B,MAAM,cAAc;YACpB,MAAM,aAAa,YAAY;YAC/B,OAAO;gBACL,IAAI,OAAO,eAAe,YACxB;qBAEA,YAAY;YAEhB;QACF,OAAO,IAAI,KAAK;YACd,IAAI,OAAO,GAAG;YACd,OAAO;gBACL,IAAI,OAAO,GAAG;YAChB;QACF;IACF,GACA;QAAC;KAAI;IAGP,OAAO,CAAA,GAAA,oBAAM,EACX,IAAO,CAAA;YACL,IAAI,WAAU;gBACZ,OAAO,OAAO,OAAO;YACvB;YACA,IAAI,SAAQ,MAAO;gBACjB,OAAO,OAAO,GAAG;gBACjB,IAAI,WAAW,OAAO,EAAE;oBACtB,WAAW,OAAO;oBAClB,WAAW,OAAO,GAAG;gBACvB;gBAEA,IAAI,SAAS,MACX,WAAW,OAAO,GAAG,UAAU;YAEnC;QACF,CAAA,GACA;QAAC;KAAU;AAEf", "sources": ["packages/@react-aria/utils/src/useObjectRef.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, useCallback, useMemo, useRef} from 'react';\n\n/**\n * Offers an object ref for a given callback ref or an object ref. Especially\n * helfpul when passing forwarded refs (created using `React.forwardRef`) to\n * React Aria hooks.\n *\n * @param ref The original ref intended to be used.\n * @returns An object ref that updates the given ref.\n * @see https://react.dev/reference/react/forwardRef\n */\nexport function useObjectRef<T>(ref?: ((instance: T | null) => (() => void) | void) | MutableRefObject<T | null> | null): MutableRefObject<T | null> {\n  const objRef: MutableRefObject<T | null> = useRef<T>(null);\n  const cleanupRef: MutableRefObject<(() => void) | void> = useRef(undefined);\n\n  const refEffect = useCallback(\n    (instance: T | null) => {\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return () => {\n          if (typeof refCleanup === 'function') {\n            refCleanup();\n          } else {\n            refCallback(null);\n          }\n        };\n      } else if (ref) {\n        ref.current = instance;\n        return () => {\n          ref.current = null;\n        };\n      }\n    },\n    [ref]\n  );\n\n  return useMemo(\n    () => ({\n      get current() {\n        return objRef.current;\n      },\n      set current(value) {\n        objRef.current = value;\n        if (cleanupRef.current) {\n          cleanupRef.current();\n          cleanupRef.current = undefined;\n        }\n\n        if (value != null) {\n          cleanupRef.current = refEffect(value);\n        }\n      }\n    }),\n    [refEffect]\n  );\n}\n"], "names": [], "version": 3, "file": "useObjectRef.main.js.map"}
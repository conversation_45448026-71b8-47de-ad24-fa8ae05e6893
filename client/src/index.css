/* 简化的 CSS 样式，替代 Tailwind CSS */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

/* 工具类 */
.min-h-screen { min-height: 100vh; }
.bg-gray-100 { background-color: #f7fafc; }
.bg-white { background-color: #ffffff; }
.bg-blue-500 { background-color: #4299e1; }
.bg-blue-600 { background-color: #3182ce; }
.bg-blue-100 { background-color: #ebf8ff; }
.bg-gray-50 { background-color: #f9fafb; }

.text-white { color: #ffffff; }
.text-gray-900 { color: #1a202c; }
.text-gray-800 { color: #2d3748; }
.text-gray-600 { color: #718096; }
.text-gray-500 { color: #a0aec0; }
.text-blue-700 { color: #2b6cb0; }

.text-xl { font-size: 1.25rem; }
.text-lg { font-size: 1.125rem; }
.text-sm { font-size: 0.875rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mt-12 { margin-top: 3rem; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

.border { border: 1px solid #e2e8f0; }
.border-b { border-bottom: 1px solid #e2e8f0; }
.border-t { border-top: 1px solid #e2e8f0; }

.flex { display: flex; }
.grid { display: grid; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }

.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.h-16 { height: 4rem; }

.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }

.hover\:bg-blue-600:hover { background-color: #3182ce; }
.hover\:text-gray-700:hover { color: #4a5568; }

.text-center { text-align: center; }

.list-disc { list-style-type: disc; }
.list-inside { list-style-position: inside; }

.font-mono { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; }

/* 响应式 */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:px-0 { padding-left: 0; padding-right: 0; }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* 自定义组件样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-transparent hover:bg-gray-50 active:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800 dark:active:bg-gray-700;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
  }

  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700 dark:text-gray-300;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }

  .badge-danger {
    @apply badge bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }

  .badge-info {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }
}

import React, { useState } from 'react';

// 简单的图标组件，替代 Heroicons
const SimpleIcon: React.FC<{ type: string; className?: string }> = ({ type, className = "w-5 h-5" }) => {
  const icons = {
    chart: "📊",
    dollar: "💰", 
    up: "📈",
    down: "📉",
    play: "▶️",
    pause: "⏸️",
    settings: "⚙️",
    user: "👤"
  };
  
  return <span className={`inline-block ${className}`}>{icons[type as keyof typeof icons] || "📋"}</span>;
};

// 简单的卡片组件
const StatCard: React.FC<{
  title: string;
  value: string | number;
  change?: number;
  icon: string;
  color?: string;
}> = ({ title, value, change, icon, color = 'blue' }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <SimpleIcon type={icon} className="w-8 h-8" />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">{value}</div>
              {change !== undefined && (
                <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                  change >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  <SimpleIcon type={change >= 0 ? 'up' : 'down'} className="w-4 h-4" />
                  <span className="sr-only">{change >= 0 ? 'Increased' : 'Decreased'} by</span>
                  {Math.abs(change)}%
                </div>
              )}
            </dd>
          </dl>
        </div>
      </div>
    </div>
  );
};

const SimpleDashboard: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<string>('未检查');
  const [stats, setStats] = useState({
    totalBalance: 12500,
    totalProfit: 1250,
    activeStrategies: 3,
    winRate: 68.5,
    dailyPnL: 2.3
  });

  // 测试后端连接
  const testBackend = async () => {
    try {
      setApiStatus('检查中...');
      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();
      setApiStatus(`✅ 连接成功 - ${data.status}`);
    } catch (error) {
      setApiStatus('❌ 连接失败');
    }
  };

  // 测试API
  const testAPI = async () => {
    try {
      const response = await fetch('http://localhost:3001/api');
      const data = await response.json();
      alert(`API测试成功: ${data.message}`);
    } catch (error) {
      alert('API测试失败');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">仪表板</h1>
        <p className="mt-1 text-sm text-gray-500">
          欢迎回来！这里是您的交易策略概览。
        </p>
      </div>

      {/* 系统状态卡片 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">系统状态</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span>后端连接状态:</span>
            <span className="font-mono">{apiStatus}</span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={testBackend}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              检查后端
            </button>
            <button
              onClick={testAPI}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              测试API
            </button>
          </div>
        </div>
      </div>

      {/* 统计卡片网格 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="总余额"
          value={`$${stats.totalBalance.toLocaleString()}`}
          icon="dollar"
        />
        <StatCard
          title="总收益"
          value={`$${stats.totalProfit.toLocaleString()}`}
          change={stats.dailyPnL}
          icon="up"
        />
        <StatCard
          title="活跃策略"
          value={stats.activeStrategies}
          icon="play"
        />
        <StatCard
          title="胜率"
          value={`${stats.winRate}%`}
          icon="chart"
        />
      </div>

      {/* 性能图表占位符 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            投资组合表现
          </h3>
          <div className="flex space-x-2">
            {['1天', '7天', '30天', '90天'].map((period) => (
              <button
                key={period}
                className="px-3 py-1 text-sm rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                {period}
              </button>
            ))}
          </div>
        </div>
        
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
          <div className="text-center">
            <SimpleIcon type="chart" className="w-16 h-16 mx-auto mb-4" />
            <p className="text-gray-500">图表功能开发中...</p>
            <p className="text-sm text-gray-400">将显示投资组合性能趋势</p>
          </div>
        </div>
      </div>

      {/* 两列布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 活跃策略 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            活跃策略
          </h3>
          <div className="space-y-3">
            {[
              { name: '网格策略 #1', type: 'GRID', profit: 125.50, status: 'active' },
              { name: '趋势跟踪 #1', type: 'TREND', profit: 89.30, status: 'active' },
              { name: 'AI策略 #1', type: 'AI', profit: 234.80, status: 'active' }
            ].map((strategy, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900">{strategy.name}</p>
                  <p className="text-xs text-gray-500">{strategy.type}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {strategy.status}
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    +${strategy.profit.toFixed(2)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 最近活动 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            最近活动
          </h3>
          <div className="space-y-3">
            {[
              { description: '网格策略执行买入订单', time: '2分钟前' },
              { description: 'AI策略检测到买入信号', time: '5分钟前' },
              { description: '趋势跟踪策略止盈', time: '10分钟前' },
              { description: '新增交易所API密钥', time: '1小时前' },
              { description: '策略性能报告生成', time: '2小时前' }
            ].map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-2 w-2 bg-blue-400 rounded-full mt-2"></div>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          快速操作
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <button className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            创建策略
          </button>
          <button className="flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            添加交易所
          </button>
          <button className="flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            查看奖励
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleDashboard;

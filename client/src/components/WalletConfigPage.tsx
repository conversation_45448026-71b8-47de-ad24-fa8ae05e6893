import React, { useState, useEffect } from 'react';

interface WalletConfig {
  id: string;
  name: string;
  network: 'ethereum' | 'bsc' | 'base' | 'solana';
  address: string;
  privateKey: string;
  enabled: boolean;
  balance?: number;
}

interface NetworkConfig {
  name: string;
  rpcUrl: string;
  chainId?: number;
  symbol: string;
  explorer: string;
}

const WalletConfigPage: React.FC = () => {
  const [wallets, setWallets] = useState<WalletConfig[]>([]);
  const [networks, setNetworks] = useState<Record<string, NetworkConfig>>({});
  const [showAddWallet, setShowAddWallet] = useState(false);
  const [newWallet, setNewWallet] = useState<Partial<WalletConfig>>({
    name: '',
    network: 'ethereum',
    address: '',
    privateKey: '',
    enabled: true
  });

  useEffect(() => {
    loadWalletConfigurations();
  }, []);

  const loadWalletConfigurations = async () => {
    // 初始化网络配置
    setNetworks({
      ethereum: {
        name: 'Ethereum',
        rpcUrl: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
        chainId: 1,
        symbol: 'ETH',
        explorer: 'https://etherscan.io'
      },
      bsc: {
        name: 'Binance Smart Chain',
        rpcUrl: 'https://bsc-dataseed1.binance.org',
        chainId: 56,
        symbol: 'BNB',
        explorer: 'https://bscscan.com'
      },
      base: {
        name: 'Base',
        rpcUrl: 'https://mainnet.base.org',
        chainId: 8453,
        symbol: 'ETH',
        explorer: 'https://basescan.org'
      },
      solana: {
        name: 'Solana',
        rpcUrl: 'https://api.mainnet-beta.solana.com',
        symbol: 'SOL',
        explorer: 'https://solscan.io'
      }
    });

    // 加载钱包配置（示例数据）
    setWallets([
      {
        id: '1',
        name: '主钱包 - ETH',
        network: 'ethereum',
        address: '******************************************',
        privateKey: '',
        enabled: true,
        balance: 1.5
      },
      {
        id: '2',
        name: '交易钱包 - BSC',
        network: 'bsc',
        address: '******************************************',
        privateKey: '',
        enabled: true,
        balance: 0.8
      }
    ]);
  };

  const addWallet = () => {
    if (!newWallet.name || !newWallet.address) {
      alert('请填写钱包名称和地址');
      return;
    }

    const wallet: WalletConfig = {
      id: Date.now().toString(),
      name: newWallet.name!,
      network: newWallet.network!,
      address: newWallet.address!,
      privateKey: newWallet.privateKey!,
      enabled: newWallet.enabled!
    };

    setWallets([...wallets, wallet]);
    setNewWallet({
      name: '',
      network: 'ethereum',
      address: '',
      privateKey: '',
      enabled: true
    });
    setShowAddWallet(false);
  };

  const removeWallet = (id: string) => {
    if (confirm('确定要删除这个钱包吗？')) {
      setWallets(wallets.filter(w => w.id !== id));
    }
  };

  const toggleWallet = (id: string) => {
    setWallets(wallets.map(w => 
      w.id === id ? { ...w, enabled: !w.enabled } : w
    ));
  };

  const checkBalance = async (wallet: WalletConfig) => {
    try {
      // 这里将来会调用API检查余额
      alert(`检查 ${wallet.name} 余额中...`);
    } catch (error) {
      alert('检查余额失败');
    }
  };

  const generateWallet = (network: string) => {
    // 这里将来会调用API生成新钱包
    const mockAddress = network === 'solana' 
      ? 'So11111111111111111111111111111111111111112'
      : '0x' + Math.random().toString(16).substr(2, 40);
    
    setNewWallet({
      ...newWallet,
      address: mockAddress,
      privateKey: '生成的私钥将在这里显示'
    });
  };

  const getNetworkIcon = (network: string) => {
    const icons = {
      ethereum: '⟠',
      bsc: '🟡',
      base: '🔵',
      solana: '🟣'
    };
    return icons[network as keyof typeof icons] || '🔗';
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">钱包配置</h1>
        <button
          onClick={() => setShowAddWallet(true)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          添加钱包
        </button>
      </div>

      {/* 网络状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {Object.entries(networks).map(([key, network]) => (
          <div key={key} className="bg-white rounded-lg shadow-md p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-2xl mr-2">{getNetworkIcon(key)}</span>
                <div>
                  <h3 className="font-semibold">{network.name}</h3>
                  <p className="text-sm text-gray-500">{network.symbol}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">钱包数量</p>
                <p className="font-semibold">
                  {wallets.filter(w => w.network === key && w.enabled).length}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 钱包列表 */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">钱包列表</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {wallets.map((wallet) => (
            <div key={wallet.id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-2xl">{getNetworkIcon(wallet.network)}</span>
                  <div>
                    <h3 className="font-semibold">{wallet.name}</h3>
                    <p className="text-sm text-gray-500">{networks[wallet.network]?.name}</p>
                    <p className="text-sm text-gray-400 font-mono">
                      {wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  {wallet.balance !== undefined && (
                    <div className="text-right">
                      <p className="text-sm text-gray-500">余额</p>
                      <p className="font-semibold">
                        {wallet.balance} {networks[wallet.network]?.symbol}
                      </p>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => checkBalance(wallet)}
                      className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                    >
                      检查余额
                    </button>
                    
                    <button
                      onClick={() => toggleWallet(wallet.id)}
                      className={`px-3 py-1 rounded text-sm ${
                        wallet.enabled
                          ? 'bg-green-500 text-white hover:bg-green-600'
                          : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                      }`}
                    >
                      {wallet.enabled ? '启用' : '禁用'}
                    </button>
                    
                    <button
                      onClick={() => removeWallet(wallet.id)}
                      className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {wallets.length === 0 && (
            <div className="px-6 py-8 text-center text-gray-500">
              暂无钱包配置，点击"添加钱包"开始配置
            </div>
          )}
        </div>
      </div>

      {/* 添加钱包模态框 */}
      {showAddWallet && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-lg font-semibold mb-4">添加新钱包</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">钱包名称</label>
                <input
                  type="text"
                  value={newWallet.name}
                  onChange={(e) => setNewWallet({...newWallet, name: e.target.value})}
                  className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入钱包名称"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">网络</label>
                <select
                  value={newWallet.network}
                  onChange={(e) => setNewWallet({...newWallet, network: e.target.value as any})}
                  className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="ethereum">Ethereum</option>
                  <option value="bsc">Binance Smart Chain</option>
                  <option value="base">Base</option>
                  <option value="solana">Solana</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">钱包地址</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newWallet.address}
                    onChange={(e) => setNewWallet({...newWallet, address: e.target.value})}
                    className="flex-1 px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入钱包地址"
                  />
                  <button
                    onClick={() => generateWallet(newWallet.network!)}
                    className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    生成
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">私钥 (可选)</label>
                <input
                  type="password"
                  value={newWallet.privateKey}
                  onChange={(e) => setNewWallet({...newWallet, privateKey: e.target.value})}
                  className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入私钥（用于自动交易）"
                />
                <p className="text-xs text-gray-500 mt-1">
                  私钥将被加密存储，仅用于自动交易功能
                </p>
              </div>
              
              <div className="flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newWallet.enabled}
                    onChange={(e) => setNewWallet({...newWallet, enabled: e.target.checked})}
                    className="mr-2"
                  />
                  启用此钱包
                </label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowAddWallet(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={addWallet}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                添加
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WalletConfigPage;

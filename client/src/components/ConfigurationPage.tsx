import React, { useState, useEffect } from 'react';

// 配置数据类型定义
interface ExchangeConfig {
  name: string;
  apiKey: string;
  secretKey: string;
  passphrase?: string;
  sandbox: boolean;
  enabled: boolean;
}

interface StrategyConfig {
  type: 'grid' | 'trend_following' | 'ai' | 'dex_grid';
  name: string;
  enabled: boolean;
  parameters: Record<string, any>;
}

interface RiskConfig {
  maxPositionSize: number;
  stopLossPercentage: number;
  takeProfitPercentage: number;
  maxDailyLoss: number;
  maxOpenPositions: number;
}

interface NotificationConfig {
  email: boolean;
  webhook: boolean;
  webhookUrl: string;
  emailAddress: string;
}

const ConfigurationPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('exchanges');
  const [exchanges, setExchanges] = useState<ExchangeConfig[]>([]);
  const [strategies, setStrategies] = useState<StrategyConfig[]>([]);
  const [riskConfig, setRiskConfig] = useState<RiskConfig>({
    maxPositionSize: 1000,
    stopLossPercentage: 5,
    takeProfitPercentage: 10,
    maxDailyLoss: 500,
    maxOpenPositions: 5
  });
  const [notifications, setNotifications] = useState<NotificationConfig>({
    email: false,
    webhook: false,
    webhookUrl: '',
    emailAddress: ''
  });

  // 初始化数据
  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = async () => {
    try {
      // 这里将来会从API加载配置
      setExchanges([
        { name: 'Binance', apiKey: '', secretKey: '', sandbox: true, enabled: false },
        { name: 'OKX', apiKey: '', secretKey: '', passphrase: '', sandbox: true, enabled: false },
        { name: 'HTX', apiKey: '', secretKey: '', sandbox: true, enabled: false }
      ]);
      
      setStrategies([
        { type: 'grid', name: '网格策略', enabled: false, parameters: { gridSize: 10, priceRange: 5 } },
        { type: 'trend_following', name: '趋势跟踪', enabled: false, parameters: { period: 20, threshold: 2 } },
        { type: 'ai', name: 'AI策略', enabled: false, parameters: { model: 'lstm', confidence: 0.8 } }
      ]);
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  const saveConfiguration = async () => {
    try {
      const config = {
        exchanges,
        strategies,
        riskConfig,
        notifications
      };
      
      // 这里将来会调用API保存配置
      console.log('保存配置:', config);
      alert('配置保存成功！');
    } catch (error) {
      console.error('保存配置失败:', error);
      alert('保存配置失败！');
    }
  };

  const testConnection = async (exchangeName: string) => {
    try {
      // 这里将来会调用API测试连接
      alert(`${exchangeName} 连接测试成功！`);
    } catch (error) {
      alert(`${exchangeName} 连接测试失败！`);
    }
  };

  // 交易所配置标签页
  const renderExchangesTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">交易所配置</h3>
        <button
          onClick={() => setExchanges([...exchanges, { name: '自定义', apiKey: '', secretKey: '', sandbox: true, enabled: false }])}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          添加交易所
        </button>
      </div>
      
      {exchanges.map((exchange, index) => (
        <div key={index} className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold">{exchange.name}</h4>
            <div className="flex items-center space-x-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exchange.enabled}
                  onChange={(e) => {
                    const newExchanges = [...exchanges];
                    newExchanges[index].enabled = e.target.checked;
                    setExchanges(newExchanges);
                  }}
                  className="mr-2"
                />
                启用
              </label>
              <button
                onClick={() => testConnection(exchange.name)}
                className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
              >
                测试连接
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">API Key</label>
              <input
                type="password"
                value={exchange.apiKey}
                onChange={(e) => {
                  const newExchanges = [...exchanges];
                  newExchanges[index].apiKey = e.target.value;
                  setExchanges(newExchanges);
                }}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入API Key"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Secret Key</label>
              <input
                type="password"
                value={exchange.secretKey}
                onChange={(e) => {
                  const newExchanges = [...exchanges];
                  newExchanges[index].secretKey = e.target.value;
                  setExchanges(newExchanges);
                }}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入Secret Key"
              />
            </div>
            
            {exchange.passphrase !== undefined && (
              <div>
                <label className="block text-sm font-medium mb-1">Passphrase</label>
                <input
                  type="password"
                  value={exchange.passphrase}
                  onChange={(e) => {
                    const newExchanges = [...exchanges];
                    newExchanges[index].passphrase = e.target.value;
                    setExchanges(newExchanges);
                  }}
                  className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入Passphrase"
                />
              </div>
            )}
            
            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exchange.sandbox}
                  onChange={(e) => {
                    const newExchanges = [...exchanges];
                    newExchanges[index].sandbox = e.target.checked;
                    setExchanges(newExchanges);
                  }}
                  className="mr-2"
                />
                沙盒模式
              </label>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  // 策略配置标签页
  const renderStrategiesTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">策略配置</h3>
        <button
          onClick={() => setStrategies([...strategies, { type: 'grid', name: '新策略', enabled: false, parameters: {} }])}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          添加策略
        </button>
      </div>
      
      {strategies.map((strategy, index) => (
        <div key={index} className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <select
                value={strategy.type}
                onChange={(e) => {
                  const newStrategies = [...strategies];
                  newStrategies[index].type = e.target.value as any;
                  setStrategies(newStrategies);
                }}
                className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="grid">网格策略</option>
                <option value="trend_following">趋势跟踪</option>
                <option value="ai">AI策略</option>
                <option value="dex_grid">DEX网格</option>
              </select>
              
              <input
                type="text"
                value={strategy.name}
                onChange={(e) => {
                  const newStrategies = [...strategies];
                  newStrategies[index].name = e.target.value;
                  setStrategies(newStrategies);
                }}
                className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="策略名称"
              />
            </div>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={strategy.enabled}
                onChange={(e) => {
                  const newStrategies = [...strategies];
                  newStrategies[index].enabled = e.target.checked;
                  setStrategies(newStrategies);
                }}
                className="mr-2"
              />
              启用
            </label>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {strategy.type === 'grid' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-1">网格大小</label>
                  <input
                    type="number"
                    value={strategy.parameters.gridSize || 10}
                    onChange={(e) => {
                      const newStrategies = [...strategies];
                      newStrategies[index].parameters.gridSize = Number(e.target.value);
                      setStrategies(newStrategies);
                    }}
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">价格范围 (%)</label>
                  <input
                    type="number"
                    value={strategy.parameters.priceRange || 5}
                    onChange={(e) => {
                      const newStrategies = [...strategies];
                      newStrategies[index].parameters.priceRange = Number(e.target.value);
                      setStrategies(newStrategies);
                    }}
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </>
            )}
            
            {strategy.type === 'trend_following' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-1">周期</label>
                  <input
                    type="number"
                    value={strategy.parameters.period || 20}
                    onChange={(e) => {
                      const newStrategies = [...strategies];
                      newStrategies[index].parameters.period = Number(e.target.value);
                      setStrategies(newStrategies);
                    }}
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">阈值</label>
                  <input
                    type="number"
                    step="0.1"
                    value={strategy.parameters.threshold || 2}
                    onChange={(e) => {
                      const newStrategies = [...strategies];
                      newStrategies[index].parameters.threshold = Number(e.target.value);
                      setStrategies(newStrategies);
                    }}
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </>
            )}
            
            {strategy.type === 'ai' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-1">模型类型</label>
                  <select
                    value={strategy.parameters.model || 'lstm'}
                    onChange={(e) => {
                      const newStrategies = [...strategies];
                      newStrategies[index].parameters.model = e.target.value;
                      setStrategies(newStrategies);
                    }}
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="lstm">LSTM</option>
                    <option value="transformer">Transformer</option>
                    <option value="random_forest">Random Forest</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">置信度</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={strategy.parameters.confidence || 0.8}
                    onChange={(e) => {
                      const newStrategies = [...strategies];
                      newStrategies[index].parameters.confidence = Number(e.target.value);
                      setStrategies(newStrategies);
                    }}
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  // 风险管理标签页
  const renderRiskTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">风险管理配置</h3>
      
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">最大仓位大小 (USDT)</label>
            <input
              type="number"
              value={riskConfig.maxPositionSize}
              onChange={(e) => setRiskConfig({...riskConfig, maxPositionSize: Number(e.target.value)})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">止损百分比 (%)</label>
            <input
              type="number"
              step="0.1"
              value={riskConfig.stopLossPercentage}
              onChange={(e) => setRiskConfig({...riskConfig, stopLossPercentage: Number(e.target.value)})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">止盈百分比 (%)</label>
            <input
              type="number"
              step="0.1"
              value={riskConfig.takeProfitPercentage}
              onChange={(e) => setRiskConfig({...riskConfig, takeProfitPercentage: Number(e.target.value)})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">每日最大亏损 (USDT)</label>
            <input
              type="number"
              value={riskConfig.maxDailyLoss}
              onChange={(e) => setRiskConfig({...riskConfig, maxDailyLoss: Number(e.target.value)})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">最大开仓数量</label>
            <input
              type="number"
              value={riskConfig.maxOpenPositions}
              onChange={(e) => setRiskConfig({...riskConfig, maxOpenPositions: Number(e.target.value)})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );

  // 通知配置标签页
  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">通知配置</h3>
      
      <div className="bg-gray-50 p-4 rounded-lg space-y-4">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={notifications.email}
              onChange={(e) => setNotifications({...notifications, email: e.target.checked})}
              className="mr-2"
            />
            邮件通知
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={notifications.webhook}
              onChange={(e) => setNotifications({...notifications, webhook: e.target.checked})}
              className="mr-2"
            />
            Webhook通知
          </label>
        </div>
        
        {notifications.email && (
          <div>
            <label className="block text-sm font-medium mb-1">邮箱地址</label>
            <input
              type="email"
              value={notifications.emailAddress}
              onChange={(e) => setNotifications({...notifications, emailAddress: e.target.value})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入邮箱地址"
            />
          </div>
        )}
        
        {notifications.webhook && (
          <div>
            <label className="block text-sm font-medium mb-1">Webhook URL</label>
            <input
              type="url"
              value={notifications.webhookUrl}
              onChange={(e) => setNotifications({...notifications, webhookUrl: e.target.value})}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入Webhook URL"
            />
          </div>
        )}
      </div>
    </div>
  );

  const tabs = [
    { id: 'exchanges', name: '交易所', icon: '🏢' },
    { id: 'strategies', name: '策略', icon: '🤖' },
    { id: 'risk', name: '风险管理', icon: '⚠️' },
    { id: 'notifications', name: '通知', icon: '🔔' }
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">参数配置</h1>
        <button
          onClick={saveConfiguration}
          className="px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          保存配置
        </button>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="min-h-96">
        {activeTab === 'exchanges' && renderExchangesTab()}
        {activeTab === 'strategies' && renderStrategiesTab()}
        {activeTab === 'risk' && renderRiskTab()}
        {activeTab === 'notifications' && renderNotificationsTab()}
      </div>
    </div>
  );
};

export default ConfigurationPage;

import React, { useState } from 'react';

// 简化的组件，避免复杂依赖
const SimpleButton: React.FC<{ onClick: () => void; children: React.ReactNode; className?: string }> = ({ onClick, children, className = '' }) => (
  <button 
    onClick={onClick}
    className={`px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors ${className}`}
  >
    {children}
  </button>
);

const Card: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="bg-white rounded-lg shadow-md p-6 mb-4">
    <h3 className="text-lg font-semibold mb-4 text-gray-800">{title}</h3>
    {children}
  </div>
);

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [apiStatus, setApiStatus] = useState<string>('未检查');

  // 测试后端连接
  const testBackend = async () => {
    try {
      setApiStatus('检查中...');
      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();
      setApiStatus(`✅ 连接成功 - ${data.status}`);
    } catch (error) {
      setApiStatus('❌ 连接失败');
    }
  };

  // 测试API
  const testAPI = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/test');
      const data = await response.json();
      alert(`API测试成功: ${data.message}`);
    } catch (error) {
      alert('API测试失败');
    }
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div>
            <Card title="系统状态">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>后端连接状态:</span>
                  <span className="font-mono">{apiStatus}</span>
                </div>
                <div className="flex space-x-2">
                  <SimpleButton onClick={testBackend}>检查后端</SimpleButton>
                  <SimpleButton onClick={testAPI}>测试API</SimpleButton>
                </div>
              </div>
            </Card>

            <Card title="快速开始">
              <div className="space-y-2 text-sm text-gray-600">
                <p>🎉 恭喜！TradeAI Bot 已成功启动</p>
                <p>📱 前端地址: http://localhost:5173</p>
                <p>🔗 后端地址: http://localhost:3001</p>
                <p>🔍 健康检查: http://localhost:3001/health</p>
              </div>
            </Card>

            <Card title="功能模块">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-gray-50 rounded">
                  <h4 className="font-semibold">交易策略</h4>
                  <p className="text-sm text-gray-600">网格、趋势跟踪、AI策略</p>
                </div>
                <div className="p-4 bg-gray-50 rounded">
                  <h4 className="font-semibold">交易所集成</h4>
                  <p className="text-sm text-gray-600">Binance、OKX、HTX等</p>
                </div>
                <div className="p-4 bg-gray-50 rounded">
                  <h4 className="font-semibold">钱包管理</h4>
                  <p className="text-sm text-gray-600">多链钱包批量管理</p>
                </div>
                <div className="p-4 bg-gray-50 rounded">
                  <h4 className="font-semibold">奖励系统</h4>
                  <p className="text-sm text-gray-600">交易奖励、推荐奖励</p>
                </div>
              </div>
            </Card>
          </div>
        );
      
      case 'api':
        return (
          <Card title="API 测试">
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="p-4 border rounded">
                  <h4 className="font-semibold">健康检查</h4>
                  <p className="text-sm text-gray-600 mb-2">GET /health</p>
                  <SimpleButton onClick={() => window.open('http://localhost:3001/health', '_blank')}>
                    测试
                  </SimpleButton>
                </div>
                <div className="p-4 border rounded">
                  <h4 className="font-semibold">API 基础信息</h4>
                  <p className="text-sm text-gray-600 mb-2">GET /api</p>
                  <SimpleButton onClick={() => window.open('http://localhost:3001/api', '_blank')}>
                    测试
                  </SimpleButton>
                </div>
                <div className="p-4 border rounded">
                  <h4 className="font-semibold">测试端点</h4>
                  <p className="text-sm text-gray-600 mb-2">GET /api/test</p>
                  <SimpleButton onClick={() => window.open('http://localhost:3001/api/test', '_blank')}>
                    测试
                  </SimpleButton>
                </div>
              </div>
            </div>
          </Card>
        );
      
      case 'about':
        return (
          <Card title="关于 TradeAI Bot">
            <div className="space-y-4 text-sm text-gray-600">
              <p>TradeAI Bot 是一个功能完整的智能加密货币交易机器人系统。</p>
              
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">核心功能:</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>15+种交易策略 (网格、趋势跟踪、AI等)</li>
                  <li>15+个交易所支持 (CEX + DEX)</li>
                  <li>4条区块链网络支持</li>
                  <li>智能奖励系统</li>
                  <li>多链钱包管理</li>
                  <li>实时数据监控</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">技术栈:</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>后端: Node.js + TypeScript + Express</li>
                  <li>前端: React + TypeScript + Tailwind CSS</li>
                  <li>数据库: PostgreSQL + Redis</li>
                  <li>部署: Docker + Kubernetes</li>
                </ul>
              </div>
            </div>
          </Card>
        );
      
      default:
        return <div>页面不存在</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">TradeAI Bot</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setCurrentPage('dashboard')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'dashboard' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                仪表板
              </button>
              <button
                onClick={() => setCurrentPage('api')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'api' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                API测试
              </button>
              <button
                onClick={() => setCurrentPage('about')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'about' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                关于
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {renderPage()}
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-gray-500">
            TradeAI Bot v1.0.0 - 智能加密货币交易机器人
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;

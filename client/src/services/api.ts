import axios, { AxiosInstance, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';

// API Base Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.');
    } else if (error.response?.data?.message) {
      // API error with message
      toast.error(error.response.data.message);
    } else if (error.message) {
      // Network or other error
      toast.error(error.message);
    }
    
    return Promise.reject(error);
  }
);

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
}

// Auth API
export const authAPI = {
  login: async (credentials: { email: string; password: string }) => {
    const response = await api.post<APIResponse>('/auth/login', credentials);
    return response.data.data;
  },

  register: async (userData: { email: string; username: string; password: string }) => {
    const response = await api.post<APIResponse>('/auth/register', userData);
    return response.data.data;
  },

  getProfile: async () => {
    const response = await api.get<APIResponse>('/auth/profile');
    return response.data.data;
  },

  updateProfile: async (updates: any) => {
    const response = await api.put<APIResponse>('/auth/profile', updates);
    return response.data.data;
  },

  changePassword: async (passwords: { currentPassword: string; newPassword: string }) => {
    const response = await api.put<APIResponse>('/auth/change-password', passwords);
    return response.data;
  },

  forgotPassword: async (email: string) => {
    const response = await api.post<APIResponse>('/auth/forgot-password', { email });
    return response.data;
  },

  resetPassword: async (token: string, password: string) => {
    const response = await api.post<APIResponse>('/auth/reset-password', { token, password });
    return response.data;
  },
};

// Exchanges API
export const exchangesAPI = {
  getSupported: async () => {
    const response = await api.get<APIResponse>('/exchanges/supported');
    return response.data.data;
  },

  getConnected: async () => {
    const response = await api.get<APIResponse>('/exchanges/connected');
    return response.data.data;
  },

  testConnection: async (exchange: string) => {
    const response = await api.post<APIResponse>(`/exchanges/test/${exchange}`);
    return response.data;
  },

  getAccount: async (exchange: string) => {
    const response = await api.get<APIResponse>(`/exchanges/${exchange}/account`);
    return response.data.data;
  },

  getBalances: async (exchange: string, asset?: string) => {
    const params = asset ? { asset } : {};
    const response = await api.get<APIResponse>(`/exchanges/${exchange}/balances`, { params });
    return response.data.data;
  },

  getSymbols: async (exchange: string) => {
    const response = await api.get<APIResponse>(`/exchanges/${exchange}/symbols`);
    return response.data.data;
  },

  getTicker: async (exchange: string, symbol: string) => {
    const response = await api.get<APIResponse>(`/exchanges/${exchange}/ticker/${symbol}`);
    return response.data.data;
  },

  getOpenOrders: async (exchange: string, symbol?: string) => {
    const params = symbol ? { symbol } : {};
    const response = await api.get<APIResponse>(`/exchanges/${exchange}/orders/open`, { params });
    return response.data.data;
  },

  getOrderHistory: async (exchange: string, symbol?: string, limit?: number) => {
    const params: any = {};
    if (symbol) params.symbol = symbol;
    if (limit) params.limit = limit;
    const response = await api.get<APIResponse>(`/exchanges/${exchange}/orders/history`, { params });
    return response.data.data;
  },

  disconnect: async (exchange: string) => {
    const response = await api.post<APIResponse>(`/exchanges/${exchange}/disconnect`);
    return response.data;
  },
};

// API Keys API
export const apiKeysAPI = {
  getAll: async () => {
    const response = await api.get<APIResponse>('/api-keys');
    return response.data.data;
  },

  create: async (keyData: any) => {
    const response = await api.post<APIResponse>('/api-keys', keyData);
    return response.data.data;
  },

  update: async (keyId: string, updates: any) => {
    const response = await api.put<APIResponse>(`/api-keys/${keyId}`, updates);
    return response.data.data;
  },

  delete: async (keyId: string) => {
    const response = await api.delete<APIResponse>(`/api-keys/${keyId}`);
    return response.data;
  },

  toggle: async (keyId: string) => {
    const response = await api.post<APIResponse>(`/api-keys/${keyId}/toggle`);
    return response.data.data;
  },

  test: async (keyId: string) => {
    const response = await api.post<APIResponse>(`/api-keys/${keyId}/test`);
    return response.data;
  },
};

// Strategies API
export const strategiesAPI = {
  getAll: async () => {
    const response = await api.get<APIResponse>('/strategies');
    return response.data.data;
  },

  getById: async (strategyId: string) => {
    const response = await api.get<APIResponse>(`/strategies/${strategyId}`);
    return response.data.data;
  },

  create: async (strategyData: any) => {
    const response = await api.post<APIResponse>('/strategies', strategyData);
    return response.data.data;
  },

  update: async (strategyId: string, updates: any) => {
    const response = await api.put<APIResponse>(`/strategies/${strategyId}`, updates);
    return response.data.data;
  },

  delete: async (strategyId: string) => {
    const response = await api.delete<APIResponse>(`/strategies/${strategyId}`);
    return response.data;
  },

  start: async (strategyId: string) => {
    const response = await api.post<APIResponse>(`/strategies/${strategyId}/start`);
    return response.data;
  },

  stop: async (strategyId: string) => {
    const response = await api.post<APIResponse>(`/strategies/${strategyId}/stop`);
    return response.data;
  },

  pause: async (strategyId: string) => {
    const response = await api.post<APIResponse>(`/strategies/${strategyId}/pause`);
    return response.data;
  },

  getPerformance: async (strategyId: string, startDate?: string, endDate?: string) => {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    const response = await api.get<APIResponse>(`/strategies/${strategyId}/performance`, { params });
    return response.data.data;
  },

  getStats: async (strategyId: string) => {
    const response = await api.get<APIResponse>(`/strategies/${strategyId}/stats`);
    return response.data.data;
  },
};

// Wallets API
export const walletsAPI = {
  getAll: async (network?: string) => {
    const params = network ? { network } : {};
    const response = await api.get<APIResponse>('/wallets', { params });
    return response.data.data;
  },

  create: async (walletData: any) => {
    const response = await api.post<APIResponse>('/wallets', walletData);
    return response.data.data;
  },

  delete: async (walletId: string) => {
    const response = await api.delete<APIResponse>(`/wallets/${walletId}`);
    return response.data;
  },

  toggle: async (walletId: string) => {
    const response = await api.post<APIResponse>(`/wallets/${walletId}/toggle`);
    return response.data.data;
  },

  getStats: async () => {
    const response = await api.get<APIResponse>('/wallets/stats');
    return response.data.data;
  },

  importFromCSV: async (csvData: any) => {
    const response = await api.post<APIResponse>('/wallets/import', csvData);
    return response.data.data;
  },

  exportToCSV: async (password: string, network?: string) => {
    const params: any = { password };
    if (network) params.network = network;
    const response = await api.post<APIResponse>('/wallets/export', params);
    return response.data.data;
  },
};

// Rewards API
export const rewardsAPI = {
  getAll: async (status?: string, limit?: number, offset?: number) => {
    const params: any = {};
    if (status) params.status = status;
    if (limit) params.limit = limit;
    if (offset) params.offset = offset;
    const response = await api.get<APIResponse>('/rewards', { params });
    return response.data.data;
  },

  getStats: async () => {
    const response = await api.get<APIResponse>('/rewards/stats');
    return response.data.data;
  },

  claim: async (rewardId: string) => {
    const response = await api.post<APIResponse>(`/rewards/${rewardId}/claim`);
    return response.data;
  },

  getReferralStats: async () => {
    const response = await api.get<APIResponse>('/rewards/referral/stats');
    return response.data.data;
  },

  processReferral: async (refereeId: string) => {
    const response = await api.post<APIResponse>('/rewards/referral', { refereeId });
    return response.data.data;
  },

  getUserAchievements: async () => {
    const response = await api.get<APIResponse>('/rewards/achievements/user');
    return response.data.data;
  },

  checkAchievements: async () => {
    const response = await api.post<APIResponse>('/rewards/achievements/check');
    return response.data.data;
  },

  getCampaigns: async (active?: boolean) => {
    const params = active !== undefined ? { active } : {};
    const response = await api.get<APIResponse>('/rewards/campaigns', { params });
    return response.data.data;
  },
};

// Dashboard API
export const dashboardAPI = {
  getOverview: async () => {
    const response = await api.get<APIResponse>('/dashboard/overview');
    return response.data.data;
  },

  getRecentActivity: async (limit?: number) => {
    const params = limit ? { limit } : {};
    const response = await api.get<APIResponse>('/dashboard/activity', { params });
    return response.data.data;
  },

  getPortfolioSummary: async () => {
    const response = await api.get<APIResponse>('/dashboard/portfolio');
    return response.data.data;
  },

  getPerformanceChart: async (period: string) => {
    const response = await api.get<APIResponse>(`/dashboard/performance/${period}`);
    return response.data.data;
  },
};

export default api;

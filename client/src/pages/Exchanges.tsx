import React from 'react';
import { PlusIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

const Exchanges: React.FC = () => {
  const exchanges = [
    { id: 1, name: 'Binance', status: 'connected', lastSync: '2 min ago' },
    { id: 2, name: 'OKX', status: 'connected', lastSync: '5 min ago' },
    { id: 3, name: 'HTX', status: 'disconnected', lastSync: 'Never' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Exchanges
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your exchange API connections
          </p>
        </div>
        <button className="btn-primary px-4 py-2">
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Exchange
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {exchanges.map((exchange) => (
          <div key={exchange.id} className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {exchange.name}
              </h3>
              {exchange.status === 'connected' ? (
                <CheckCircleIcon className="w-6 h-6 text-success-600 dark:text-success-400" />
              ) : (
                <XCircleIcon className="w-6 h-6 text-danger-600 dark:text-danger-400" />
              )}
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Status:</span>
                <span className={`text-sm font-medium ${
                  exchange.status === 'connected' 
                    ? 'text-success-600 dark:text-success-400' 
                    : 'text-danger-600 dark:text-danger-400'
                }`}>
                  {exchange.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Last Sync:</span>
                <span className="text-sm text-gray-900 dark:text-white">
                  {exchange.lastSync}
                </span>
              </div>
            </div>
            <div className="mt-4 flex space-x-2">
              <button className="btn-outline flex-1 py-2 text-sm">
                Configure
              </button>
              <button className="btn-secondary flex-1 py-2 text-sm">
                Test
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Exchanges;

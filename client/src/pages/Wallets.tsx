import React from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';

const Wallets: React.FC = () => {
  const wallets = [
    { id: 1, network: 'Ethereum', address: '0x1234...5678', balance: 2.5, symbol: 'ETH' },
    { id: 2, network: 'BSC', address: '0xabcd...efgh', balance: 1.2, symbol: 'BNB' },
    { id: 3, network: 'Solana', address: 'ABC123...XYZ', balance: 50.0, symbol: 'SOL' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Wallets
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your blockchain wallets
          </p>
        </div>
        <button className="btn-primary px-4 py-2">
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Wallet
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {wallets.map((wallet) => (
          <div key={wallet.id} className="card p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {wallet.network}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                {wallet.address}
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Balance:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {wallet.balance} {wallet.symbol}
                </span>
              </div>
            </div>
            <div className="mt-4 flex space-x-2">
              <button className="btn-outline flex-1 py-2 text-sm">
                View
              </button>
              <button className="btn-secondary flex-1 py-2 text-sm">
                Export
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Wallets;

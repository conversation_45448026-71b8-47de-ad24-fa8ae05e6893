import React from 'react';
import { 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  CogIcon, 
  BoltIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const Dashboard: React.FC = () => {
  // 模拟数据
  const stats = {
    totalProfit: 12450.67,
    totalTrades: 1234,
    activeStrategies: 8,
    successRate: 78.5
  };

  const recentTrades = [
    { id: 1, symbol: 'BTC/USDT', side: 'buy', amount: 0.1, price: 43250, profit: 125.50, time: '2 min ago' },
    { id: 2, symbol: 'ETH/USDT', side: 'sell', amount: 2.5, price: 2650, profit: -45.20, time: '5 min ago' },
    { id: 3, symbol: 'BNB/USDT', side: 'buy', amount: 10, price: 315, profit: 78.90, time: '8 min ago' },
  ];

  const activeStrategies = [
    { id: 1, name: 'BTC Grid Strategy', type: 'Grid', status: 'active', profit: 2450.30, trades: 156 },
    { id: 2, name: 'ETH Trend Following', type: 'Trend', status: 'active', profit: 1890.45, trades: 89 },
    { id: 3, name: 'BNB Liquidity', type: 'Liquidity', status: 'paused', profit: 567.80, trades: 34 },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Welcome back! Here's what's happening with your trading strategies.
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-primary px-4 py-2">
            <BoltIcon className="w-4 h-4 mr-2" />
            New Strategy
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-success-100 rounded-lg dark:bg-success-900/20">
              <CurrencyDollarIcon className="w-6 h-6 text-success-600 dark:text-success-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Profit</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                ${stats.totalProfit.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 rounded-lg dark:bg-primary-900/20">
              <ChartBarIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Trades</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.totalTrades.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-warning-100 rounded-lg dark:bg-warning-900/20">
              <CogIcon className="w-6 h-6 text-warning-600 dark:text-warning-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Strategies</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.activeStrategies}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-info-100 rounded-lg dark:bg-info-900/20">
              <TrendingUpIcon className="w-6 h-6 text-info-600 dark:text-info-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.successRate}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最近交易 */}
        <div className="card">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Trades
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentTrades.map((trade) => (
                <div key={trade.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      trade.side === 'buy' 
                        ? 'bg-success-100 dark:bg-success-900/20' 
                        : 'bg-danger-100 dark:bg-danger-900/20'
                    }`}>
                      {trade.side === 'buy' ? (
                        <TrendingUpIcon className="w-4 h-4 text-success-600 dark:text-success-400" />
                      ) : (
                        <TrendingDownIcon className="w-4 h-4 text-danger-600 dark:text-danger-400" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trade.symbol}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {trade.amount} @ ${trade.price.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${
                      trade.profit > 0 
                        ? 'text-success-600 dark:text-success-400' 
                        : 'text-danger-600 dark:text-danger-400'
                    }`}>
                      {trade.profit > 0 ? '+' : ''}${trade.profit.toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {trade.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 活跃策略 */}
        <div className="card">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Active Strategies
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {activeStrategies.map((strategy) => (
                <div key={strategy.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      strategy.status === 'active' 
                        ? 'bg-success-100 dark:bg-success-900/20' 
                        : 'bg-warning-100 dark:bg-warning-900/20'
                    }`}>
                      {strategy.status === 'active' ? (
                        <CheckCircleIcon className="w-4 h-4 text-success-600 dark:text-success-400" />
                      ) : (
                        <ClockIcon className="w-4 h-4 text-warning-600 dark:text-warning-400" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {strategy.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {strategy.type} • {strategy.trades} trades
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-success-600 dark:text-success-400">
                      +${strategy.profit.toFixed(2)}
                    </p>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      strategy.status === 'active'
                        ? 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-200'
                        : 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-200'
                    }`}>
                      {strategy.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="btn-outline p-4 text-left">
            <BoltIcon className="w-6 h-6 text-primary-600 dark:text-primary-400 mb-2" />
            <h4 className="font-medium text-gray-900 dark:text-white">Create Strategy</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">Set up a new trading strategy</p>
          </button>
          <button className="btn-outline p-4 text-left">
            <CogIcon className="w-6 h-6 text-primary-600 dark:text-primary-400 mb-2" />
            <h4 className="font-medium text-gray-900 dark:text-white">Configure Exchange</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">Add or update API keys</p>
          </button>
          <button className="btn-outline p-4 text-left">
            <ChartBarIcon className="w-6 h-6 text-primary-600 dark:text-primary-400 mb-2" />
            <h4 className="font-medium text-gray-900 dark:text-white">View Analytics</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">Analyze performance metrics</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

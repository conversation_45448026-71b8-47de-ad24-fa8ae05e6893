import React from 'react';
import { PlusIcon, PlayIcon, PauseIcon, StopIcon } from '@heroicons/react/24/outline';

const Strategies: React.FC = () => {
  const strategies = [
    {
      id: 1,
      name: 'BTC Grid Strategy',
      type: 'Grid',
      exchange: 'Binance',
      status: 'active',
      profit: 2450.30,
      trades: 156,
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      name: 'ETH Trend Following',
      type: 'Trend Following',
      exchange: 'OKX',
      status: 'active',
      profit: 1890.45,
      trades: 89,
      createdAt: '2024-01-10'
    },
    {
      id: 3,
      name: 'BNB Liquidity Maintenance',
      type: 'Liquidity',
      exchange: 'Binance',
      status: 'paused',
      profit: 567.80,
      trades: 34,
      createdAt: '2024-01-08'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return 'badge-success';
      case 'paused':
        return 'badge-warning';
      case 'stopped':
        return 'badge-danger';
      default:
        return 'badge-info';
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Trading Strategies
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your automated trading strategies
          </p>
        </div>
        <button className="btn-primary px-4 py-2">
          <PlusIcon className="w-4 h-4 mr-2" />
          New Strategy
        </button>
      </div>

      {/* 策略列表 */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-head">Strategy</th>
                <th className="table-head">Type</th>
                <th className="table-head">Exchange</th>
                <th className="table-head">Status</th>
                <th className="table-head">Profit</th>
                <th className="table-head">Trades</th>
                <th className="table-head">Created</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody>
              {strategies.map((strategy) => (
                <tr key={strategy.id} className="table-row">
                  <td className="table-cell">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {strategy.name}
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="text-gray-900 dark:text-white">
                      {strategy.type}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="text-gray-900 dark:text-white">
                      {strategy.exchange}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className={`badge ${getStatusBadge(strategy.status)}`}>
                      {strategy.status}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="font-medium text-success-600 dark:text-success-400">
                      +${strategy.profit.toFixed(2)}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="text-gray-900 dark:text-white">
                      {strategy.trades}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="text-gray-500 dark:text-gray-400">
                      {strategy.createdAt}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      {strategy.status === 'active' ? (
                        <button className="p-1 text-warning-600 hover:text-warning-700 dark:text-warning-400">
                          <PauseIcon className="w-4 h-4" />
                        </button>
                      ) : (
                        <button className="p-1 text-success-600 hover:text-success-700 dark:text-success-400">
                          <PlayIcon className="w-4 h-4" />
                        </button>
                      )}
                      <button className="p-1 text-danger-600 hover:text-danger-700 dark:text-danger-400">
                        <StopIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Strategies;

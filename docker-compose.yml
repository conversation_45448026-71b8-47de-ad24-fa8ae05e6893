version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tradeai-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-tradeai_bot}
      POSTGRES_USER: ${DB_USER:-tradeai}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-tradeai_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - tradeai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-tradeai} -d ${DB_NAME:-tradeai_bot}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tradeai-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - tradeai-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TradeAI Bot Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: tradeai-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3001

      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-tradeai_bot}
      DB_USER: ${DB_USER:-tradeai}
      DB_PASSWORD: ${DB_PASSWORD:-tradeai_password}

      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}

      # JWT
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}

      # Encryption
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-your-32-character-encryption-key}

      # Email (optional)
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      SMTP_FROM: ${SMTP_FROM:-<EMAIL>}

      # External APIs
      BINANCE_API_URL: ${BINANCE_API_URL:-https://api.binance.com}
      OKX_API_URL: ${OKX_API_URL:-https://www.okx.com}
      HTX_API_URL: ${HTX_API_URL:-https://api.huobi.pro}

      # Blockchain RPCs
      ETH_RPC_URL: ${ETH_RPC_URL:-https://mainnet.infura.io/v3/YOUR_PROJECT_ID}
      BSC_RPC_URL: ${BSC_RPC_URL:-https://bsc-dataseed.binance.org/}
      BASE_RPC_URL: ${BASE_RPC_URL:-https://mainnet.base.org}
      SOLANA_RPC_URL: ${SOLANA_RPC_URL:-https://api.mainnet-beta.solana.com}

      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-info}

    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
      - app_temp:/app/temp
      - app_models:/app/models
    ports:
      - "${APP_PORT:-3001}:3001"
    networks:
      - tradeai-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # pgAdmin (可选，用于数据库管理)
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - tradeai-network
    restart: unless-stopped
    profiles:
      - tools

  # Redis Commander (可选，用于 Redis 管理)
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - tradeai-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  app_temp:
    driver: local
  app_models:
    driver: local

networks:
  tradeai-network:
    driver: bridge

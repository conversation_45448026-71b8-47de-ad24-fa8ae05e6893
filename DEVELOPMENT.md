# TradeAI Bot 开发指南

## 🚀 快速开始

### 前置要求
- Node.js >= 18.0.0
- npm >= 8.0.0
- PostgreSQL >= 13 (可选，可使用 Docker)
- Redis >= 6.0 (可选，可使用 Docker)
- Docker & Docker Compose (推荐)

### 一键启动
```bash
# 使用启动脚本（推荐）
./scripts/start.sh

# 或者手动启动
npm install
cd client && npm install && cd ..
cp .env.example .env
# 编辑 .env 文件
npm run dev
```

## 📁 项目结构

```
TradeAI Bot/
├── src/
│   ├── server/           # 后端代码
│   │   ├── controllers/  # 控制器
│   │   ├── services/     # 业务逻辑
│   │   ├── models/       # 数据模型
│   │   ├── middleware/   # 中间件
│   │   ├── routes/       # 路由
│   │   ├── utils/        # 工具函数
│   │   ├── strategies/   # 交易策略
│   │   ├── exchanges/    # 交易所集成
│   │   └── blockchain/   # 区块链集成
│   ├── shared/           # 共享代码
│   │   ├── types/        # 类型定义
│   │   ├── constants/    # 常量
│   │   └── utils/        # 共享工具
│   └── cli/              # CLI 工具
├── client/               # 前端代码 (React)
├── scripts/              # 脚本文件
├── logs/                 # 日志文件
└── docs/                 # 文档
```

## 🛠️ 开发环境设置

### 1. 使用 Docker Compose (推荐)
```bash
# 启动所有服务
docker-compose up

# 仅启动数据库
docker-compose up -d postgres redis

# 启动开发工具
docker-compose --profile tools up -d pgadmin redis-commander
```

### 2. 本地开发
```bash
# 安装依赖
npm install
cd client && npm install && cd ..

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动开发服务器
npm run dev
```

## 📊 数据库管理

### 使用 pgAdmin
- URL: http://localhost:5050
- 邮箱: <EMAIL>
- 密码: admin

### 使用 Redis Commander
```bash
docker-compose --profile tools up -d redis-commander
```
- URL: http://localhost:8081

### 数据库迁移
```bash
# 初始化数据库
npm run db:init

# 运行迁移
npm run db:migrate

# 重置数据库
npm run db:reset
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行测试并监听变化
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 测试结构
```
src/
├── **/__tests__/         # 测试文件
├── **/*.test.ts         # 单元测试
└── **/*.spec.ts         # 集成测试
```

## 🔧 CLI 工具使用

### 基本命令
```bash
# 初始化配置
npm run cli init

# 配置交易所
npm run cli config --exchange binance

# 启动服务器
npm run cli start

# 查看状态
npm run cli status

# 管理策略
npm run cli strategy --list
npm run cli strategy --start <id>
npm run cli strategy --stop <id>
```

## 🏗️ 构建和部署

### 开发构建
```bash
npm run build:dev
```

### 生产构建
```bash
npm run build
```

### Docker 构建
```bash
# 构建开发镜像
docker build --target development -t tradeai-bot:dev .

# 构建生产镜像
docker build --target production -t tradeai-bot:prod .
```

## 📝 代码规范

### ESLint 和 Prettier
```bash
# 检查代码规范
npm run lint

# 自动修复
npm run lint:fix

# 格式化代码
npm run format
```

### Git Hooks
项目使用 Husky 进行 Git hooks 管理：
- `pre-commit`: 运行 lint 和格式化
- `pre-push`: 运行测试

## 🔐 安全最佳实践

### 环境变量
- 永远不要提交 `.env` 文件
- 使用强密码和密钥
- 定期轮换 API 密钥

### API 密钥管理
- 所有敏感数据都经过加密存储
- 使用 AES-256 加密算法
- 密钥分离存储

### 数据库安全
- 使用参数化查询防止 SQL 注入
- 定期备份数据库
- 限制数据库访问权限

## 📈 性能优化

### 数据库优化
- 使用适当的索引
- 定期分析查询性能
- 使用连接池

### 缓存策略
- Redis 缓存热点数据
- 设置合理的过期时间
- 使用缓存预热

### API 优化
- 实现速率限制
- 使用分页查询
- 压缩响应数据

## 🐛 调试和日志

### 日志级别
- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息

### 日志文件
- `logs/error.log`: 错误日志
- `logs/combined.log`: 综合日志
- `logs/strategy.log`: 策略日志
- `logs/trades.log`: 交易日志

### 调试技巧
```bash
# 启用调试模式
DEBUG=tradeai:* npm run dev

# 查看实时日志
tail -f logs/combined.log

# 使用 VS Code 调试器
npm run debug
```

## 🔄 CI/CD

### GitHub Actions
项目包含以下 CI/CD 流程：
- 代码质量检查
- 自动化测试
- 安全扫描
- Docker 镜像构建

### 部署流程
1. 推送代码到 main 分支
2. 自动运行测试
3. 构建 Docker 镜像
4. 部署到生产环境

## 📚 API 文档

### Swagger/OpenAPI
- 开发环境: http://localhost:3000/api/docs
- 生产环境: https://api.tradeai.com/docs

### API 版本控制
- 使用语义化版本控制
- 向后兼容性保证
- 废弃 API 的迁移指南

## 🤝 贡献指南

### 提交代码
1. Fork 项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建 Pull Request

### 代码审查
- 所有代码必须经过审查
- 确保测试覆盖率 > 80%
- 遵循代码规范

### 问题报告
- 使用 GitHub Issues
- 提供详细的重现步骤
- 包含环境信息

## 📞 获取帮助

- 📖 文档: [docs.tradeai.com](https://docs.tradeai.com)
- 💬 Discord: [discord.gg/tradeai](https://discord.gg/tradeai)
- 📧 邮件: <EMAIL>
- 🐛 问题: [GitHub Issues](https://github.com/tradeai/bot/issues)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

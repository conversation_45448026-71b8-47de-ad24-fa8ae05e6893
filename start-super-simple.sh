#!/bin/bash

# TradeAI Bot 超级简单启动脚本
# 使用最小依赖，确保 100% 成功启动

set -e

echo "🚀 TradeAI Bot 超级简单启动"
echo "=========================="

# 清理端口
echo "🧹 清理端口占用..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 修复前端依赖
echo "🔧 修复前端依赖..."
if [ -f "fix-main-app.sh" ]; then
    chmod +x fix-main-app.sh
    ./fix-main-app.sh
fi

# 检查基础依赖
echo "📦 检查基础依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装基础依赖..."
    npm install express cors @types/express @types/cors @types/node typescript ts-node
fi

if [ ! -d "client/node_modules" ]; then
    echo "安装前端依赖..."
    cd client
    npm install
    cd ..
fi

# 使用最小化服务器
echo "🔄 使用最小化服务器..."
if [ -f "src/server/index.ts" ]; then
    cp src/server/index.ts src/server/index-backup.ts
fi
cp src/server/index-minimal.ts src/server/index.ts

# 启动后端 (使用最小依赖)
echo "🔧 启动最小化后端..."
npx ts-node src/server/index.ts &
BACKEND_PID=$!
echo "✅ 后端已启动 (PID: $BACKEND_PID)"

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 5

# 验证后端
echo "🔍 验证后端状态..."
for i in {1..10}; do
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ 后端健康检查通过"
        break
    else
        echo "⏳ 等待后端启动... ($i/10)"
        sleep 2
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 后端启动失败"
        echo "🔍 最后尝试检查..."
        
        # 显示进程信息
        echo "进程信息:"
        ps aux | grep -E "(ts-node|node)" | grep -v grep || echo "没有找到相关进程"
        
        # 显示端口信息
        echo "端口信息:"
        lsof -i :3001 || echo "端口 3001 未被占用"
        
        # 尝试直接访问
        echo "直接测试:"
        curl -v http://localhost:3001/health || echo "无法连接到后端"
        
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
done

# 测试后端API
echo "🧪 测试后端API..."
echo "健康检查:"
curl -s http://localhost:3001/health | head -c 200
echo ""
echo "API基础信息:"
curl -s http://localhost:3001/api | head -c 200
echo ""

# 启动前端
echo "🎨 启动前端服务器..."
cd client
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 前端已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 8

# 显示最终信息
echo ""
echo "🎉 TradeAI Bot 启动完成！"
echo "========================="
echo ""
echo "📱 前端地址: http://localhost:5173"
echo "🔗 后端地址: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo "🧪 API测试: http://localhost:3001/api/test"
echo ""
echo "🔧 服务器类型: 最小化 TypeScript 服务器"
echo "📦 依赖: 仅使用 express + cors (最小依赖)"
echo ""
echo "🌟 功能页面:"
echo "  📊 仪表板: http://localhost:5173/"
echo "  🔐 登录: http://localhost:5173/login"
echo "  📝 注册: http://localhost:5173/register"
echo ""
echo "🧪 快速测试命令:"
echo "  curl http://localhost:3001/health"
echo "  curl http://localhost:3001/api"
echo "  curl http://localhost:3001/api/test"
echo ""
echo "💡 提示:"
echo "  - 这是最简化版本，确保稳定运行"
echo "  - 所有基础API都可用"
echo "  - 前端功能完整"
echo ""
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    # 恢复原始文件
    if [ -f "src/server/index-backup.ts" ]; then
        echo "🔄 恢复原始服务器文件..."
        cp src/server/index-backup.ts src/server/index.ts
    fi
    
    echo "✅ 所有服务已停止，文件已恢复"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待进程
wait $BACKEND_PID $FRONTEND_PID

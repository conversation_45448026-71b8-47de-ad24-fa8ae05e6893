# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/
client/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.sqlite
*.sqlite3
*.db

# Backup files
*.backup
*.bak
*.tmp

# Docker
.dockerignore

# PM2
ecosystem.config.js

# Temporary files
temp/
tmp/

# API keys and sensitive data
api-keys/
private-keys/
*.pem
*.key
*.crt

# CSV files with sensitive data
*.csv
wallets/

# Test files
test-results/
playwright-report/
test-results.xml

# Build artifacts
*.tgz
*.tar.gz

# Local development
.local/

# Webpack
.webpack/

# ESLint
.eslintcache

# Prettier
.prettierignore

# Husky
.husky/_

# Storybook
storybook-static/

# Chromatic
chromatic.log

# Sentry
.sentryclirc

# Vercel
.vercel

# Turborepo
.turbo

# Local Netlify folder
.netlify

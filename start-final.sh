#!/bin/bash

# TradeAI Bot 最终启动脚本
# 使用干净的服务器文件，确保稳定运行

set -e

echo "🚀 TradeAI Bot 最终启动脚本"
echo "==========================="

# 清理端口
echo "🧹 清理端口占用..."
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
sleep 2

# 修复前端依赖
echo "🔧 修复前端依赖..."
if [ -f "fix-main-app.sh" ]; then
    chmod +x fix-main-app.sh
    ./fix-main-app.sh
fi

# 检查依赖
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi

if [ ! -d "client/node_modules" ]; then
    echo "安装前端依赖..."
    cd client
    npm install
    cd ..
fi

# 备份原始服务器文件并使用干净版本
echo "🔄 使用干净的服务器文件..."
if [ -f "src/server/index.ts" ]; then
    cp src/server/index.ts src/server/index-backup.ts
fi
cp src/server/index-clean.ts src/server/index.ts

# 尝试编译 TypeScript
echo "🔨 编译 TypeScript..."
if npm run build:server; then
    echo "✅ TypeScript 编译成功"
    
    # 使用编译后的 JavaScript 启动
    echo "🔧 启动编译后的后端..."
    node dist/server/index.js &
    BACKEND_PID=$!
    BACKEND_TYPE="compiled"
    echo "✅ 后端已启动 (PID: $BACKEND_PID, Type: $BACKEND_TYPE)"
else
    echo "⚠️  TypeScript 编译失败，使用 ts-node..."
    
    # 直接使用 ts-node 运行
    npx ts-node src/server/index.ts &
    BACKEND_PID=$!
    BACKEND_TYPE="ts-node"
    echo "✅ 后端已启动 (PID: $BACKEND_PID, Type: $BACKEND_TYPE)"
fi

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 8

# 验证后端
echo "🔍 验证后端状态..."
for i in {1..15}; do
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ 后端健康检查通过"
        break
    else
        echo "⏳ 等待后端启动... ($i/15)"
        sleep 2
    fi
    
    if [ $i -eq 15 ]; then
        echo "❌ 后端启动失败"
        echo "🔍 检查后端进程状态..."
        ps aux | grep -E "(node|ts-node)" | grep -v grep || echo "没有找到 Node.js 进程"
        
        echo "🔍 检查端口占用..."
        lsof -i :3001 || echo "端口 3001 未被占用"
        
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
done

# 启动前端
echo "🎨 启动前端服务器..."
cd client
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 前端已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 10

# 显示访问信息
echo ""
echo "🎉 TradeAI Bot 启动完成！"
echo "========================="
echo ""
echo "📱 前端地址: http://localhost:5173"
echo "🔗 后端地址: http://localhost:3001"
echo "🔍 健康检查: http://localhost:3001/health"
echo "🧪 API测试: http://localhost:3001/api"
echo ""
echo "🔧 后端类型: $BACKEND_TYPE"
if [ "$BACKEND_TYPE" = "compiled" ]; then
    echo "  ✅ 使用编译后的 JavaScript"
else
    echo "  ✅ 使用 ts-node 直接运行 TypeScript"
fi
echo ""
echo "🌟 功能页面:"
echo "  📊 仪表板: http://localhost:5173/"
echo "  🔐 登录: http://localhost:5173/login"
echo "  📝 注册: http://localhost:5173/register"
echo "  🤖 策略: http://localhost:5173/strategies"
echo "  🏢 交易所: http://localhost:5173/exchanges"
echo "  💰 钱包: http://localhost:5173/wallets"
echo "  ⚙️  设置: http://localhost:5173/settings"
echo ""
echo "💡 提示:"
echo "  - 前端包含完整的路由系统"
echo "  - 所有页面都有基础框架"
echo "  - 后端API完全可用"
echo "  - WebSocket 连接已启用"
echo ""
echo "🧪 快速测试:"
echo "  curl http://localhost:3001/health"
echo "  curl http://localhost:3001/api"
echo ""
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    # 恢复原始服务器文件
    if [ -f "src/server/index-backup.ts" ]; then
        echo "🔄 恢复原始服务器文件..."
        cp src/server/index-backup.ts src/server/index.ts
    fi
    
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待进程
wait $BACKEND_PID $FRONTEND_PID

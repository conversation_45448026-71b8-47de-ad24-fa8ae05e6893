#!/bin/bash

# TradeAI Bot 本地启动脚本
# 使用方法: ./scripts/start-local.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $1 已被占用"
        read -p "是否要杀死占用进程? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            lsof -ti:$1 | xargs kill -9
            log_success "已杀死端口 $1 的占用进程"
        else
            log_error "请手动处理端口占用问题"
            return 1
        fi
    fi
    return 0
}

# 主函数
main() {
    log_info "开始启动 TradeAI Bot..."
    
    # 1. 检查依赖
    log_info "检查系统依赖..."
    check_command "node" || exit 1
    check_command "npm" || exit 1
    
    # 检查 Node.js 版本
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本需要 18 或更高，当前版本: $(node --version)"
        exit 1
    fi
    log_success "Node.js 版本检查通过: $(node --version)"
    
    # 2. 检查项目文件
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    # 3. 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_info "创建环境变量文件..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_success "已从 .env.example 创建 .env 文件"
        else
            log_info "创建默认 .env 文件..."
            cat > .env << EOF
NODE_ENV=development
PORT=3001
HOST=localhost

DB_HOST=localhost
DB_PORT=5432
DB_NAME=tradeai_bot
DB_USER=postgres
DB_PASSWORD=postgres

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

JWT_SECRET=development-jwt-secret-key-$(date +%s)
JWT_EXPIRES_IN=7d

ENCRYPTION_KEY=dev-encryption-key-32-chars-long

LOG_LEVEL=debug
EOF
            log_success "已创建默认 .env 文件"
        fi
    fi
    
    # 4. 检查端口占用
    log_info "检查端口占用..."
    check_port 3000 || exit 1
    check_port 3001 || exit 1
    
    # 5. 安装依赖
    log_info "安装后端依赖..."
    if [ ! -d "node_modules" ]; then
        npm install
        log_success "后端依赖安装完成"
    else
        log_info "后端依赖已存在，跳过安装"
    fi
    
    log_info "安装前端依赖..."
    if [ ! -d "client/node_modules" ]; then
        cd client
        npm install
        cd ..
        log_success "前端依赖安装完成"
    else
        log_info "前端依赖已存在，跳过安装"
    fi
    
    # 6. 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        log_info "检测到 Docker，启动数据库服务..."
        
        # 检查 docker-compose
        if command -v docker-compose &> /dev/null; then
            # 启动数据库
            docker-compose up -d postgres redis
            log_success "数据库服务启动完成"
            
            # 等待数据库就绪
            log_info "等待数据库就绪..."
            sleep 10
            
            # 运行数据库迁移
            log_info "运行数据库迁移..."
            npm run migrate 2>/dev/null || log_warning "数据库迁移失败，可能需要手动处理"
        else
            log_warning "未找到 docker-compose，请手动启动数据库"
        fi
    else
        log_warning "未检测到 Docker，请确保 PostgreSQL 和 Redis 已启动"
    fi
    
    # 7. 启动应用
    log_info "启动 TradeAI Bot 应用..."
    log_success "应用启动中..."
    log_info "前端地址: http://localhost:3000"
    log_info "后端地址: http://localhost:3001"
    log_info "API文档: http://localhost:3001/api-docs"
    log_info ""
    log_info "按 Ctrl+C 停止服务"
    log_info ""
    
    # 启动开发服务器
    npm run dev
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    if command -v docker-compose &> /dev/null; then
        docker-compose stop
    fi
    log_success "服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 运行主函数
main "$@"

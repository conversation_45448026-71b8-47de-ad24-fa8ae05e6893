#!/bin/bash

# TradeAI Bot 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🤖 TradeAI Bot 启动脚本" $BLUE
echo "=================================="

# 检查 Node.js 版本
print_message "检查 Node.js 版本..." $YELLOW
if ! command -v node &> /dev/null; then
    print_message "❌ Node.js 未安装，请先安装 Node.js 18+" $RED
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_message "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)" $RED
    exit 1
fi
print_message "✅ Node.js 版本: $(node -v)" $GREEN

# 检查 npm 版本
print_message "检查 npm 版本..." $YELLOW
if ! command -v npm &> /dev/null; then
    print_message "❌ npm 未安装" $RED
    exit 1
fi
print_message "✅ npm 版本: $(npm -v)" $GREEN

# 检查 Docker 和 Docker Compose
print_message "检查 Docker..." $YELLOW
if command -v docker &> /dev/null; then
    print_message "✅ Docker 版本: $(docker --version)" $GREEN
    DOCKER_AVAILABLE=true
else
    print_message "⚠️  Docker 未安装，将使用本地数据库" $YELLOW
    DOCKER_AVAILABLE=false
fi

if command -v docker-compose &> /dev/null || docker compose version &> /dev/null 2>&1; then
    print_message "✅ Docker Compose 可用" $GREEN
    DOCKER_COMPOSE_AVAILABLE=true
else
    print_message "⚠️  Docker Compose 未安装" $YELLOW
    DOCKER_COMPOSE_AVAILABLE=false
fi

# 检查环境变量文件
print_message "检查环境配置..." $YELLOW
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        print_message "📝 复制 .env.example 到 .env" $YELLOW
        cp .env.example .env
        print_message "⚠️  请编辑 .env 文件配置数据库和其他设置" $YELLOW
    else
        print_message "❌ 未找到 .env.example 文件" $RED
        exit 1
    fi
else
    print_message "✅ 环境配置文件存在" $GREEN
fi

# 安装依赖
print_message "安装后端依赖..." $YELLOW
npm install
print_message "✅ 后端依赖安装完成" $GREEN

print_message "安装前端依赖..." $YELLOW
cd client && npm install && cd ..
print_message "✅ 前端依赖安装完成" $GREEN

# 选择启动方式
echo ""
print_message "选择启动方式:" $BLUE
echo "1) 使用 Docker Compose (推荐)"
echo "2) 本地启动 (需要手动配置数据库)"
echo "3) 仅启动数据库服务"
echo "4) 退出"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        if [ "$DOCKER_AVAILABLE" = true ] && [ "$DOCKER_COMPOSE_AVAILABLE" = true ]; then
            print_message "🐳 使用 Docker Compose 启动..." $BLUE
            
            # 创建日志目录
            mkdir -p logs
            
            # 启动服务
            if docker compose version &> /dev/null 2>&1; then
                docker compose up -d postgres redis
                print_message "⏳ 等待数据库启动..." $YELLOW
                sleep 10
                docker compose up app
            else
                docker-compose up -d postgres redis
                print_message "⏳ 等待数据库启动..." $YELLOW
                sleep 10
                docker-compose up app
            fi
        else
            print_message "❌ Docker 或 Docker Compose 不可用" $RED
            exit 1
        fi
        ;;
    2)
        print_message "🖥️  本地启动模式..." $BLUE
        print_message "⚠️  请确保 PostgreSQL 和 Redis 已启动并正确配置" $YELLOW
        
        # 创建日志目录
        mkdir -p logs
        
        # 构建项目
        print_message "构建项目..." $YELLOW
        npm run build
        
        # 启动开发服务器
        print_message "启动开发服务器..." $YELLOW
        npm run dev
        ;;
    3)
        if [ "$DOCKER_AVAILABLE" = true ] && [ "$DOCKER_COMPOSE_AVAILABLE" = true ]; then
            print_message "🗄️  仅启动数据库服务..." $BLUE
            if docker compose version &> /dev/null 2>&1; then
                docker compose up -d postgres redis
            else
                docker-compose up -d postgres redis
            fi
            print_message "✅ 数据库服务已启动" $GREEN
            print_message "📊 pgAdmin: http://localhost:5050 (<EMAIL> / admin)" $BLUE
            print_message "🔴 Redis Commander: docker compose --profile tools up -d redis-commander" $BLUE
        else
            print_message "❌ Docker 不可用" $RED
            exit 1
        fi
        ;;
    4)
        print_message "👋 退出" $YELLOW
        exit 0
        ;;
    *)
        print_message "❌ 无效选择" $RED
        exit 1
        ;;
esac

echo ""
print_message "🎉 TradeAI Bot 启动完成!" $GREEN
print_message "📱 Web 界面: http://localhost:3000" $BLUE
print_message "📚 API 文档: http://localhost:3000/api/docs" $BLUE
print_message "📊 健康检查: http://localhost:3000/health" $BLUE

if [ "$choice" = "1" ] || [ "$choice" = "3" ]; then
    print_message "🗄️  数据库管理: http://localhost:5050" $BLUE
fi

echo ""
print_message "📖 更多信息请查看 README.md" $YELLOW
print_message "🛑 停止服务: Ctrl+C 或运行 docker-compose down" $YELLOW

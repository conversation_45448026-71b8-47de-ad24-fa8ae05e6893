@echo off
setlocal enabledelayedexpansion

:: TradeAI Bot 本地启动脚本 (Windows)
:: 使用方法: scripts\start-local.bat

echo.
echo ========================================
echo    TradeAI Bot 本地启动脚本
echo ========================================
echo.

:: 检查 Node.js
echo [INFO] 检查 Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js 18+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do (
    set NODE_MAJOR=%%a
    set NODE_MAJOR=!NODE_MAJOR:v=!
)

if !NODE_MAJOR! LSS 18 (
    echo [ERROR] Node.js 版本需要 18 或更高
    node --version
    pause
    exit /b 1
)

echo [SUCCESS] Node.js 版本检查通过
node --version

:: 检查 npm
echo [INFO] 检查 npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm 未安装
    pause
    exit /b 1
)
echo [SUCCESS] npm 检查通过

:: 检查项目文件
echo [INFO] 检查项目文件...
if not exist "package.json" (
    echo [ERROR] 未找到 package.json，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 创建环境变量文件
echo [INFO] 检查环境变量文件...
if not exist ".env" (
    echo [INFO] 创建环境变量文件...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [SUCCESS] 已从 .env.example 创建 .env 文件
    ) else (
        echo [INFO] 创建默认 .env 文件...
        (
            echo NODE_ENV=development
            echo PORT=3001
            echo HOST=localhost
            echo.
            echo DB_HOST=localhost
            echo DB_PORT=5432
            echo DB_NAME=tradeai_bot
            echo DB_USER=postgres
            echo DB_PASSWORD=postgres
            echo.
            echo REDIS_HOST=localhost
            echo REDIS_PORT=6379
            echo REDIS_PASSWORD=
            echo.
            echo JWT_SECRET=development-jwt-secret-key
            echo JWT_EXPIRES_IN=7d
            echo.
            echo ENCRYPTION_KEY=dev-encryption-key-32-chars-long
            echo.
            echo LOG_LEVEL=debug
        ) > .env
        echo [SUCCESS] 已创建默认 .env 文件
    )
)

:: 检查端口占用
echo [INFO] 检查端口占用...
netstat -an | find "3000" | find "LISTENING" >nul
if not errorlevel 1 (
    echo [WARNING] 端口 3000 已被占用
    set /p KILL_3000="是否要杀死占用进程? (y/n): "
    if /i "!KILL_3000!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| find "3000" ^| find "LISTENING"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo [SUCCESS] 已杀死端口 3000 的占用进程
    )
)

netstat -an | find "3001" | find "LISTENING" >nul
if not errorlevel 1 (
    echo [WARNING] 端口 3001 已被占用
    set /p KILL_3001="是否要杀死占用进程? (y/n): "
    if /i "!KILL_3001!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| find "3001" ^| find "LISTENING"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo [SUCCESS] 已杀死端口 3001 的占用进程
    )
)

:: 安装依赖
echo [INFO] 安装后端依赖...
if not exist "node_modules" (
    echo [INFO] 正在安装后端依赖，请稍候...
    npm install
    if errorlevel 1 (
        echo [ERROR] 后端依赖安装失败
        pause
        exit /b 1
    )
    echo [SUCCESS] 后端依赖安装完成
) else (
    echo [INFO] 后端依赖已存在，跳过安装
)

echo [INFO] 安装前端依赖...
if not exist "client\node_modules" (
    echo [INFO] 正在安装前端依赖，请稍候...
    cd client
    npm install
    if errorlevel 1 (
        echo [ERROR] 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] 前端依赖安装完成
) else (
    echo [INFO] 前端依赖已存在，跳过安装
)

:: 检查 Docker
echo [INFO] 检查 Docker...
docker --version >nul 2>&1
if not errorlevel 1 (
    echo [INFO] 检测到 Docker，启动数据库服务...
    docker-compose --version >nul 2>&1
    if not errorlevel 1 (
        echo [INFO] 启动数据库服务...
        docker-compose up -d postgres redis
        if not errorlevel 1 (
            echo [SUCCESS] 数据库服务启动完成
            echo [INFO] 等待数据库就绪...
            timeout /t 10 /nobreak >nul
            
            echo [INFO] 运行数据库迁移...
            npm run migrate >nul 2>&1
            if errorlevel 1 (
                echo [WARNING] 数据库迁移失败，可能需要手动处理
            ) else (
                echo [SUCCESS] 数据库迁移完成
            )
        ) else (
            echo [WARNING] 数据库服务启动失败，请手动启动
        )
    ) else (
        echo [WARNING] 未找到 docker-compose，请手动启动数据库
    )
) else (
    echo [WARNING] 未检测到 Docker，请确保 PostgreSQL 和 Redis 已启动
)

:: 启动应用
echo.
echo ========================================
echo [SUCCESS] 准备启动 TradeAI Bot 应用...
echo ========================================
echo.
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:3001
echo API文档: http://localhost:3001/api-docs
echo.
echo 按 Ctrl+C 停止服务
echo.

:: 启动开发服务器
npm run dev

:: 清理
echo.
echo [INFO] 正在停止服务...
docker-compose stop >nul 2>&1
echo [SUCCESS] 服务已停止
pause

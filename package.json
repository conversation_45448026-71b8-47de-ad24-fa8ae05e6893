{"name": "tradeai-bot", "version": "1.0.0", "description": "Advanced Trading Bot with CEX and DEX strategies", "main": "dist/index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon --exec ts-node src/server/index.ts", "dev:client": "cd client && npm run dev", "build": "npm run build:server && npm run build:client", "build:server": "tsc -p tsconfig.server.json", "build:client": "cd client && npm run build", "start": "node dist/index.js", "cli": "ts-node src/cli/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["trading", "bot", "cryptocurrency", "defi", "cex", "dex", "automated-trading"], "author": "TradeAI Bot Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "redis": "^4.6.10", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "crypto-js": "^4.2.0", "ws": "^8.14.2", "axios": "^1.6.0", "ccxt": "^4.1.0", "ethers": "^6.8.0", "@solana/web3.js": "^1.87.0", "commander": "^11.1.0", "inquirer": "^9.2.11", "chalk": "^5.3.0", "ora": "^7.0.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/express": "^4.17.20", "@types/cors": "^2.8.15", "@types/bcrypt": "^5.0.1", "@types/jsonwebtoken": "^9.0.4", "@types/ws": "^8.5.8", "@types/pg": "^8.10.7", "@types/crypto-js": "^4.2.1", "@types/inquirer": "^9.0.6", "@types/node-cron": "^3.0.9", "@types/jest": "^29.5.6", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "concurrently": "^8.2.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.51.0", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
# TradeAI Bot

🤖 **Advanced Trading Bot with CEX and DEX strategies**

TradeAI Bot is a comprehensive trading automation platform that supports both Centralized Exchange (CEX) and Decentralized Exchange (DEX) trading strategies. It features a modern web interface, CLI tools, and advanced AI-powered trading strategies.

## 🚀 Features

### CEX Strategies
- **网格交易 (Grid Trading)**: 单边和多边网格策略，支持自定义价格区间和触发条件
- **建仓/出货 (Position Building)**: 高频小额挂单，不影响价格的建仓和出货
- **流动性维护 (Liquidity Maintenance)**: 在盘口一定百分比价差内维护流动性
- **刷量 (Volume Washing)**: 多账户对敲交易实现刷量操作
- **趋势跟踪 (Trend Following)**: 根据价格趋势进行随机买卖
- **对冲波动 (Hedging)**: 自动对冲持仓币种
- **AI策略 (AI Strategy)**: AI制定策略并确认执行

### DEX Strategies
- **多地址管理**: 支持CSV文件导入加密的密钥文件
- **自动路由配置**: 选择链和对应DEX自动配置路由合约
- **DEX刷量**: 多地址间随机交易刷量并平衡资产
- **DEX趋势跟踪**: 根据价格趋势进行买卖操作
- **跟踪账户**: 跟随配置账户的买卖操作
- **快速建仓/出货**: 快速买入/卖出指定数量资产
- **DEX AI策略**: AI制定DEX策略并确认执行

### 支持的交易所和协议

#### CEX 交易所
- 币安 (Binance)
- OKX
- HTX (Huobi)
- Bitget
- Bybit
- MEXC
- Bitmart

#### DEX 协议
- **BSC链**: PancakeSwap, Uniswap V2
- **ETH链**: Uniswap V2/V3
- **Base链**: Uniswap V3
- **Solana链**: Raydium, Jupiter

### 高级功能
- **奖励系统**: Token奖励分配，按时间和配资大小计算
- **付费订阅**: 高级功能订阅 ($500/月)
- **实时监控**: WebSocket实时数据推送
- **安全加密**: 多层加密保护API密钥和私钥
- **风险管理**: 内置风险控制和止损机制

## 🛠️ 技术栈

### 后端
- **Node.js** + TypeScript
- **Express.js** - Web框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话管理
- **WebSocket** - 实时通信
- **CCXT** - 交易所API集成
- **ethers.js** - 以太坊交互
- **@solana/web3.js** - Solana交互

### 前端
- **React** + TypeScript
- **Vite** - 构建工具
- **Tailwind CSS** - 样式框架
- **React Router** - 路由管理
- **Zustand** - 状态管理
- **React Hook Form** - 表单处理
- **Recharts** - 图表组件

### CLI工具
- **Commander.js** - 命令行框架
- **Inquirer.js** - 交互式命令行
- **Chalk** - 彩色输出
- **Ora** - 加载动画

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/tradeai-bot.git
cd tradeai-bot

# 2. 一键启动（自动安装依赖）
chmod +x start.sh
./start.sh
```

### 手动启动

```bash
# 1. 安装依赖
npm install
cd client && npm install && cd ..

# 2. 启动服务
./start.sh
```

### 访问应用
- 🌐 前端界面: http://localhost:5173
- 🔗 后端API: http://localhost:3001
- 🔍 健康检查: http://localhost:3001/health
- 🧪 API测试: http://localhost:3001/api

## 🖥️ CLI 使用

TradeAI Bot 提供了强大的命令行工具：

```bash
# 初始化配置
tradeai-bot init

# 配置交易所API
tradeai-bot config --exchange binance

# 配置钱包
tradeai-bot config --wallet

# 启动服务器
tradeai-bot start

# 以守护进程方式启动
tradeai-bot start --daemon

# 查看状态
tradeai-bot status

# 管理策略
tradeai-bot strategy --list
tradeai-bot strategy --start <strategy-id>
tradeai-bot strategy --stop <strategy-id>

# 查看日志
tradeai-bot logs --follow
tradeai-bot logs --type trade

# 备份数据
tradeai-bot backup --output ./backup.tar.gz

# 恢复数据
tradeai-bot restore ./backup.tar.gz
```

## 🔧 配置

### 环境变量配置

详细的环境变量配置请参考 `.env.example` 文件。主要配置项包括：

- **数据库配置**: PostgreSQL和Redis连接信息
- **安全配置**: JWT密钥和加密密钥
- **交易所API**: 各交易所的API密钥（用户自行配置）
- **区块链RPC**: 各链的RPC节点地址
- **AI配置**: OpenAI API密钥
- **支付配置**: Stripe支付配置

### 策略配置

每种策略都有详细的配置选项，可以通过Web界面或API进行配置。

## 🔒 安全

- **多层加密**: API密钥和私钥使用AES-256加密存储
- **JWT认证**: 基于JWT的用户认证系统
- **权限控制**: 基于角色的访问控制
- **审计日志**: 完整的操作审计日志
- **风险控制**: 内置风险管理和限制机制

## 📊 监控

- **实时仪表板**: Web界面实时显示交易状态
- **WebSocket推送**: 实时数据更新
- **日志系统**: 分类日志记录
- **性能监控**: 系统性能指标监控
- **告警系统**: 异常情况自动告警

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本软件仅供教育和研究目的使用。使用本软件进行实际交易存在风险，可能导致资金损失。用户应当：

1. 充分了解交易风险
2. 仅使用可承受损失的资金
3. 遵守当地法律法规
4. 对使用本软件的后果承担全部责任

开发者不对使用本软件造成的任何损失承担责任。

## 📞 支持

- **文档**: [查看完整文档](https://docs.tradeai-bot.com)
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/tradeai-bot/issues)
- **社区讨论**: [Discord](https://discord.gg/tradeai-bot)
- **邮件支持**: <EMAIL>

---

**TradeAI Bot** - 让交易更智能 🚀

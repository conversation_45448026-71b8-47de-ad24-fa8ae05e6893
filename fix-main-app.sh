#!/bin/bash

# 修复主程序 App.tsx 的依赖问题

echo "🔧 修复主程序依赖问题..."

cd client/src

# 1. 创建缺失的页面组件
echo "📝 创建缺失的页面组件..."

# 创建 pages 目录
mkdir -p pages/Auth pages/Dashboard pages/Strategies pages/Exchanges pages/Wallets pages/Settings

# 创建简化的 Dashboard 页面
cat > pages/Dashboard/index.tsx << 'EOF'
import React from 'react';
import SimpleDashboard from '../../components/SimpleDashboard';

const Dashboard: React.FC = () => {
  return <SimpleDashboard />;
};

export default Dashboard;
EOF

# 创建简化的 Login 页面
cat > pages/Auth/Login.tsx << 'EOF'
import React, { useState } from 'react';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('登录功能开发中...');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            登录 TradeAI Bot
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="邮箱地址"
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="密码"
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              登录
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
EOF

# 创建简化的 Register 页面
cat > pages/Auth/Register.tsx << 'EOF'
import React, { useState } from 'react';

const Register: React.FC = () => {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('注册功能开发中...');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            注册 TradeAI Bot
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="邮箱地址"
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="用户名"
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="密码"
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              注册
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;
EOF

# 创建其他页面的占位符
for page in Strategies Exchanges Wallets Settings; do
  cat > pages/${page}/index.tsx << EOF
import React from 'react';

const ${page}: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold text-gray-900 mb-4">${page}</h1>
      <div className="bg-white rounded-lg shadow-md p-6">
        <p className="text-gray-600">${page} 功能开发中...</p>
        <p className="text-sm text-gray-500 mt-2">
          这个模块将包含 ${page} 相关的所有功能。
        </p>
      </div>
    </div>
  );
};

export default ${page};
EOF
done

# 2. 创建简化的 Layout 组件
echo "🏗️ 创建 Layout 组件..."
mkdir -p components/Layout

cat > components/Layout/index.tsx << 'EOF'
import React from 'react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">TradeAI Bot</h1>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/" className="text-gray-500 hover:text-gray-700">仪表板</a>
              <a href="/strategies" className="text-gray-500 hover:text-gray-700">策略</a>
              <a href="/exchanges" className="text-gray-500 hover:text-gray-700">交易所</a>
              <a href="/wallets" className="text-gray-500 hover:text-gray-700">钱包</a>
              <a href="/settings" className="text-gray-500 hover:text-gray-700">设置</a>
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {children}
        </div>
      </main>
    </div>
  );
};

export default Layout;
EOF

# 3. 更新导入路径
echo "🔄 更新导入路径..."

# 创建页面的 index 文件
cat > pages/Dashboard.tsx << 'EOF'
export { default } from './Dashboard';
EOF

cat > pages/Login.tsx << 'EOF'
export { default } from './Auth/Login';
EOF

cat > pages/Register.tsx << 'EOF'
export { default } from './Auth/Register';
EOF

cat > pages/Strategies.tsx << 'EOF'
export { default } from './Strategies';
EOF

cat > pages/Exchanges.tsx << 'EOF'
export { default } from './Exchanges';
EOF

cat > pages/Wallets.tsx << 'EOF'
export { default } from './Wallets';
EOF

cat > pages/Settings.tsx << 'EOF'
export { default } from './Settings';
EOF

cat > components/Layout.tsx << 'EOF'
export { default } from './Layout';
EOF

echo "✅ 所有组件创建完成"

cd ../..
echo "🎉 主程序依赖修复完成！"
